package com.kanzhun.mms.service.process;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.kanzhun.mms.common.MMSConstants;
import com.kanzhun.mms.service.MMSServiceNative;
import com.kanzhun.mms.utils.BLog;
import com.kanzhun.mms.utils.ExceptionUtils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/3/20.
 */

public class MMSReceiver extends BroadcastReceiver {
    private static final String TAG = "MMSReceiver";
    private static final String ACTION = "com.twl.suicide";

    @Override
    public void onReceive(final Context context, Intent intent) {
        if (MMSServiceNative.startMMSService(context)){
            BLog.d(TAG, "MMSReceiver startMMSService");
            ExceptionUtils.reportAction(MMSConstants.STATISTICS_RECEIVE_MMS);
        }

//        if (intent != null && ACTION.equals(intent.getAction())) {
////            ThreadManager.getSubThreadHandler().postDelayed(new Runnable() {
////                @Override
////                public void run() {
////                    Process.killProcess(Process.myPid());
////                    ComponentName componentName = new ComponentName(context.getPackageName(),
////                            "com.kanzhun.mms.service.MMSServiceNative");
////                    Intent intent = new Intent();
////                    intent.setComponent(componentName);
////                    context.stopService(intent);
////                    BLog.d(TAG, ACTION);
////                }
////            }, 50);
//        }
    }
}
