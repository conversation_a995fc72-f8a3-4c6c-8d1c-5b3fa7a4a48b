package com.kanzhun.mms.utils;

import org.eclipse.paho.client.mqttv3.MqttException;

/**
 * Created by y<PERSON><PERSON><PERSON><PERSON> on 16/11/21.
 * 自定义异常，用于异常收集，使用ExceptionUtils.postCatchedException 会减少上报次数
 */

public class TWLException extends MqttException{
    public final static int MMS_INIT = 10000;//初始化异常
    public final static int MMS_FOREGROUD = 10001;//设置Foreground异常

    public final static int MMS_SUB_THRED = 20000;//副线程异常
    public final static int MMS_WATCHER = 20001;//watcher

    public final static int MMS_SERVER = 21000;//mqtt服务异常
    public final static int MMS_SERVER_NET_ERROR = 21001;//网络异常
    public final static int MMS_SERVER_SEND_MESSAGE = 21002;//sendmessage接口异常
    public final static int MMS_SERVER_2_CLIENT_REMOTE = 21003;//往主app发送消息Remote异常
    public final static int MMS_SERVER_2_CLIENT_THROWABLE = 21004;//往主app发送消息其它异常
    public final static int MMS_SERVER_PING_NO_FIND = 21005;//手机上没有ping
    public final static int MMS_SERVER_TCP_2_HTTP = 21006;//tcp切换到http
    public final static int MMS_SERVER_HTTP_RESULT = 21007;//切换到http的效果
    public final static int MMS_SERVER_HTTP_2_TCP = 21008;//http切换到tcp

    public final static int MMS_CLIENT = 22000;
    public final static int MMS_CLIENT_SERVER_DEAD = 22001;//往mms发送，mms处于dead状态
    public final static int MMS_CLIENT_SERVER_OTHER = 22012;//往mms发送，其它异常
    public final static int MMS_CLIENT_START_MMS = 22013;
    public final static int MMS_CLIENT_BIND_MMS = 22014;
    public final static int MMS_CLIENT_SEND_FAIL = 22015;
    public final static int MMS_CLIENT_CHANGETOSINGLETON = 22016;
    public final static int MMS_CLIENT_DIE_TO_MANY = 22017;
    public final static int MMS_CLIENT_START_MMS_TOO_LONG = 22018;

    public final static int MMS_COMMON_ASM_WRITE = 23001;//asm写异常
    public final static int MMS_COMMON_ASM_READ = 23002;//asm读异常

    public final static int MAIN_APP = 30000;//主进程App异常，持续扩展

    public final static int RECYCLER_VIEW_DATA_INSISTENCE_ERROR = 250;

    public TWLException(int reasonCode) {
        super(reasonCode);
    }

    public TWLException(Throwable cause) {
        super(cause);
    }

    public TWLException(int reason, Throwable cause) {
        super(reason, cause);
    }
}
