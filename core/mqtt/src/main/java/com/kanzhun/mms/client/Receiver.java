package com.kanzhun.mms.client;

import static com.kanzhun.mms.common.MMSConstants.ACTION_CONNECT;
import static com.kanzhun.mms.common.MMSConstants.ACTION_DELIVERED;
import static com.kanzhun.mms.common.MMSConstants.ACTION_LOST;
import static com.kanzhun.mms.common.MMSConstants.ACTION_PUSH;
import static com.kanzhun.mms.common.MMSConstants.CONECTION_FAILED;
import static com.kanzhun.mms.common.MMSConstants.CONECTION_IN;
import static com.kanzhun.mms.common.MMSConstants.CONECTION_SUCCESS;
import static com.kanzhun.mms.common.MMSConstants.EXTRA_DATA;
import static com.kanzhun.mms.common.MMSConstants.EXTRA_ID;
import static com.kanzhun.mms.common.MMSConstants.EXTRA_RESULT;
import static com.kanzhun.mms.utils.TWLException.MMS_CLIENT_START_MMS_TOO_LONG;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.RemoteException;

import com.kanzhun.mms.IMMServicePushFilter;
import com.kanzhun.mms.MMSMessage;
import com.kanzhun.mms.ServerResponse;
import com.kanzhun.mms.common.MMSConstants;
import com.kanzhun.mms.common.MMSMessageFactory;
import com.kanzhun.mms.utils.BLog;
import com.kanzhun.mms.utils.ExceptionUtils;
import com.kanzhun.mms.utils.TWLException;

/**
 * Created by yuchaofei on 2017/3/2.
 */

class Receiver extends IMMServicePushFilter.Stub {
    private static final String TAG = "Receiver";
    private IConnectionListener mConnectionListener;
    private IReceiveListener mReceiveListener;
    private IDeliveredListener mDeliveredListener;
    private IKickListener mKickListener;

    public Receiver(Context context) {
        registerReceiver(context);
    }

    private void registerReceiver(Context context){
        IntentFilter filter = new IntentFilter();
        try {
            filter.addAction(MMSConstants.ACTION_DELIVERED);
            filter.addAction(MMSConstants.ACTION_PUSH);
            filter.addAction(MMSConstants.ACTION_CONNECT);
            filter.addAction(MMSConstants.ACTION_LOST);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                int flags = Context.RECEIVER_EXPORTED; // 或 Context.RECEIVER_NOT_EXPORTED
                context.registerReceiver(new BRReceive(),filter, flags);
            } else {
                context.registerReceiver(new BRReceive(),filter);
            }

        } catch (Throwable e) {
            BLog.printErrStackTrace(TAG, e, "registerReceiver");
        }
    }

    @Override
    public byte[] getIdentifyData() throws RemoteException {
        IConnectionListener connectionListener = mConnectionListener;
        if (connectionListener != null) {
            return connectionListener.getIdentifyData();
        } else {
            BLog.e(TAG, "IConnectionListener is null.");
        }
        return null;
    }

    @Override
    public void onConnected(int result) throws RemoteException {
        IConnectionListener connectionListener = mConnectionListener;
        if (connectionListener != null) {
            switch (result) {
                case CONECTION_IN:
                    connectionListener.onConnectionConnecting();
                break ;
                case CONECTION_FAILED:
                    connectionListener.onConnectionFailed();
                    break ;
                case CONECTION_SUCCESS:
                    connectionListener.onConnectionConnected();
                    break ;
                default:
                    BLog.e(TAG, "onConnected unknow Code!");
                    break;
            }
        } else {
            BLog.e(TAG, "IConnectionListener is Null. result = [%b]", result);
        }
    }

    @Override
    public void onPush(MMSMessage message) throws RemoteException {
        BLog.d(TAG, "onPush id = [%d]", message.getId());
        IReceiveListener receiveListener = mReceiveListener;
        if (receiveListener != null) {
            receiveListener.onReceive(message.getData());
        } else {
            BLog.e(TAG, "IReceiveListener is Null.");
        }
    }

    @Override
    public void onDelivered(int id, ServerResponse result) throws RemoteException {
        BLog.d(TAG, "onDelivered() called with: id = [%d], result = [%s]", id, result);
        IDeliveredListener deliveredListener = mDeliveredListener;
        if (deliveredListener != null) {
            deliveredListener.onDelivered(id, result);
        } else {
            BLog.e(TAG, "IDeliveredListener is Null");
        }
    }

    @Override
    public void onConnectionLost(int code) throws RemoteException {
        BLog.d(TAG, "onConnectionLost code = [%d]", code);
        IKickListener kickListener = mKickListener;
        if (kickListener != null
                && code == MMSConstants.CONECTION_CODE_FAILED_AUTHENTICATION) {//如果被踢出
            kickListener.onKick();
        }
        IConnectionListener connectionListener = mConnectionListener;
        if (connectionListener != null) {
            connectionListener.onConnectionDisconnected(code);
        } else {
            BLog.e(TAG, "IConnectionListener is Null");
        }
    }

    public void setConnectionListener(IConnectionListener connectionListener) {
        mConnectionListener = connectionListener;
    }

    public void setReceiveListener(IReceiveListener receiveListener) {
        mReceiveListener = receiveListener;
    }

    public void setDeliveredListener(IDeliveredListener deliveredListener) {
        mDeliveredListener = deliveredListener;
    }

    public void setKickListener(IKickListener kickListener) {
        mKickListener = kickListener;
    }

    private class BRReceive extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            BLog.d(TAG, "onReceive() called with: context = [], intent = [%s]", intent);
            try {
                if (intent != null) {
                    switch (intent.getAction()){
                        case ACTION_DELIVERED:
                            int id = intent.getIntExtra(EXTRA_ID, 0);
                            ServerResponse result = intent.getParcelableExtra(EXTRA_RESULT);
                            onDelivered(id, result);
                            break;
                        case ACTION_PUSH:
                            id = intent.getIntExtra(EXTRA_ID, 0);
                            BLog.d(TAG, "ACTION_PUSH message id = [%d]", id);
                            ExceptionUtils.postCatchedException(new TWLException(MMS_CLIENT_START_MMS_TOO_LONG, new Exception("BRReceive push, Only Statistics!")));
                            onPush(MMSMessageFactory.createMqttMessage(intent.getByteArrayExtra(EXTRA_DATA)));
                            break;
                        case ACTION_CONNECT :
                            int ret = intent.getIntExtra(EXTRA_RESULT, -1);
                            BLog.d(TAG, "ACTION_CONNECT ret = [%d]", ret);
                            onConnected(ret);
                            break;
                        case ACTION_LOST :
                            ret = intent.getIntExtra(EXTRA_RESULT, -1);
                            BLog.d(TAG, "ACTION_LOST ret = [%d]", ret);
                            onConnectionLost(ret);
                            break;
                    }
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }
}
