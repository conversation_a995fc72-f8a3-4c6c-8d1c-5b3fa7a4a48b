package com.kanzhun.mms.client;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;

import com.kanzhun.mms.utils.BLog;

import java.util.WeakHashMap;

/**
 * Created by y<PERSON>aofei on 2017/3/30.
 */

class ForegroundLifecycle implements Application.ActivityLifecycleCallbacks {
    private static final String TAG = "ForegroundLifecycle";
    private short mActivityCount = 0;
    private WeakHashMap<Activity, Integer> mWeakHashMap = new WeakHashMap<>();
    private boolean mForegroundChangeStart;

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        mWeakHashMap.put(activity, 0);
    }

    @Override
    public void onActivityStarted(Activity activity) {
        BLog.d(TAG, "onActivityStarted: %d, %s", mActivityCount, activity);
        if (!mWeakHashMap.containsKey(activity)) {
            mWeakHashMap.put(activity, 0);
        }
        mActivityCount++;
    }

    @Override
    public void onActivityResumed(Activity activity) {
        BLog.d(TAG, "onActivityResumed: %d, %s", mActivityCount, activity);
        if (mForegroundChangeStart) {
            MMSServiceSDK.get().setForeground(true);
        }
    }

    @Override
    public void onActivityPaused(Activity activity) {

    }

    @Override
    public void onActivityStopped(Activity activity) {
        BLog.d(TAG, "onActivityStopped: %d, %s", mActivityCount, activity);
        if (mWeakHashMap.containsKey(activity)) {
            mActivityCount--;
            if (mActivityCount == 0) {
                BLog.d(TAG, "onActivityStopped 1 : %d, %s", mActivityCount, activity);
                MMSServiceSDK.get().setForeground(false);
            }
        }
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        mWeakHashMap.remove(activity);
    }

    public void setForegroundChangeStart(boolean foregroundChangeStart) {
        this.mForegroundChangeStart = foregroundChangeStart;
    }
}
