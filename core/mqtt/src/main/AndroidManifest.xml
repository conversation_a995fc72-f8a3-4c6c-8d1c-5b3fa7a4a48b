<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          >
    <!-- 开机广播接收权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <!-- 手机唤醒广播接收权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.USE_EXACT_ALARM"/>




    <application>
        <service
            android:name="com.kanzhun.mms.service.MMSServiceNative"
            android:foregroundServiceType="remoteMessaging"/>
        <service
            android:name="com.kanzhun.mms.service.MMSServiceNative$InnerService"
            android:foregroundServiceType="remoteMessaging"/>

        <service
            android:name="com.kanzhun.mms.service.process.GuardJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:foregroundServiceType="remoteMessaging"
            />
    </application>

</manifest>
