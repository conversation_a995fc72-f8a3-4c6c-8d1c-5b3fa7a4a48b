package com.twl.meeboss.common.ktx

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * @author: musa on 2025/05/13
 * @e-mail: <EMAIL>
 * @desc: 自定义操作符
 */
fun <T> Flow<T>.throttleFirst(periodMillis: Long): Flow<T> = flow {
    var lastTime = 0L
    collect { value ->
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastTime >= periodMillis) {
            lastTime = currentTime
            emit(value)
        }
    }
}