package com.twl.meeboss.core.network.utils;

import net.jpountz.lz4.LZ4CompressorWithLength;
import net.jpountz.lz4.LZ4DecompressorWithLength;
import net.jpountz.lz4.LZ4Factory;

public class Lz4Utils {
    private static final LZ4DecompressorWithLength decompressor = new LZ4DecompressorWithLength(LZ4Factory.fastestInstance().fastDecompressor());
    private static final LZ4CompressorWithLength compressor = new LZ4CompressorWithLength(LZ4Factory.fastestInstance().fastCompressor());

    public static byte[] compress(byte[] src) {
        return compressor.compress(src);
    }

    public static byte[] decompress(byte[] src) {
        return decompressor.decompress(src);
    }
}
