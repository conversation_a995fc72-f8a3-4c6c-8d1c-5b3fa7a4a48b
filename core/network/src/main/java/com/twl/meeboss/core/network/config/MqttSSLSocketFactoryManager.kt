package com.twl.meeboss.core.network.config

import android.content.Context
import com.twl.meeboss.core.network.config.HttpConfigManager.getNetEnvironment
import java.io.Serializable
import java.security.KeyStore
import java.security.cert.CertificateFactory
import javax.net.ssl.KeyManagerFactory
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManager
import javax.net.ssl.TrustManagerFactory

class MqttSSLSocketFactoryManager {

    companion object {

        fun createMqttSSLSocketFactory(context: Context): SSLSocketFactory {
            return try {
                val config = getSSLConfigBean()
                // 加载客户端证书
                val kmf = getClientKeyManagerFactory(context, config)
                // 加载服务器信任证书
                val trustManager = getTrustManagers(context, config)
                // 初始化 SSL 上下文
                val sslContext = SSLContext.getInstance("TLS")
                sslContext.init(kmf.keyManagers, trustManager, null)
                sslContext.socketFactory
            } catch (e: Exception) {
                throw IllegalStateException("Failed to create SSLSocketFactory", e)
            }
        }


        private fun getClientKeyManagerFactory(
            context: Context,
            config: SSLConfigBean
        ): KeyManagerFactory {
            val keyStore = KeyStore.getInstance("PKCS12")
            context.resources.assets.open(config.ksPath).use { ksInputStream ->
                keyStore.load(ksInputStream, config.password)
            }
            val kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm())
            kmf.init(keyStore, config.password)
            return kmf
        }

        private fun getTrustManagers(context: Context, config: SSLConfigBean): Array<TrustManager> {
            val certificateFactory = CertificateFactory.getInstance("X.509")
            val trustManager =
                context.resources.assets.open(config.certPath).use { certInputStream ->
                    val caCert = certificateFactory.generateCertificate(certInputStream)
                    val trustStore = KeyStore.getInstance(KeyStore.getDefaultType()).apply {
                        load(null)
                        setCertificateEntry("mqtt-server", caCert)
                    }
                    val tmf =
                        TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
                    tmf.init(trustStore)
                    tmf.trustManagers
                }
            return trustManager
        }

        private fun getSSLConfigBean(): SSLConfigBean {
            return if (getNetEnvironment().environmentType == EnvironmentType.PROD) {
                SSLConfigBean(
                    ksPath = "client.p12",
                    certPath = "server.crt",
                    password = "FS4IQH9T16DE9DHAMWI24UBMESX7DJJI".toCharArray()
                )
            } else {
                SSLConfigBean(
                    ksPath = "client_dev.p12",
                    certPath = "server_dev.crt",
                    password = "2WDMBA1VX1D6RF90K2Z1QB6C423B18KF".toCharArray()
                )
            }
        }
    }

    private data class SSLConfigBean(
        val ksPath: String,
        val certPath: String,
        val password: CharArray
    ):Serializable {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (other !is SSLConfigBean) return false

            if (ksPath != other.ksPath) return false
            if (certPath != other.certPath) return false
            if (!password.contentEquals(other.password)) return false

            return true
        }

        override fun hashCode(): Int {
            var result = ksPath.hashCode()
            result = 31 * result + certPath.hashCode()
            result = 31 * result + password.contentHashCode()
            return result
        }
    }
}