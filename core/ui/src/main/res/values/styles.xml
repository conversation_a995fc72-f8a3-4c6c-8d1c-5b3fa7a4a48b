<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="common_window_bottom_to_top_anim" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/common_activity_new_enter_up_glide</item>
        <item name="android:windowExitAnimation">@anim/common_activity_new_exit_up_glide</item>
    </style>

    <style name="LoadingDialog" parent="android:Theme.Dialog">
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:backgroundDimAmount">0.4</item>
    </style>

</resources>