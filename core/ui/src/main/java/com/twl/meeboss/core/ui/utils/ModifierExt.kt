package com.twl.meeboss.core.ui.utils

import android.annotation.SuppressLint
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.TabPosition
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.platform.debugInspectorInfo

/**
 * @param preventDoubleClick 防止快速重复点击
 */
@SuppressLint("ModifierFactoryUnreferencedReceiver")
fun Modifier.noRippleClickable(preventDoubleClick: Boolean = true, onClick: () -> Unit): Modifier =
    composed {
        val multipleEventsCutter = remember { MultipleEventsCutter.get() }
        clickable(
            indication = null,
            interactionSource = remember { MutableInteractionSource() },
            onClick = {
                if(preventDoubleClick){
                    multipleEventsCutter.processEvent { onClick() }
                }else{
                    onClick()
                }
            },
        )
    }

@Composable
fun (() -> Unit).throttleClick(): () -> Unit {
    val multipleEventsCutter = remember { MultipleEventsCutter.get() }
    return { multipleEventsCutter.processEvent { this() } }
}

fun Modifier.tabIndicatorOffsetWithPercent(
    currentTabPosition: TabPosition,
    percent: Float
): Modifier = composed(
    inspectorInfo = debugInspectorInfo {
        name = "tabIndicatorOffset"
        value = currentTabPosition
    }
) {
    val ratio = if (percent > 1) 1F else percent
    val currentTabWidth by animateDpAsState(
        targetValue = currentTabPosition.width,
        animationSpec = tween(durationMillis = 250, easing = FastOutSlowInEasing)
    )
    val indicatorOffset by animateDpAsState(
        targetValue = currentTabPosition.left + currentTabPosition.width * (1 - ratio) / 2,
        animationSpec = tween(durationMillis = 250, easing = FastOutSlowInEasing)
    )
    fillMaxWidth()
        .wrapContentSize(Alignment.BottomStart)
        .offset(x = indicatorOffset)
        .width(currentTabWidth * ratio)
}