package com.twl.meeboss.core.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.os.bundleOf
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.core.ui.R
import com.twl.meeboss.core.ui.component.wheelpicker.FVerticalWheelPicker
import com.twl.meeboss.core.ui.component.wheelpicker.rememberFWheelPickerState
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.GRAY_EEEEEE
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable

class TwoColumnWheelDialog : CommonBottomDialogFragment() {
    private val title: String by lazy {
        arguments?.getString("title") ?: ""
    }
    private val list1: List<String> by lazy {
        arguments?.getStringArrayList("list1") ?: arrayListOf()
    }
    private val defaultIndex1: Int by lazy {
        arguments?.getInt("defaultIndex1") ?: 0
    }
    private val list2: List<String> by lazy {
        arguments?.getStringArrayList("list2") ?: arrayListOf()
    }
    private val defaultIndex2: Int by lazy {
        arguments?.getInt("defaultIndex2") ?: 0
    }
    private var onConfirmClick: ((Int, Int) -> Unit)? = null

    companion object {
        fun newInstance(
            title: String,
            list1: List<String>,
            defaultIndex1: Int = 0,
            list2: List<String>,
            defaultIndex2: Int = 0,
            onConfirmClick: (Int, Int) -> Unit
        ) = TwoColumnWheelDialog().apply {
            this.onConfirmClick = onConfirmClick
            arguments = bundleOf(
                "title" to title,
                "list1" to ArrayList(list1),
                "defaultIndex1" to defaultIndex1,
                "list2" to ArrayList(list2),
                "defaultIndex2" to defaultIndex2
            )
        }
    }

    @Composable
    override fun DialogContent() {
        if (list1.isNotEmpty() && list2.isNotEmpty()) {
            TwoColumnWheelDialogContent(
                title = title,
                list1 = list1,
                defaultIndex1 = defaultIndex1,
                list2 = list2,
                defaultIndex2 = defaultIndex2,
                onCancelClick = { dismissSafely() },
                onConfirmClick = { index1, index2 ->
                    onConfirmClick?.invoke(index1, index2)
                    dismissSafely()
                }
            )
        }
    }
}

@Composable
fun TwoColumnWheelDialogContent(modifier: Modifier = Modifier,
                                title: String,
                                list1: List<String>,
                                defaultIndex1: Int = 0,
                                list2: List<String>,
                                defaultIndex2: Int = 0,
                                onCancelClick: () -> Unit,
                                onConfirmClick: (Int, Int) -> Unit) {
    val state1 = rememberFWheelPickerState(if (defaultIndex1 < 0) 0 else defaultIndex1)
    val state2 = rememberFWheelPickerState(if (defaultIndex2 < 0) 0 else defaultIndex2)
    Column(modifier = modifier
        .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
        .background(Color.White)) {
        Row(modifier = Modifier
            .height(57.dp)
            .padding(horizontal = 16.dp), verticalAlignment = Alignment.CenterVertically) {

            Text(text = stringResource(id = R.string.common_button_cancel),
                fontSize = 16.sp,
                lineHeight = 22.sp,
                color = Black484848,
                fontWeight = FontWeight.SemiBold, modifier = Modifier.noRippleClickable {
                    onCancelClick()
                })

            Text(text = title, modifier = Modifier.weight(1F), textAlign = TextAlign.Center, fontSize = 18.sp,
                lineHeight = 26.sp,
                color = Black222222,
                fontWeight = FontWeight.SemiBold)

            Text(text = stringResource(id = R.string.common_button_done), fontSize = 16.sp,
                lineHeight = 22.sp,
                color = Secondary,
                fontWeight = FontWeight.SemiBold, modifier = Modifier.noRippleClickable {
                    onConfirmClick(state1.currentIndex, state2.currentIndex)
                })
        }
        Row(modifier = modifier) {
            FVerticalWheelPicker(
                modifier = Modifier.weight(1F),
                state = state1,
                count = list1.size,
                itemHeight = 56.dp,
                focus = {
                    // Custom focus.
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(topStart = 8.dp, bottomStart = 8.dp))
                            .background(GRAY_EEEEEE)
                    )
                },
            ) {
                Text(list1[it], modifier = Modifier.fillMaxWidth(),
                    fontSize = 20.sp,
                    color = Black222222,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center)
            }

            FVerticalWheelPicker(
                modifier = Modifier.weight(1F),
                state = state2,
                count = list2.size,
                itemHeight = 56.dp,
                focus = {
                    // Custom focus.
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(topEnd = 8.dp, bottomEnd = 8.dp))
                            .background(GRAY_EEEEEE)
                    )
                },
            ) {
                Text(list2[it], modifier = Modifier.fillMaxWidth(),
                    fontSize = 20.sp,
                    color = Black222222,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center)
            }
        }
    }

}

@Preview
@Composable
private fun PreviewTwoColumnWheelDialogContent() {
    Box(modifier = Modifier
        .fillMaxSize()
        .background(Color.Gray)) {
        TwoColumnWheelDialogContent(
            title = "From",
            list1 = listOf("February", "March", "April", "June", "July"),
            defaultIndex1 = 2,
            list2 = listOf("2021", "2022", "2023", "2024", "2025"),
            defaultIndex2 = 3,
            onCancelClick = {},
            onConfirmClick = { index1, index2 -> }
        )
    }
}