package com.twl.meeboss.core.ui.utils

/**
 * @author: 冯智健
 * @date: 2024年07月03日 00:42
 * @description:
 */
object ComposeHookUtils {
    // ScrollableTab内部设置了单个 tab 的最小宽度 ScrollableTabRowMinimumTabWidth 为90，且属性无法自定义
    // https://issuetracker.google.com/issues/218684743 (Won't Fix)
    fun scrollableTabRowMinimumTabWidthChange(width: Float = 0.0f) = runCatching {
        val clazz = Class.forName("androidx.compose.material.TabRowKt")
        val field = clazz.getDeclaredField("ScrollableTabRowMinimumTabWidth")
        field.isAccessible = true
        field.set(null, width) // set tab min width to 0
        val clazz3 = Class.forName("androidx.compose.material3.TabRowKt")
        val field3 = clazz3.getDeclaredField("ScrollableTabRowMinimumTabWidth")
        field3.isAccessible = true
        field3.set(null, width) // set tab min width to 0
    }
}