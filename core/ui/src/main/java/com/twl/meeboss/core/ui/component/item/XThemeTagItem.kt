package com.twl.meeboss.core.ui.component.item

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ChainStyle
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.twl.meeboss.core.ui.R
import com.twl.meeboss.core.ui.theme.COLOR_E0F8EB
import com.twl.meeboss.core.ui.theme.Primary50
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.utils.noRippleClickable

@Composable
fun XThemeTagItem(modifier: Modifier = Modifier,
                  text: String,
                  borderStroke: BorderStroke = BorderStroke(
                      width = 0.dp,
                      color = Color.Transparent,
                  ),
                  horizontalPadding: Dp = 12.dp,
                  verticalPadding: Dp = 8.dp,
                  fontSize: TextUnit = 14.sp,
                  radius: Dp = 8.dp,
                  maxLine:Int = 1,
                  onRemoveClick:()->Unit = {},
                  onClick: () -> Unit = {},
                  ) {
    OutlinedButton(
        modifier = modifier,
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = COLOR_E0F8EB,
            contentColor = Secondary,
            disabledContentColor = Primary50,
            disabledContainerColor = Color.White
        ),
        border = borderStroke,
        contentPadding = androidx.compose.foundation.layout.PaddingValues(
            start = horizontalPadding,
            end = horizontalPadding,
            top = verticalPadding,
            bottom = verticalPadding
        ),
        shape = RoundedCornerShape(radius),
        onClick = onClick
    ) {
        ConstraintLayout {
            val (textRef, spacerRef, imageRef) = createRefs()
            createHorizontalChain(textRef,spacerRef,imageRef, chainStyle = ChainStyle.Packed(0f))

            Text(
                text = text,
                color = Secondary,
                fontWeight = FontWeight.Medium,
                fontSize = fontSize,
                maxLines = maxLine,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.constrainAs(textRef) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(spacerRef.start)
                    width = Dimension.preferredWrapContent
                }
            )
            Spacer(
                modifier = Modifier
                    .width(8.dp)
                    .constrainAs(spacerRef) {
                        start.linkTo(textRef.end)
                        end.linkTo(imageRef.start)
                    },
            )
            Image(
                painter = painterResource(id = R.drawable.ui_green_new),
                contentDescription = "Close",
                modifier = Modifier
                    .constrainAs(imageRef) {
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                        start.linkTo(spacerRef.end)
                        end.linkTo(parent.end)
                    }
                    .noRippleClickable(onClick = onRemoveClick)
            )
        }
    }

}

/**
 * 原先的 XThemeTagItem，使用了md3中的Button,但是Button的最小高度在内部做了限制,所以这里使用Row来实现
 * 自定义组件更灵活方便定制和拓展
 */
@Composable
fun XThemeTagItemSimple(
    text: String,
    modifier: Modifier = Modifier,
    fontSize: TextUnit = 14.sp,
    radius: Dp = 8.dp,
    maxLine: Int = 1,
    horizontalPadding: Dp = 12.dp,
    verticalPadding: Dp = 8.dp,
    onClick: () -> Unit = {},
    onRemoveClick: () -> Unit = {},
) {
    Row(
        Modifier
            .background(COLOR_E0F8EB, shape = RoundedCornerShape(radius))
            .padding(horizontal = horizontalPadding, vertical = verticalPadding)
            .clickable(onClick = onClick)
            .then(modifier),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            modifier = Modifier.weight(1f, false),
            text = text,
            color = Secondary,
            fontWeight = FontWeight.Medium,
            fontSize = fontSize,
            maxLines = maxLine,
            overflow = TextOverflow.Ellipsis,
        )
        Spacer(
            modifier = Modifier
                .width(8.dp)
        )
        Image(
            painter = painterResource(id = R.drawable.ui_green_new),
            contentDescription = "Close",
            modifier = Modifier
                .noRippleClickable(onClick = onRemoveClick)
        )
    }
}

@Preview
@Composable
private fun PreviewXTagItem() {
    Column(modifier = Modifier
        .fillMaxSize()
        .background(Color.White)) {
        XThemeTagItem(text = "longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglongtext", onClick = {})
        XThemeTagItem(modifier = Modifier.background(Color.Red), text = "这里看出来了，md3的Button最小高度限制了，红色区域就是补充的", onClick = {})
        XThemeTagItem(modifier = Modifier.background(Color.Blue), verticalPadding = 15.dp, text = "超过md3的Button最小高度限制了，就没有补充点击区域了", onClick = {})
        XThemeTagItemSimple(text = "longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglongtext")
        XThemeTagItemSimple(text = "short")
        XThemeTagItemSimple(text = "longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglongtext", maxLine = 2)
    }
}