package com.twl.meeboss.core.ui.dialog

import android.content.Context
import androidx.fragment.app.FragmentActivity
import com.twl.meeboss.core.ui.R
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.core.ui.utils.toResourceString

fun Context.showSingleChoiceBottomDialog(
    title: String,
    items: List<String>,
    defaultSelectedIndex: Int = 0,
    showSaveButton: Boolean = false,
    selectUIType: @DialogChooseUIType Int = DialogChooseUIType.TYPE_NORMAL,
    onItemClick: (Int) -> Unit,
) {
    if (this is FragmentActivity) {
        SingleChoiceDialogFragment.newInstance(title, items, defaultSelectedIndex, showSaveButton = showSaveButton, onItemClick = onItemClick, selectUIType = selectUIType)
            .showSafely(this)
    }
}

fun Context.showMultiChoiceBottomDialog(
    title: String, items: List<String>,
    defaultSelectList: List<String> = listOf(),
    exclusiveItem: String? = "",
    hasEnable: Boolean = true,
    onSaveClick: (List<String>) -> Unit
) {
    if (this is FragmentActivity) {
        MultiChoiceDialogFragment.newInstance(title, items = items, defaultSelectList = defaultSelectList, hasEnable = hasEnable, exclusiveItem = exclusiveItem, onSaveClick = onSaveClick)
            .showSafely(this)
    }
}

/**
 * 显示确认对话框
 */
fun Context.showConfirmDialog(
    title: String = "",
    content: String? = "",
    canceledOnTouchOutside:Boolean = false,
    confirmText: String = R.string.common_button_confirm.toResourceString(),
    onConfirm: () -> Unit = {},
    cancelText: String = R.string.common_button_cancel.toResourceString(),
    onCancel: () -> Unit = {}
) {
    if (this is FragmentActivity) {
        ConfirmDialog.newInstance(title, content, canceledOnTouchOutside,confirmText, onConfirm, cancelText, onCancel)
            .showSafely(this)
    }
}