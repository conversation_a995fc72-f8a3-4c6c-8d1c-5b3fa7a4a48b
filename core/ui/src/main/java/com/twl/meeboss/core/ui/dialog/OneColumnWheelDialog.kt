package com.twl.meeboss.core.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.os.bundleOf
import com.twl.meeboss.core.ui.R
import com.twl.meeboss.core.ui.component.wheelpicker.FVerticalWheelPicker
import com.twl.meeboss.core.ui.component.wheelpicker.rememberFWheelPickerState
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.GRAY_EEEEEE
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable

class OneColumnWheelDialog : CommonBottomDialogFragment() {

    private val title: String by lazy {
        arguments?.getString("title") ?: ""
    }
    private val list: List<String> by lazy {
        arguments?.getStringArrayList("list") ?: arrayListOf()
    }
    private val defaultIndex: Int by lazy {
        arguments?.getInt("defaultIndex") ?: 0
    }
    private var onConfirmClick: ((Int) -> Unit)? =null

    companion object {
        fun newInstance(
            title: String,
            list: List<String>,
            defaultIndex: Int = 0,
            onConfirmClick: (Int) -> Unit
        ) = OneColumnWheelDialog().apply {
            this.onConfirmClick = onConfirmClick
            arguments = bundleOf(
                "title" to title,
                "list" to list,
                "defaultIndex" to defaultIndex
            )
        }
    }
    @Composable
    override fun DialogContent() {
        OneColumnWheelDialogContent(
            title = title,
            list = list,
            defaultIndex = defaultIndex,
            onCancelClick = { dismissSafely() },
            onConfirmClick = { index ->
                onConfirmClick?.invoke(index)
                dismissSafely()
            }
        )
    }
}

@Composable
fun OneColumnWheelDialogContent(modifier: Modifier = Modifier,
                                title: String,
                                list: List<String>,
                                defaultIndex: Int = 0,
                                onCancelClick: () -> Unit,
                                onConfirmClick: (Int) -> Unit) {
    val state1 = rememberFWheelPickerState(if (defaultIndex < 0) 0 else defaultIndex)
    Column(modifier = modifier
        .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
        .background(Color.White)) {
        Row(modifier = Modifier
            .height(57.dp)
            .padding(horizontal = 16.dp), verticalAlignment = Alignment.CenterVertically) {

            Text(text = stringResource(id = R.string.common_button_cancel),
                fontSize = 16.sp,
                lineHeight = 22.sp,
                color = Black484848,
                fontWeight = FontWeight.SemiBold, modifier = Modifier.noRippleClickable {
                    onCancelClick()
                })

            Text(text = title, modifier = Modifier.weight(1F), textAlign = TextAlign.Center, fontSize = 18.sp,
                lineHeight = 26.sp,
                color = Black222222,
                fontWeight = FontWeight.SemiBold)

            Text(text = stringResource(id = R.string.common_button_done), fontSize = 16.sp,
                lineHeight = 22.sp,
                color = Secondary,
                fontWeight = FontWeight.SemiBold, modifier = Modifier.noRippleClickable {
                    onConfirmClick(state1.currentIndex)
                })
        }
        Row(modifier = modifier) {
            FVerticalWheelPicker(
                modifier = Modifier.weight(1F),
                state = state1,
                count = list.size,
                itemHeight = 56.dp,
                focus = {
                    // Custom focus.
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(topStart = 8.dp, bottomStart = 8.dp))
                            .background(GRAY_EEEEEE)
                    )
                },
            ) {
                Text(list[it], modifier = Modifier.fillMaxWidth(),
                    fontSize = 20.sp,
                    color = Black222222,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center)
            }

        }
    }

}

@Preview
@Composable
private fun PreviewOneColumnWheelDialogContent() {
    Box(modifier = Modifier
        .fillMaxSize()
        .background(Color.Gray)) {
        OneColumnWheelDialogContent(
            title = "From",
            list = listOf("February", "March", "April", "June", "July"),
            defaultIndex = 2,
            onCancelClick = {},
            onConfirmClick = { index -> }
        )
    }
}