package com.twl.meeboss.core.ui.component.state

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.core.ui.R
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.Primary
import com.twl.meeboss.core.ui.theme.Primary30
import com.twl.meeboss.core.ui.theme.Secondary

/**
 * @author: 冯智健
 * @date: 2024年08月10日 15:53
 * @description:
 */
@Preview
@Composable
fun XNoMoreItem(text: String = stringResource(id = R.string.common_no_more_results)) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            modifier = Modifier.padding(vertical = 16.dp),
            text = text,
            textAlign = TextAlign.Center,
            fontSize = 14.sp,
            color = Black888888
        )
    }
}

@Preview
@Composable
fun XLoadingItem(
    finishText: String = stringResource(id = R.string.common_data_updated),
    isFinish: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        horizontalArrangement = Arrangement.Center
    ) {
        if (isFinish) {
            Text(
                text = finishText,
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = Secondary
            )
        } else {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                strokeWidth = 2.dp,
                color = Primary,
                trackColor = Primary30
            )
        }
    }
}