package com.twl.meeboss.core.ui.dialog

import android.os.Build
import android.view.Gravity
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.window.DialogWindowProvider
import com.twl.meeboss.core.ui.theme.BlackEBEBEB

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BossUpBottomSheet(
    modifier: Modifier = Modifier,
    showBottomSheet: Boolean,
    onShowRequest: () -> Unit = {},
    onDismissRequest: () -> Unit = {},
    content: @Composable () -> Unit,
) {
    var showAnimatedDialog by remember { mutableStateOf(false) }

    LaunchedEffect(showBottomSheet) {
        if (showBottomSheet) showAnimatedDialog = true
    }

    if (showAnimatedDialog) {
        BasicAlertDialog(
            modifier = Modifier
                .imePadding()
                .systemBarsPadding(),
            onDismissRequest = onDismissRequest,
            properties = DialogProperties(
                usePlatformDefaultWidth = false,
                /*
                    decorFitsSystemWindows - Sets WindowCompat.setDecorFitsSystemWindows value. Set to false to use WindowInsets.
                                             If false, the soft input mode will be changed to WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
                                             and android:windowIsFloating is set to false for Android R and earlier.
                 */
                decorFitsSystemWindows = Build.VERSION.SDK_INT <= Build.VERSION_CODES.R
            )
        ) {
            var animateIn by remember { mutableStateOf(false) }
            LaunchedEffect(Unit) {
                animateIn = true
                onShowRequest()
            }
            val visible = animateIn && showBottomSheet

            val dimStart = 0.5f
            val dimAmount: Float by animateFloatAsState(
                if (visible) dimStart else 0f,
                label = "DimAmount"
            )

            (LocalView.current.parent as? DialogWindowProvider)?.window?.apply {
                setDimAmount(dimAmount)
                setWindowAnimations(-1)
                setGravity(Gravity.BOTTOM)
            }

            Box(
                modifier = modifier
                    .fillMaxWidth(),
            ) {
                val density = LocalDensity.current
                val configuration = LocalConfiguration.current
                val screenHeight = configuration.screenHeightDp.dp
                AnimatedVisibility(
                    visible = visible,
                    enter = slideInVertically(
                        animationSpec = tween(durationMillis = 150, easing = FastOutSlowInEasing),
                        initialOffsetY = { with(density) { screenHeight.roundToPx() } },
                    ),
                    exit = slideOutVertically(
                        targetOffsetY = { (it * 1.2).toInt() },
                        animationSpec = tween(durationMillis = 150, easing = FastOutLinearInEasing),
                    )
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                            .background(Color.White)
                    ) {
                        content()
                    }

                    DisposableEffect(Unit) {
                        onDispose {
                            showAnimatedDialog = false
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun App() {
    var showDialog by remember {
        mutableStateOf(false)
    }
    BossUpBottomSheet(
        showBottomSheet = showDialog,
        onDismissRequest = { showDialog = false }
    ) {
        Column(Modifier.background(MaterialTheme.colorScheme.surface)) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clickable { showDialog = false },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "CLOSE",
                    fontWeight = FontWeight.Bold,
                    color = BlackEBEBEB
                )
            }
        }
    }

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Button(onClick = {
            showDialog = true
        }) {
            Text(text = "Show Dialog")
        }
    }
}

@Preview
@Composable
fun PreviewApp() {
    App()
}