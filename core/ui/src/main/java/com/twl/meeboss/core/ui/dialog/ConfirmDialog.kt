package com.twl.meeboss.core.ui.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.Gravity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.core.ui.R
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.component.button.XDialogOutlineButton
import com.twl.meeboss.core.ui.fragment.BaseComposeDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.utils.dismissSafely

class ConfirmDialog : BaseComposeDialogFragment() {
    val title: String by lazy {
        arguments?.getString("title") ?: "This is a title"
    }
    val content: String? by lazy {
        arguments?.getString("content")
    }
    val canceledOnTouchOutside by lazy {
        arguments?.getBoolean("canceledOnTouchOutside") ?: false
    }
    val confirmText: String by lazy {
        arguments?.getString("confirmText") ?: ""
    }
    val cancelText: String by lazy {
        arguments?.getString("cancelText") ?: ""
    }
    private var onCancel: (() -> Unit)? = null
    private var onConfirm: (() -> Unit)? = null

    companion object {
        fun newInstance(
            title: String = "This is a title",
            content: String? = "",
            canceledOnTouchOutside:Boolean = false,
            confirmText: String = "",
            onConfirm: () -> Unit = {},
            cancelText: String = "",
            onCancel: () -> Unit = {}
        ): ConfirmDialog {
            return ConfirmDialog().apply {
                this.onConfirm = onConfirm
                this.onCancel = onCancel
                arguments = Bundle().apply {
                    putString("title", title)
                    putString("content", content)
                    putBoolean("canceledOnTouchOutside", canceledOnTouchOutside)
                    putString("confirmText", confirmText)
                    putString("cancelText", cancelText)
                }
            }
        }
    }
    override fun setDialogAttributes(dialog: Dialog) {
        dialog.run {
            dialog.window?.attributes?.gravity = Gravity.CENTER
        }
        dialog.setCancelable(false)
        dialog.setCanceledOnTouchOutside(false)
    }

    @Composable
    override fun ComposeComponent() {
        ConfirmDialogContent(title = title, content = content,
            confirmText = confirmText,
            onConfirm = {
                dismissSafely()
                onConfirm?.invoke()
            },
            cancelText = cancelText,
            onCancel = {
                dismissSafely()
                onCancel?.invoke()

            })
    }
}

@Composable
fun ConfirmDialogContent(title: String = "",
                         content: String? = "",
                         confirmText: String = stringResource(id = R.string.common_button_confirm),
                         onConfirm: () -> Unit = {},
                         cancelText: String = stringResource(id = R.string.common_button_cancel),
                         onCancel: () -> Unit = {}) {
    Box(modifier = Modifier
        .fillMaxWidth()
        .clip(RoundedCornerShape(12.dp))
        .background(Color.White)) {
        Column(modifier = Modifier.padding(16.dp, 20.dp)) {
            if(title.isNotBlank()){
                Text(
                    text = title,
                    fontWeight = FontWeight.SemiBold,
                    color = Black222222,
                    fontSize = 18.sp,
                    lineHeight = 26.sp
                )
            }
            if (!content.isNullOrBlank()) {
                Text(
                    text = content,
                    modifier = Modifier.padding(top = 8.dp),
                    fontWeight = FontWeight.Normal,
                    fontSize = 14.sp,
                    lineHeight = 20.sp
                )
            }
            Row(modifier = Modifier.padding(top = 20.dp)) {
                if(cancelText.isNotBlank()){
                    XDialogOutlineButton(
                        text = cancelText,
                        modifier = Modifier.weight(1f),
                        onClick = {
                            onCancel()
                        }
                    )
                    Spacer(modifier = Modifier.width(11.dp))
                }
                XCommonButton(
                    text = confirmText,
                    modifier = Modifier.weight(1f),
                    onClick = {
                        onConfirm()
                    }
                )
            }
        }

    }
}

@Preview
@Composable
private fun PreviewDialog1() {
    ConfirmDialogContent(content = "")
}


@Preview
@Composable
private fun PreviewDialog2() {
    ConfirmDialogContent(content = stringResource(id = R.string.ui_common_long_place_holder))
}

@Preview
@Composable
private fun PreviewDialog3() {
    ConfirmDialogContent(
        title = "This is a title",
        cancelText = "",
        content = stringResource(id = R.string.ui_common_long_place_holder))
}



