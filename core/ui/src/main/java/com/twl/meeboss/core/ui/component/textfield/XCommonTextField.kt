package com.twl.meeboss.core.ui.component.textfield

import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.core.ui.R
import com.twl.meeboss.core.ui.component.InputState
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.GRAY_AAAAAA
import com.twl.meeboss.core.ui.theme.RedBD222B
import kotlinx.coroutines.delay

@Composable
fun XCommonTextField(modifier:Modifier = Modifier, value: TextFieldValue,
               @StringRes innerTitle: Int = 0, //输入框内小标题
               @StringRes placeHolder: Int = 0,
               options: KeyboardOptions = KeyboardOptions.Default,
               textStyle: TextStyle = TextStyle(fontSize = 20.sp),
               showKeyboard: Boolean = false,
               onValueChange: (TextFieldValue) -> Unit) {
    var state by remember{ mutableStateOf(InputState.None) }

    Column(modifier = modifier) {

        Box(modifier = Modifier
            .fillMaxWidth()
            .sizeIn(minHeight = 56.dp)
            .border(2.dp,
                when (state) {
                    InputState.None -> {
                        BlackEBEBEB
                    }

                    InputState.Focus -> {
                        Color.Black
                    }

                    InputState.Error -> {
                        RedBD222B
                    }
                }, RoundedCornerShape(8.dp)),
            contentAlignment = Alignment.CenterStart
        ) {
            val focusRequester = remember { FocusRequester() }
            BasicTextField(modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp)
                .focusRequester(focusRequester)
                .onFocusChanged {
                    state = if (it.isFocused) {
                        InputState.Focus
                    }else{
                        InputState.None
                    }
                },
                textStyle = textStyle,
                keyboardOptions = options,
                singleLine = true, value = value, onValueChange = {
                    if(it.text != value.text || it.selection != value.selection){
                        onValueChange(it)
                    }

                }, decorationBox = { innerTextField ->
                    Column(modifier = Modifier.padding(16.dp, 0.dp, 0.dp, 0.dp)) {
                        Text(text = stringResource(id = innerTitle), color = if (state == InputState.Error) RedBD222B else Black888888, fontSize = 12.sp, fontWeight = FontWeight.W400)
                        Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                            Box(modifier = Modifier.weight(1F)) {

                                innerTextField()
                                if (value.text.isBlank() && state == InputState.None) {
                                    Text(text = stringResource(id = placeHolder), color = GRAY_AAAAAA, fontSize = 16.sp, fontWeight = FontWeight.W400)
                                }
                            }
                            if (value.text.isNotBlank() && state == InputState.Focus) {
                                Image(modifier = Modifier
                                    .clickable {
                                        onValueChange(value.copy(text = "", selection = TextRange(0, 0)))
                                    }, painter = painterResource(id = R.mipmap.ui_input_delete), contentDescription = "Delete icon")
                            }
                            Spacer(modifier = Modifier.width(16.dp))

                        }
                    }

                })
            if (showKeyboard) {
                LaunchedEffect(Unit) {
                    delay(100)
                    focusRequester.requestFocus()
                }
            }

        }
    }
}


@Preview
@Composable
fun PreviewXCommonTextField() {
    Surface(modifier = Modifier.fillMaxSize()) {
        Column {
            Box(modifier = Modifier.padding(18.dp, 8.dp)) {
                XCommonTextField(value = TextFieldValue(""), innerTitle = R.string.common_button_save, placeHolder = R.string.ui_common_place_holder, onValueChange = {})
            }


            Box(modifier = Modifier.padding(18.dp, 8.dp)) {
                XCommonTextField(value = TextFieldValue("123"), innerTitle = R.string.common_button_save, placeHolder = R.string.ui_common_place_holder, onValueChange = {})
            }
        }


    }

}
