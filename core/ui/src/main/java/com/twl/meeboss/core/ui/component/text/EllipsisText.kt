package com.twl.meeboss.core.ui.component.text

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.core.ui.R
import com.twl.meeboss.core.ui.theme.COLOR_000000
import com.twl.meeboss.core.ui.theme.COLOR_0D9EA3

@Composable
fun EllipsisText(
    //文字
    text: AnnotatedString,
    //文字颜色
    color: Color,
    //文字背景颜色
    backgroundColor: Color = Color.White,
    //字体大小
    fontSize: TextUnit = TextUnit.Unspecified,
    //行高
    lineHeight: TextUnit = TextUnit.Unspecified,
    //折叠行数
    maxLines: Int = Int.MAX_VALUE,
    //Text组件中inlineContent属性
    inlineContent: Map<String, InlineTextContent> = mapOf(),
    //省略文字样式
    ellipsisDecoration:TextDecoration = TextDecoration.Underline,
    //省略符号
    ellipsisPrefix: String = stringResource(id = R.string.common_ellipsis_prefix),
    //展开文字
    ellipsisText: String = stringResource(id = R.string.common_lower_case_more),
    //展开文字颜色
    ellipsisColor: Color = COLOR_0D9EA3,
    //上padding
    paddingTop: Dp = 0.dp,
    //下padding
    paddingBottom: Dp = 0.dp
    ) {
    var ellipsis by remember { mutableStateOf(false) }
    var expand by remember { mutableStateOf(false) }
    val style = TextStyle.Default.copy(color = color, fontSize = fontSize)
    var ellipsisBottom by remember { mutableFloatStateOf(0f) }
    var ellipsisLeft by remember { mutableFloatStateOf(0f) }
    val ellipsisMeasure = rememberTextMeasurer()
    val ellipsisLayoutResult = ellipsisMeasure.measure(
        text = ellipsisPrefix + ellipsisText,
        style = style
    )
    val ellipsisWidth = ellipsisLayoutResult.size.width
    Box(modifier = Modifier
        .animateContentSize()
        .padding(top = paddingTop, bottom = paddingBottom)) {
        Text(
            text = text,
            modifier = Modifier
                .background(backgroundColor),
            lineHeight = lineHeight,
            overflow = TextOverflow.Ellipsis,
            maxLines = if (expand) Int.MAX_VALUE else maxLines,
            inlineContent = inlineContent,
            onTextLayout = {
                try {
                    val offset = if (maxLines == Int.MAX_VALUE) 0 else ellipsisWidth
                    ellipsisBottom = it.getLineBottom(it.lineCount - 1)
                    ellipsisLeft = it.getHorizontalPosition(
                        it.getOffsetForPosition(
                            Offset(
                                it.getLineRight(it.lineCount - 1) - offset,
                                it.getLineTop(it.lineCount - 1)
                            )
                        ), true
                    )
                    if (ellipsisLeft + ellipsisWidth > it.size.width) {
                        ellipsisLeft = it.getHorizontalPosition(
                            it.getOffsetForPosition(
                                Offset(
                                    (it.size.width - ellipsisWidth).toFloat(),
                                    it.getLineTop(it.lineCount - 1)
                                )
                            ), true
                        )
                    }
                    ellipsis = it.isLineEllipsized(it.lineCount - 1)
                } catch (e: Throwable) {
                    ellipsis = true
                    e.printStackTrace()
                }
            },
            style = style
        )

        Row(
            modifier = Modifier
                .graphicsLayer {
                    translationX = ellipsisLeft
                    translationY = ellipsisBottom - size.height
                }
                .clickable { expand = !expand }
                .background(backgroundColor),
        ) {
            if (expand) {
                //收起
            } else if (ellipsis) {
                Text(text = ellipsisPrefix, style = style)
                Text(
                    text = "$ellipsisText ",
                    textDecoration = ellipsisDecoration,
                    style = style.copy(color = ellipsisColor)
                )
            } else {
                //兜底
            }

        }

    }

}


@Preview
@Composable
private fun PreviewEllipsisText() {
    EllipsisText(
        text = buildAnnotatedString {
            append(
                "I am happy to join with you today in what will go down in history as the greatest demonstration for freedom in the history of our nation."
            )
        },
        color = COLOR_000000,
        fontSize = 14.sp,
        maxLines = 2,
        ellipsisText = "more"
    )
}
