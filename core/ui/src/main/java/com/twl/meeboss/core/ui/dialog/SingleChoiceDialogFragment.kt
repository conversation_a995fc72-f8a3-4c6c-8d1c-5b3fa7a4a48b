package com.twl.meeboss.core.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.core.os.bundleOf
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.utils.dismissSafely

class SingleChoiceDialogFragment : CommonBottomDialogFragment() {
    private val title: String by lazy {
        arguments?.getString("title") ?: ""
    }
    private val items: List<CharSequence> by lazy {
        arguments?.getCharSequenceArrayList("items") ?: arrayListOf()
    }
    private val defaultSelectIndex: Int by lazy {
        arguments?.getInt("defaultSelectIndex") ?: 0
    }
    private val radius:Dp  by lazy {
        arguments?.getFloat("radius")?.dp ?: 12.dp
    }
    private val showSaveButton: Boolean by lazy {
        arguments?.getBoolean("showSaveButton") ?: false
    }
    private val selectUIType: @DialogChooseUIType Int by lazy {
        arguments?.getInt("selectUIType") ?: DialogChooseUIType.TYPE_NORMAL
    }
    private var onItemClick: ((Int) -> Unit)? = null

    companion object {
        fun newInstance(
            title: String,
            items: List<CharSequence>,
            defaultSelectIndex: Int = 0,
            radius: Dp = 12.dp,
            showSaveButton: Boolean = false,
            onItemClick: (Int) -> Unit,
            selectUIType: @DialogChooseUIType Int = DialogChooseUIType.TYPE_NORMAL,
        ) = SingleChoiceDialogFragment().apply {
            this.onItemClick = onItemClick
            arguments = bundleOf(
                "title" to title,
                "items" to items,
                "defaultSelectIndex" to defaultSelectIndex,
                "radius" to radius.value,
                "showSaveButton" to showSaveButton,
                "selectUIType" to selectUIType
            )
        }
    }

    @Composable
    override fun DialogContent() {
        SingleChoiceDialog(title, items, radius, defaultSelectIndex, this::closeDialog,this::onInnerItemClick, showSaveButton, selectUIType)
    }

    private fun onInnerItemClick(position: Int) {
        onItemClick?.invoke(position)
        dismissSafely()
    }

    private fun closeDialog() {
        dismissSafely()
    }
}

@Preview
@Composable
fun PreviewSingleChoiceDialogFragment() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Gray)
    ) {
        SingleChoiceDialog(
            title = "Single Choice Dialog",
            list = listOf("Item 1", "Item 2", "Item 3", "Item 4", "Item 5"),
            showSaveButton = true
        )
    }
}