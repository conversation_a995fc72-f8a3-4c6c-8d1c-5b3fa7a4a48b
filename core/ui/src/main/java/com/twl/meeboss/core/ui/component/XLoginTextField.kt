package com.twl.meeboss.core.ui.component

import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.core.ui.R
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.GRAY_AAAAAA
import com.twl.meeboss.core.ui.theme.RedBD222B

@Composable
fun XLoginTextField(modifier:Modifier = Modifier,value: TextFieldValue, @StringRes placeHolder: Int = 0,
                    state: InputState = InputState.None,
                    errorMessage: String = "", isPhone: Boolean = false,
                    showKeyboard: Boolean = false,
                    areaCode: String = "",
                    onClickAreaCode:()->Unit = {},
                    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
                    onValueChange: (TextFieldValue) -> Unit, onStateChanged: (InputState) -> Unit) {
    Column(modifier = modifier) {
        Box(modifier = Modifier
            .fillMaxWidth()
            .height(56.dp)
            .border(2.dp,
                when (state) {
                    InputState.None -> {
                        BlackEBEBEB
                    }

                    InputState.Focus -> {
                        Color.Black
                    }

                    InputState.Error -> {
                        RedBD222B
                    }
                }, RoundedCornerShape(8.dp)),
            contentAlignment = Alignment.CenterStart
        ) {
            val focusRequester = remember { FocusRequester() }
            BasicTextField(modifier = Modifier
                .fillMaxWidth()
                .focusRequester(focusRequester)
                .onFocusChanged {
                    if (it.isFocused) {
                        onStateChanged(InputState.Focus)
                    }
                },
                keyboardOptions = keyboardOptions,
                singleLine = true, value = value, onValueChange = onValueChange, decorationBox = { innerTextField ->
                Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                    if (isPhone) {
                        Spacer(modifier = Modifier.width(8.dp))
                        AreaCodeButton(modifier = Modifier, areaCode = areaCode){
                            onClickAreaCode()
                        }
                    }else{
                        Spacer(modifier = Modifier.width(16.dp))

                    }
                    Box(modifier = Modifier.weight(1F), contentAlignment = Alignment.CenterStart) {
                        if (value.text.isBlank()) {
                            Text(text = stringResource(id = placeHolder), color = GRAY_AAAAAA, fontSize = 16.sp, fontWeight = FontWeight.W400)
                        }
                        innerTextField()
                    }
                    if (value.text.isNotBlank()) {
                        Image(modifier = Modifier
                            .padding(0.dp, 0.dp, 16.dp, 0.dp)
                            .clickable {
                                onValueChange(value.copy(text = "", selection = TextRange.Zero))
                            }, painter = painterResource(id = R.mipmap.ui_input_delete), contentDescription = "Delete icon")
                    }

                }
            })
            if(showKeyboard){
                LaunchedEffect(Unit) {
                    focusRequester.requestFocus()
                }
            }
        }

        if(errorMessage.isNotBlank()){
            Spacer(modifier = Modifier.height(6.dp))
            Text(text = errorMessage, fontWeight = FontWeight.W400, fontSize = 12.sp, color = RedBD222B, lineHeight = 18.sp)
        }
    }
}


@Preview
@Composable
private fun XTextFieldPreview() {
    Surface(modifier = Modifier.fillMaxSize()) {
        Column {
            Box(modifier = Modifier.padding(18.dp, 8.dp)) {
                XLoginTextField(value = TextFieldValue(), state = InputState.Focus, placeHolder = 0, onValueChange = {}, onStateChanged = {})
            }

            Box(modifier = Modifier.padding(18.dp, 8.dp)) {
                XLoginTextField(value = TextFieldValue(""), placeHolder = 0, state = InputState.Error, errorMessage = "Please enter email address", onValueChange = {}, onStateChanged = {})
            }

            Box(modifier = Modifier.padding(18.dp, 8.dp)) {
                XLoginTextField(value = TextFieldValue("123"), areaCode = "+86", placeHolder = 0, state = InputState.Focus, isPhone = true, errorMessage = "Please enter email address", onValueChange = {}, onStateChanged = {})
            }
        }


    }

}
