package com.twl.meeboss.core.ui.ktx

import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp

/**
 * 一个不受系统字体缩放影响的 TextUnit 属性
 *
 * 与普通的 sp 不同，immutableSp 创建的字体大小不会随系统字体缩放设置变化
 */
val Int.immutableSp: TextUnit
    @Composable
    @ReadOnlyComposable
    get() {
        val fontScale = LocalDensity.current.fontScale
        return (this / fontScale).sp
    }

val Float.immutableSp: TextUnit
    @Composable
    @ReadOnlyComposable
    get() {
        val fontScale = LocalDensity.current.fontScale
        return (this / fontScale).sp
    }

/**
 * 智能的字体尺寸属性：
 * - 当系统字体缩放 >= 1.0 时，字体大小保持固定
 * - 当系统字体缩放 < 1.0 时，字体大小正常缩小
 */
val Int.smartSp: TextUnit
    @Composable
    @ReadOnlyComposable
    get() {
        val fontScale = LocalDensity.current.fontScale
        return when {
            fontScale >= 1.0f -> (this / fontScale).sp  // 标准或放大时抵消缩放效果
            else -> this.sp  // 缩小时应用系统缩放
        }
    }

val Float.smartSp: TextUnit
    @Composable
    @ReadOnlyComposable
    get() {
        val fontScale = LocalDensity.current.fontScale
        return when {
            fontScale >= 1.0f -> (this / fontScale).sp  // 标准或放大时抵消缩放效果
            else -> this.sp  // 缩小时应用系统缩放
        }
    }