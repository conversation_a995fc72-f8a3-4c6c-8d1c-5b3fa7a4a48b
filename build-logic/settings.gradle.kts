/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
pluginManagement {
    repositories {
        maven { url = uri("https://android3.weizhipin.com/nexus/repository/public/") }
//        maven { url = uri("https://maven.aliyun.com/nexus/content/groups/public/") }
//        maven { url = uri("https://maven.aliyun.com/repository/gradle-plugin/") }
//        maven { url = uri("https://mirrors.cloud.tencent.com/nexus/repository/maven-public/") }
//        maven { url = uri("https://jitpack.io") }
//        google {
//            content {
//                includeGroupByRegex("com\\.android.*")
//                includeGroupByRegex("com\\.google.*")
//                includeGroupByRegex("androidx.*")
//            }
//        }
//        mavenCentral()
//        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositories {
        maven { url = uri("https://android3.weizhipin.com/nexus/repository/public/") }
//        maven { url = uri("https://maven.aliyun.com/nexus/content/groups/public/") }
//        maven { url = uri("https://mirrors.cloud.tencent.com/nexus/repository/maven-public/") }
//        maven { url = uri("https://maven.aliyun.com/nexus/content/groups/public/") }
//        maven { url = uri("https://maven.aliyun.com/repository/central") }
//        maven { url = uri("https://maven.aliyun.com/repository/google") }
//        maven { url = uri("https://maven.aliyun.com/repository/jcenter") }
//        maven { url = uri("https://jitpack.io") }
//        mavenCentral()
//        google()
    }
    versionCatalogs {
        create("libs") {
            from(files("../gradle/libs.versions.toml"))
        }
    }
}

rootProject.name = "build-logic"
include(":convention")
