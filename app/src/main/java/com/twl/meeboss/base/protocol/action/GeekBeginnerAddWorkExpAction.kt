package com.twl.meeboss.base.protocol.action

import android.content.Context
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.export.GuidanceType

class GeekBeginnerAddWorkExpAction:IProtocolAction {

    override fun startAction(context: Context, params: Map<String, String>) {
        GeekPageRouter.jumpToGeekBeginnerAddWorkExpActivity(context, guidanceType = GuidanceType.BeginnerGuidance)
    }
}