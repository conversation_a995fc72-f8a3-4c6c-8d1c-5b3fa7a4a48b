package com.twl.meeboss.base.protocol.action

import android.content.Context
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.geek.export.GeekPageRouter

class H5JobDetailAction : IProtocolAction {
    val TAG = "H5JobDetailAction"
    override fun startAction(context: Context, params: Map<String, String>) {
        if (!UserProvider.isLogin()) {
            XLog.error(TAG, "not login")
            return
        }
        if (AccountManager.getFirstCompleteStatus() != UserConstants.COMPLETE_STATUS_ALREADY_COMPLETE) {
            XLog.error(TAG, "not complete")
            return
        }
        if (!UserProvider.isGeek()) {
            XLog.error(TAG, "not geek")
            return
        }
        val securityId = params["securityId"]
        XLog.info(TAG, "securityId:${securityId}")
        securityId?.takeIf { it.isNotEmpty() }?.let {
            GeekPageRouter.jumpToGeekJobDetailActivity(context, it)
        }
    }
}