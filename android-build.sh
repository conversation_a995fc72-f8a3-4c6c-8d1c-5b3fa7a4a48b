#!/bin/bash

function envRequiredInstall {
    # 检查 pip 是否已安装
    if command -v pip3 &> /dev/null; then
        echo "pip is already installed."
    else
        echo "pip is not installed. Installing pip..."
        # 下载 get-pip.py
        curl -s 'https://bootstrap.pypa.io/pip/3.6/get-pip.py' -o get-pip.py
        # 赋予执行权限
        chmod a+x get-pip.py
        # 安装 pip
        python3 get-pip.py
        # 清理 get-pip.py 文件
        rm get-pip.py
    fi

    # 检查 requests 是否已安装
    if python3 -c "import requests" &> /dev/null; then
        echo "requests is already installed."
    else
        echo "requests is not installed. Installing requests..."
        pip3 install requests
    fi
}

function gradleVersion() {
    /home/<USER>/gradle-8.4/bin/gradle "$@"
    if [ $? -eq 0 ]; then
        echo "gradle触发成功!"
    else
        echo "实时下载gradle编译"
        ./gradlew "$@"
    fi
}

function prepareNativeH5Zip {
    echo $h5ZipInfoList
    decodeZip=$(echo "$h5ZipInfoList" | base64 --decode)
    echo "decode : $decodeZip"
    if [ -z "$h5ZipInfoList" ]; then
        echo "没有本地h5信息"
#        notifyBuildMessage "没有本地h5信息，已忽略并继续打包中，请知悉！"
        return
    fi
    envRequiredInstall
    python3 sh/check_md5.py $decodeZip $flow_h5_zip_assets
    if [ $? -ne 0 ]; then
        echo "h5包内置失败，请检查参数是否正常！"
#        notifyBuildMessage "本地h5信息异常:$decodeZip，已忽略并继续打包中，请知悉！"
        return
    fi
    echo $decodeZip > $flow_h5_zip_assets/html/h5ZipConfig.json
}

#开始编译
function startBuild {
  prepareNativeH5Zip
	#清除缓存
	gradleVersion clean
	#执行编译
	gradleVersion $gradle_task $gradle_params
}

function buildBundle {
    gradleVersion bundleProdRelease
}

function startTinkerPatchBuild {
  prepareNativeH5Zip
	gradleVersion $gradle_task -PbaseApkDir=$1 $gradle_params
}

# 上传mapping文件
function uploadMappingToBugly {
	versionCode=$(getPropertiesVersionName "$flow_env_app_version_code")
  versionName=$(getPropertiesVersionName "$flow_env_app_version_name")
  /home/<USER>/jdk1.8.0_202/bin/java -jar sh/buglyqq-upload-symbol.jar -appid $flow_bugly_app_id -appkey $flow_bugly_app_key -bundleid $flow_env_app_pkg_name -version $versionName -platform Android -inputMapping $flow_app_mapping_path
  if [ $? -eq 0 ]; then
    echo 'mapping文件已上传'
  else
    echo 'mapping文件上传失败'
    notifyBuildFailed "mapping文件上传失败"
#    exit 1
  fi
}

# 崩溃堆栈解析
function uploadMappingToApm {
  echo "upload crash mapping file"
  echo $(java -jar sh/AutoTaskV3.jar  --workType apmCrash --apmKey $flow_apm_app_key --apmEnv prod --apmCrashAppDir $1)
  echo "upload crash mapping end $?"
}

# app分析
function uploadRFileToApm {
    echo "upload apk R.txt file"
    echo $(java -jar sh/AutoTaskV3.jar  --workType apmAnalysis --apmKey $flow_apm_app_key --apmEnv prod --apmAnalysisAppDir $1)
    echo "upload apk R.txt end $?"
}

# 快速获取gradle.properties文件中的属性值
function getPropertiesVersionName {
    cat gradle/libs.versions.toml | grep -v '^#' | grep -v '^$' | grep "versionCode" | grep '=' | awk -F '=' '{print $2}' | sed -E 's/[ "]//g; s/#.*//'
}

#apm监控 线上
function apmUpload {
  echo "apm open monitor"
	versionCode=$(getPropertiesVersionName "$flow_env_app_version_code")
  versionName=$(getPropertiesVersionName "$flow_env_app_version_name")
	echo $(curl -H "authorization: Basic Y29tLmR6LnNlcnZpY2U6" -X GET https://service-apm-qa.kanzhun.tech/apm/changeState\?version\=${versionName}\&state\=1)
}

function generateGrayDiffPatch {
    genDiffGrayPatch64=$(java -jar sh/diffjarv2.jar "$2" "$1" "$3")
    if [ $? -eq 0 ]; then
        echo "$2差分包patch生成成功!"
    else
        echo "$2差分包patch生成失败! $genDiffGrayPatch64"
        notifyBuildFailed "$genDiffGrayPatch64"
        exit 1
    fi
}

function curlSaveFile {
    curl -s $1 -o $2
    if [ $? -eq 0 ]; then
        echo "$1 文件下载成功"
    else
        echo "$1 文件下载失败"
        notifyBuildFailed "$1 文件下载失败 请检查参数配置是否正确"
        exit 1
    fi
}

#打包通知
function notifyBuildFailed {
  versionCode=$(getPropertiesVersionName)

	last_commit_msg=$(git log -1 --oneline)
	branch=$(git symbolic-ref --short HEAD)
	if [[ "$env" == "prod" ]]; then
    msg_content="[$flow_env_project_name]应用打包失败\n打包分支: $branch\n版本号: $versionCode\n构建号：$BUILDID\n最新提交信息: $last_commit_msg\n原因:$1\n触发用户: $email\n前往如流平台查看打包详情: https://flow.weizhipin.com/manage/app-pack"
  else
    msg_content="[$flow_env_project_name]应用打包失败\n打包分支: $branch\n版本号: $versionCode\n构建号：$BUILDID\n最新提交信息: $last_commit_msg\n原因:$1\n触发用户: $email\n前往如流平台查看打包详情: https://flow-qa.weizhipin.com/manage/app-pack"
  fi
  echo $msg_content
	msg="{\"msg_type\":\"text\",\"content\":{\"text\":\"$msg_content\"}}"
	# 测试机器人
  curl -X POST -H "Content-Type: application/json" \
  -d "$msg" https://hi-open.zhipin.com/open-apis/bot/hook/$robotId
}

#打包通知
function notifyBuildMessage {
  versionCode=$(getPropertiesVersionName)

	last_commit_msg=$(git log -1 --oneline)
	branch=$(git symbolic-ref --short HEAD)
	if [[ "$env" == "prod" ]]; then
    msg_content="[$flow_env_project_name]打包提醒\n打包分支: $branch\n版本号: $versionCode\n构建号：$BUILDID\n最新提交信息: $last_commit_msg\n原因:$1\n触发用户: $email\n前往如流平台查看打包详情: https://flow.weizhipin.com/manage/app-pack"
  else
    msg_content="[$flow_env_project_name]打包提醒\n打包分支: $branch\n版本号: $versionCode\n构建号：$BUILDID\n最新提交信息: $last_commit_msg\n原因:$1\n触发用户: $email\n前往如流平台查看打包详情: https://flow-qa.weizhipin.com/manage/app-pack"
  fi
  echo $msg_content
	msg="{\"msg_type\":\"text\",\"content\":{\"text\":\"$msg_content\"}}"
	# 测试机器人
  curl -X POST -H "Content-Type: application/json" \
  -d "$msg" https://hi-open.zhipin.com/open-apis/bot/hook/$robotId
}

#打包通知
function notifyBuildSuccess {
  versionCode=$(getPropertiesVersionName)

  last_commit_msg=$(git log -1 --oneline)
  branch=$(git symbolic-ref --short HEAD)


  if [ $1 -eq 0 ]; then
    msg_content="[$flow_env_project_name]Qa测试包打包成功\n打包分支: $branch\n版本号: $versionCode\n版本名称: $versionName\n构建号：$BUILDID\n64位包下载地址: $3\n32位包下载地址: $2\n最新提交信息: $last_commit_msg\n触发用户: $email\n前往如流平台查看打包详情: https://flow-qa.weizhipin.com/manage/app-pack\n"
#    if [[ "$testPkg" == "true" ]]; then
#        chmod a+x sh/qianji_install.sh
#        ./sh/qianji_install.sh 1
#    fi
  elif [ $1 -eq 1 ]; then
    msg_content="[$flow_env_project_name]安卓正式包打包成功\n打包分支: $branch\n版本号: $versionCode\n版本名称: $versionName\n构建号：$BUILDID\n64位包下载地址: $3\n32位包下载地址: $2\n基准包下载地址: $4\n最新提交信息: $last_commit_msg\n触发用户: $email\n$5\n$6\n前往如流平台查看打包详情: https://flow.weizhipin.com/manage/app-pack\n"
  fi

  if [ $1 -eq 0 -o $1 -eq 1 ]; then
        # 生成二维码
        chmod a+x sh/qr_code_notify.sh
        ./sh/qr_code_notify.sh "$group_id" "$msg_content"
        if [ $? -eq 0 ]; then
            exit 0
        fi
  fi


  msg="{\"msg_type\":\"text\",\"content\":{\"text\":\"$msg_content\"}}"
  if [ $1 -eq 0 ]; then
    # 测试机器人
    curl -X POST -H "Content-Type: application/json" \
    -d "$msg" https://hi-open.zhipin.com/open-apis/bot/hook/$robotId
  elif [ $1 -eq 1 ]; then
    # 研发群 release
#    curl -X POST -H "Content-Type: application/json" \
#    -d "$msg" https://hi-open.zhipin.com/open-apis/bot/hook/dc402a847ee040478265b097d11a94fb
    # 测试机器人
    curl -X POST -H "Content-Type: application/json" \
    -d "$msg" https://hi-open.zhipin.com/open-apis/bot/hook/$robotId
  elif [ $1 -eq 2 ]; then
    # 研发群 热修复
    curl -X POST -H "Content-Type: application/json" \
    -d "$msg" https://hi-open.zhipin.com/open-apis/bot/hook/$robotId
  elif [ $1 -eq 3 ]; then
    # 研发群 灰度包
    curl -X POST -H "Content-Type: application/json" \
    -d "$msg" https://hi-open.zhipin.com/open-apis/bot/hook/$robotId
    # 测试机器人
#    curl -X POST -H "Content-Type: application/json" \
#    -d "$msg" https://hi-open.zhipin.com/open-apis/bot/hook/cc839a317e3942e19eb7d59d4553f6f5
  fi
    # qa机器人
    curl -X POST -H "Content-Type: application/json" \
    -d "$msg" http://hi-qa.weizhipin.com/open-apis/bot/hook/d768eeb4900a491aa323b50c0ce9de67
  echo $msg_content

}

