# -*- coding: utf-8 -*-
import json
import requests
import hashlib
import shutil
import zipfile
import os
import sys

def process_json_data(json_str, extract_dir):
    try:
        json_data = json.loads(json_str)
        if not isinstance(json_data, list):
            print("JSON 格式错误，退出脚本。")
            sys.exit(1)
        for item in json_data:
            zipFileMd5 = item['zipFileMd5']
            downloadUrl = item['downloadUrl']
            appId = item['appId']
            version = item['version']
            buildVersion = item['buildVersion']
            allFileMd5 = item['allFileMd5']
            zip_path = download_and_verify(downloadUrl, zipFileMd5, appId, version, buildVersion)
            print(f"download zip file check md5 ok: {downloadUrl} allFileMd5={allFileMd5}  zipFileMd5={zipFileMd5}")
            if zip_path:
                retry_count = 0
                while True:
                    if calculate_md5_from_zip(zip_path, allFileMd5, f"{extract_dir}/html/{appId}/{version}-{buildVersion}"):
                        print(f"zip file list in {appId}/{version}-{buildVersion} check ok!")
                        break
                    else:
                        os.remove(zip_path)
                        zip_path = download_and_verify(downloadUrl, zipFileMd5, appId, version, buildVersion)
                        if not zip_path:
                            print("zip file invalid, exit !")
                            sys.exit(1)
                        retry_count += 1
                        print(f"zip file list in {appId}/{version}-{buildVersion} failed，retry :{retry_count} \n")
                        if retry_count == 3:
                            print("retry in 3 times failed! exit build")
                            sys.exit(1)
        sys.exit(0)
    except json.JSONDecodeError:
        print("error JSON failed")
        sys.exit(1)


def download_and_verify(url, expected_zip_md5, app_id, version, build_version):
    retry_count = 0
    temp_file_path = f"./{app_id}-{version}-{build_version}.zip"
    while True:
        response = requests.get(url, stream=True)
        with open(temp_file_path, 'wb') as out_file:
            shutil.copyfileobj(response.raw, out_file)
        downloaded_md5 = hashlib.md5(open(temp_file_path, 'rb').read()).hexdigest()
        if downloaded_md5 == expected_zip_md5:
            return temp_file_path
        else:
            retry_count += 1
            if retry_count == 3:
                print(f"download failed，download 3 times not match MD5。\nurl {url} md5 {downloaded_md5}, expected {expected_zip_md5}")
                sys.exit(1)


def calculate_file_md5(file_path):
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)

    file_md5 = hash_md5.hexdigest()
    print(f"files : {file_path} md5 -> {file_md5}")
    return file_md5

def find_all_files(directory, files=None):
    if files is None:
        files = []

    # 遍历目录中的所有条目
    for entry in os.listdir(directory):
        # 构建完整路径
        full_path = os.path.join(directory, entry)

        # 如果条目是文件，则添加到列表中
        if os.path.isfile(full_path):
            files.append(full_path)
        # 如果条目是目录，则递归调用
        elif os.path.isdir(full_path):
            find_all_files(full_path, files)

    return files


def calculate_md5_from_zip(zip_path, expected_all_file_md5, extract_folder):
    try:
        # 清理上次尝试留下的文件（如果有的话）
        if os.path.exists(extract_folder):
            shutil.rmtree(extract_folder)

        # 检查当前工作目录（可选）
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_folder)

        extracted_files = find_all_files(extract_folder)
        # 计算每个文件的MD5，并排序
        file_md5s = sorted([calculate_file_md5(file_path) for file_path in extracted_files])
        # 将MD5值拼接成一个字符串
        concatenated_md5 = ''.join(file_md5s)

        # 对拼接后的字符串计算MD5
        final_md5 = hashlib.md5(concatenated_md5.encode('utf-8')).hexdigest()
        print(f"final_md5:{final_md5}, expected_all_file_md5 {expected_all_file_md5}")
        return final_md5 == expected_all_file_md5
    except zipfile.BadZipFile:
        print(f"ZIP file error:{zip_path}")
        return False
    except Exception as e:
        print(f"unZIP error:{e}")
        return False


if __name__ == "__main__":
    if len(sys.argv) > 2:
        process_json_data(sys.argv[1], sys.argv[2])
    else:
        print("No JSON data provided.")
        sys.exit(1)