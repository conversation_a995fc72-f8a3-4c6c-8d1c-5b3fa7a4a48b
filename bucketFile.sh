#!/bin/bash

function initS3Env {
#	wget http://minio0.arsenal.kanzhun-inc.com/asiatrip/publicUtil/linux/mc -P ./
#	echo $(pwd)
#	if [ $? -eq 0 ]; then
#    	echo "mc下载成功!"
#	else
#	    echo "mc下载失败!"
#	fi
  mv ./sh/mc ./mc
	chmod a+x ./mc
	if [ $? -eq 0 ]; then
      echo "mc添加权限成功!"
  else
      echo "mc添加权限失败!"
  fi

	./mc config host add ci_bucket https://s3-tcloud-ht.kanzhun.tech "$autoTestAccessKeyQa" "$autoTestSecurityKeyQa"
  	if [ $? -eq 0 ]; then
      	echo "autoTest ci mc初始化成功!"
  	else
  	    echo "autoTest ci mc初始化失败!"
  	fi
}


function uploadFile {
	if [ "$#" -ne 3 ]; then
	    echo "错误：期望接收3个参数"
	    echo "用法：$0 <本地文件路径> <bucket文件存储路径> <bucket文件名>"
	    return 1
	fi
	result=$(./mc cp "$1" "qa_bucket/flow-package/$2/$3")
	if [ $? -eq 0 ]; then
    	echo "文件$1上传成功!"
	else
	    echo "上传命令执行失败！$result"
	    return 1
	fi
}

function uploadAutoTestFile {
	if [ "$#" -ne 3 ]; then
	    echo "错误：期望接收3个参数"
	    echo "用法：$0 <本地文件路径> <bucket文件存储路径> <bucket文件名>"
	    return 1
	fi
	result=$(./mc cp "$1" "ci_bucket/public/$2/$3")
	if [ $? -eq 0 ]; then
    	echo "文件$1上传成功! "
	else
	    echo "上传命令执行失败！$result"
	    return 1
	fi
}


function downloadFile {
	if [ "$#" -ne 2 ]; then
	    echo "错误：期望接收2个参数"
	    echo "用法：$0 <bucket文件绝对路径> <本地保存路径> "
	    exit 1
	fi
	result=$(./mc cp "qa_bucket/flow-package/$1" "$2")
	if [ $? -eq 0 ]; then
    	echo "文件$1下载成功!"
	else
	    echo "下载命令执行失败！$result"
	    exit 1
	fi
}

function containsFile {
  if [ "$#" -ne 2 ]; then
      echo "错误：期望接收2个参数"
      echo "用法：$0 <文件夹> <文件名> "
      exit 1
  fi

  result=$(./mc ls "qa_bucket/flow-package/$1")
  if echo "$result" | grep -q "$2"; then
      return 0
  else
      return 1
  fi
}