package com.twl.meeboss.debugger.autotest.localserver

import com.blankj.utilcode.util.ProcessUtils
import com.blankj.utilcode.util.Utils
import com.bzl.dz.autotest.localserver.ATLocalServerManager
import com.twl.meeboss.base.initializer.IInitializer
import com.twl.meeboss.common.base.AppConfig

/**
 * 自动化测试本地服务初始化
 */
class ATLocalServerInitializer : IInitializer {
    override fun init(appConfig: AppConfig) {
        if (ProcessUtils.isMainProcess()) {
            ATLocalServerManager.init(Utils.getApp())
        }
    }
}