# FastJson相关
-dontwarn com.alibaba.fastjson.**
-keep class com.alibaba.fastjson.** { *; }

# 处理缺失类的警告
-dontwarn java.awt.**
-dontwarn javax.money.**
-dontwarn javax.ws.rs.**
-dontwarn org.glassfish.jersey.**
-dontwarn org.javamoney.moneta.**
-dontwarn org.joda.time.**
-dontwarn springfox.documentation.**

# 保留fastjson的序列化器
-keep class com.alibaba.fastjson.serializer.** { *; }
-keep class com.alibaba.fastjson.parser.** { *; }
-keep class com.alibaba.fastjson.support.** { *; }

# 特别处理一些fastjson引用但实际不需要的类
-keep class com.alibaba.fastjson.serializer.AwtCodec { *; }
-keep class com.alibaba.fastjson.serializer.JodaCodec { *; }
-keep class com.alibaba.fastjson.support.moneta.MonetaCodec { *; }
-keep class com.alibaba.fastjson.support.jaxrs.FastJsonProvider { *; }
-keep class com.alibaba.fastjson.support.jaxrs.FastJsonAutoDiscoverable { *; }
-keep class com.alibaba.fastjson.support.springfox.SwaggerJsonSerializer { *; }

# 自动化测试相关
-keep class com.bzl.dz.autotest.** { *; }
-dontwarn com.bzl.dz.autotest.**
-keep class com.twl.meeboss.debugger.autotest.** { *; }
-dontwarn com.twl.meeboss.debugger.autotest.**
-keep class com.blankj.utilcode.** { *; }
-dontwarn com.blankj.utilcode.**
