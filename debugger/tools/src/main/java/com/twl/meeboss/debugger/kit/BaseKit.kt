package com.twl.meeboss.debugger.kit

import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import com.twl.meeboss.debugger.viewmodel.DebuggerViewModel
/**
 * @author: 冯智健
 * @date: 2024年08月10日 18:22
 * @description:
 */
abstract class BaseKit(override val category: Int = 0): AbstractKit() {
    val TAG = this::class.java.simpleName
    val viewModel: DebuggerViewModel? by lazy {
        (currentActivity() as? ViewModelStoreOwner)?.let {
            ViewModelProvider(it)[DebuggerViewModel::class]
        }
    }
}