/*
 * Copyright 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@file:OptIn(ExperimentalSharedTransitionApi::class, ExperimentalSharedTransitionApi::class)

package com.twl.meeboss.debugger.activity.animate

import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionLayout
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.Key.Companion.Ro
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.twl.meeboss.chat.core.model.MessageModel
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.debugger.R


private val listSnacks = listOf(
    Messages("Cupcake", "", R.drawable.cupcake),
    Messages("Donut", "", R.drawable.donut),
    Messages("Eclair", "", R.drawable.eclair),
    Messages("Froyo", "", R.drawable.froyo),
    Messages("Gingerbread", "", R.drawable.gingerbread),
    Messages("Honeycomb", "", R.drawable.honeycomb),
)

private val shapeForSharedElement = RoundedCornerShape(16.dp)

@OptIn(ExperimentalSharedTransitionApi::class)
@Preview
@Composable
fun AnimatedVisibilitySharedElementShortenedMessageExample() {
    // [START android_compose_shared_elements_animated_visibility]
    var selectedSnack by remember { mutableStateOf<Messages?>(null) }

    SharedTransitionLayout(modifier = Modifier.fillMaxSize()) {
        MessageListContent(
            selectedSnack = selectedSnack,
            onSnackSelected = { snack ->
                selectedSnack = snack
            }
        )
        // Contains matching AnimatedContent with sharedBounds modifiers.
        MessageEditDetails(
            snack = selectedSnack,
            onConfirmClick = {
                selectedSnack = null
            }
        )
    }
    // [END android_compose_shared_elements_animated_visibility]
}

@Composable
fun SharedTransitionScope.MessageListContent(
    modifier: Modifier = Modifier,
    selectedSnack: Messages?,
    onSnackSelected: (Messages) -> Unit
) {
    LazyColumn(
        // [START_EXCLUDE]
        modifier = Modifier
            .fillMaxSize()
            .background(Color.LightGray.copy(alpha = 0.5f))
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
        // [END_EXCLUDE]
    ) {
        items(listSnacks) { snack ->
            MessageContents(
                snack = snack,
                selectedSnack = selectedSnack,
                sharedTransitionScope = this@MessageListContent,
                onClick = {
                    onSnackSelected(snack)
//                            selectedSnack = snack
                }
            )
        }


    }
}

@Composable
fun SharedTransitionScope.MessageEditDetails(
    snack: Messages?,
    modifier: Modifier = Modifier,
    onConfirmClick: () -> Unit
) {
    AnimatedContent(
        modifier = modifier,
        targetState = snack,
        label = "SnackEditDetails"
    ) { targetSnack ->

        if (targetSnack != null) {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .clickable {
                        onConfirmClick()
                    }
                    .background(Color.Black.copy(alpha = 0.5f)),
            ) {
                Text(
                    modifier = Modifier
                        .background(Color.Blue)
                        .clip(RoundedCornerShape(10.dp))
                        .padding(10.dp)
                        .sharedElement(
                            state = rememberSharedContentState(key = snack?.name.notNull()),
                            animatedVisibilityScope = this@AnimatedContent
                        ).sharedBounds(
                            rememberSharedContentState(key = snack?.name.notNull()),
                            animatedVisibilityScope = this@AnimatedContent,
                            enter = fadeIn(),
                            exit = fadeOut(),
                            resizeMode = SharedTransitionScope.ResizeMode.ScaleToBounds()
                        ).wrapContentWidth(),
                    text = snack?.name.notNull(),
                    style = MaterialTheme.typography.titleSmall
                )
            }


        }

    }
}

@Composable
fun MessageContents(
    snack: Messages,
    selectedSnack: Messages?,
    modifier: Modifier = Modifier,
    sharedTransitionScope: SharedTransitionScope,
    onClick: () -> Unit
) {

    Row(modifier = modifier.noRippleClickable { onClick() }) {

        with(sharedTransitionScope) {
            AnimatedVisibility(visible = snack != selectedSnack) {
                Text(
                    modifier = Modifier
                        .background(Color.Blue)
                        .clip(RoundedCornerShape(10.dp))
                        .padding(10.dp)
                        .sharedElement(
                            state = rememberSharedContentState(key = snack.name),
                            animatedVisibilityScope = this@AnimatedVisibility
                        ).sharedBounds(
                            rememberSharedContentState(key = snack.name),
                            animatedVisibilityScope = this@AnimatedVisibility,
                            enter = fadeIn(),
                            exit = fadeOut(),
                            resizeMode = SharedTransitionScope.ResizeMode.ScaleToBounds()

                        ).wrapContentWidth(),
                    text = snack.name,
                    style = MaterialTheme.typography.titleSmall
                )
            }

        }

    }

}

data class Messages(
    val name: String,
    val description: String,
    @DrawableRes val image: Int,
    val message: MessageModel<out ChatMessage>? = null
)
