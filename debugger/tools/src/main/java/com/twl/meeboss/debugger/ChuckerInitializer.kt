package com.twl.meeboss.debugger

import android.content.Context
import com.chuckerteam.chucker.api.ChuckerInterceptor
import com.twl.meeboss.core.network.HttpCore

object ChuckerInitializer {
    private var hasInit = false
    fun init(context: Context) {
        if (hasInit) {
            return
        }
        hasInit = true
        HttpCore.addInterceptor(
            ChuckerInterceptor.Builder(context)
                .addBodyDecoder(JsonBodyDecoder()) // 添加JSON格式化解码器
                .build()
        ) //这个ChuckerInterceptor必须放到所有的网络请求之前, 否则chucker无法生效
    }
}

