package com.twl.meeboss.debugger

import com.blankj.utilcode.util.ProcessUtils
import com.blankj.utilcode.util.Utils
import com.didichuxing.doraemonkit.DoKit
import com.twl.meeboss.base.initializer.IInitializer
import com.twl.meeboss.base.protocol.ProtocolHelper
import com.twl.meeboss.common.base.AppConfig
import com.twl.meeboss.debugger.kit.CompanyAuthKit
import com.twl.meeboss.debugger.kit.CrashGenKit
import com.twl.meeboss.debugger.kit.H5DemoKit
import com.twl.meeboss.debugger.kit.MrDingKit
import com.twl.meeboss.debugger.kit.MrFengKit
import com.twl.meeboss.debugger.kit.MrQinKit
import com.twl.meeboss.debugger.kit.SwitchEnvironmentKit
import com.twl.meeboss.debugger.kit.UserInfoEntryKit

/**
 * 在这里增加具体的调试逻辑，如果要新增方法，先在IDebugger里增加，两个模块都要实现，避免打包报错
 */
internal class DebuggerInitializer : IInitializer {
    override fun init(appConfig: AppConfig) {
        if (ProcessUtils.isMainProcess()) {
            DoKit.Builder(Utils.getApp())
                .customKits(listKits = mutableListOf(
                    CompanyAuthKit(),
                    UserInfoEntryKit(),
                    CrashGenKit(),
                    MrQinKit(),
                    MrFengKit(),
                    MrDingKit(),
                    H5DemoKit(),
                    SwitchEnvironmentKit()
                ))
                .webDoorCallback { _, url -> ProtocolHelper.parseProtocol(url) }
                .build()
        }

    }
}