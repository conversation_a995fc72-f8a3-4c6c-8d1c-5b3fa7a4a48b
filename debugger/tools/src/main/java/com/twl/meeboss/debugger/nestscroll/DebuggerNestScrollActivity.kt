package com.twl.meeboss.debugger.nestscroll

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.PointerEventType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.dp
import com.google.accompanist.pager.HorizontalPager
import com.google.accompanist.pager.rememberPagerState
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.debugger.nestscroll.ui.theme.NestedscrollsampleTheme

class DebuggerNestScrollActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            NestedscrollsampleTheme {
                val showCollapsing = remember { mutableStateOf(false) }
                val showSearch = remember { mutableStateOf(false) }
                val showCart = remember { mutableStateOf(false) }
                if (showCollapsing.value) {
                    Collapsing(showCollapsing)
                } else if (showSearch.value) {
                    SearchView(showSearch)
                } else if (showCart.value) {
                    Cart()
                } else {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            verticalArrangement = Arrangement.spacedBy(20.dp),
                        ) {
                            MyButton(text = "Collapsing", show = showCollapsing)
                            MyButton(text = "Search", show = showSearch)
                            MyButton(text = "Cart", show = showCart)
                        }
                    }
                }
                BackHandler {
                    if (showCollapsing.value) {
                        showCollapsing.value = false
                    } else if (showSearch.value) {
                        showSearch.value = false
                    } else if (showCart.value) {
                        showCart.value = false
                    } else {
                        finish()
                    }
                }
            }

            //HorizontalPager和lazyRow滑动冲突处理
            /*Column {
                val pageState = rememberPagerState(initialPage = 0)
                val scrollEnable = remember {
                    mutableStateOf(true)
                }
                HorizontalPager(
                    modifier = Modifier
                        .fillMaxSize(),
                    state = pageState,
                    count = 2,
                    userScrollEnabled = scrollEnable.value
                ) {page ->
                    if (page == 0) {
                        Column(
                            Modifier
                                .fillMaxSize()
                                .background(Color.Red)
                        ) {
                            LazyRowWithGestureDetection(scrollEnable)
                        }
                    } else {
                        Box(
                            Modifier
                                .fillMaxSize()
                                .background(Color.Blue)
                        ) {
                        }
                    }
                }
            }*/
        }
    }

    companion object {
        fun intent(context: Context) {
            context.startActivity(Intent(context, DebuggerNestScrollActivity::class.java))
        }
    }
}

@Composable
fun LazyRowWithGestureDetection(
    isPagerScrollable: MutableState<Boolean> = mutableStateOf(true),
) {
    LazyRow(
        modifier = Modifier.pointerInput(Unit) {
            awaitPointerEventScope {
                while (true) {
                    val event = awaitPointerEvent()
                    when (event.type) {
                        PointerEventType.Press -> {
                            isPagerScrollable.value = false
                            XLog.info("shy","Pressed")
                        }
                        PointerEventType.Move -> {
                            isPagerScrollable.value = false
                            XLog.info("shy","Pointer moved")
                        }
                        PointerEventType.Release -> {
                            isPagerScrollable.value = true
                            XLog.info("shy","Released")
                        }
                        else -> {
                            isPagerScrollable.value = true
                            XLog.info("shy","Other event: ${event.type}")
                        }
                    }
                }
            }
        },
    ) {
        items(20) { index ->
            Box(
                modifier = Modifier
                    .size(100.dp)
                    .padding(8.dp)
                    .background(Color.Cyan),
                contentAlignment = Alignment.Center
            ) {
                Text(text = "Item $index")
            }
        }
    }
}

@Composable
private fun MyButton(
    text: String,
    show: MutableState<Boolean>
    ) {
    Box(
        modifier = Modifier
            .background(
                lightColorScheme().primary,
                RoundedCornerShape(8.dp)
            )
            .size(200.dp, 60.dp)
            .clickable {
                show.value = true
            },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = Color.White
        )
    }
}