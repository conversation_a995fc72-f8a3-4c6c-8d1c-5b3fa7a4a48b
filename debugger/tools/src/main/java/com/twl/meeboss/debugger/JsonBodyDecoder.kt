package com.twl.meeboss.debugger

import com.chuckerteam.chucker.api.BodyDecoder
import com.twl.meeboss.common.utils.GsonUtils
import okhttp3.Request
import okhttp3.Response
import okio.ByteString
import org.json.JSONArray
import org.json.JSONObject

/**
 * JSON格式化解码器，用于将压缩的JSON转换为易读的格式化JSON
 */
class JsonBodyDecoder : BodyDecoder {

    override fun decodeRequest(request: Request, body: ByteString): String? {
        return formatJsonIfPossible(body.utf8())
    }

    override fun decodeResponse(response: Response, body: ByteString): String? {
        return formatJsonIfPossible(body.utf8())
    }

    /**
     * 尝试格式化JSON字符串，如果不是有效的JSON则返回null
     */
    private fun formatJsonIfPossible(bodyString: String): String? {
        if (bodyString.isBlank()) {
            return null
        }

        return try {
            // 尝试解析为JSONObject
            if (bodyString.trim().startsWith("{")) {
                val jsonObject = JSONObject(bodyString)
                jsonObject.toString(4) // 4个空格缩进
            }
            // 尝试解析为JSONArray
            else if (bodyString.trim().startsWith("[")) {
                val jsonArray = JSONArray(bodyString)
                jsonArray.toString(4) // 4个空格缩进
            }
            // 不是JSON格式，返回null让Chucker使用默认处理
            else {
                null
            }
        } catch (e: Exception) {
            // 解析失败，不是有效的JSON，返回null让Chucker使用默认处理
            null
        }
    }
}
