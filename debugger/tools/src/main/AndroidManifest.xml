<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <application>
        <activity android:name=".activity.DebuggerUserInfoActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".activity.DebuggerTestActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".activity.DebuggerListActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".activity.DebuggerEnvironmentConfigActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".activity.DebuggerChatActivity"
            android:windowSoftInputMode="adjustNothing|stateHidden"
            android:screenOrientation="portrait"/>
        <activity android:name=".activity.DebuggerAnimateActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".nestscroll.DebuggerNestScrollActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".activity.DebuggerBitmapActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".activity.DebuggerLottieActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".activity.DebuggerMarkdownActivity"
            android:screenOrientation="portrait"/>
    </application>
</manifest>