plugins {
    alias(libs.plugins.twl.android.library)
    alias(libs.plugins.twl.android.library.compose)
}

android {
    namespace = "com.twl.meeboss.debugger"
}

dependencies {
    api(projects.debugger.statement)
    implementation(projects.core.network)
    implementation(projects.feature.login)
    implementation(projects.feature.boss)
    implementation(projects.feature.geek)
    implementation(projects.feature.chat)
    implementation(projects.feature.setting)
    implementation(projects.feature.webview)
    implementation(projects.feature.loginExport)
    implementation(projects.feature.bossExport)
    implementation(projects.feature.geekExport)
    implementation(projects.feature.chatExport)
    implementation(projects.feature.settingExport)
    implementation(projects.feature.webviewExport)
    implementation(libs.nativekit)
    debugImplementation(libs.dokit.dokitx)           // DoKit Debug 版本，包含完整功能
    releaseImplementation(libs.dokit.dokitx.no.op)   // DoKit Release 版本，空实现
    debugImplementation(libs.chucker)                // Chucker Debug 版本
    releaseImplementation(libs.chucker.no.op)        // Chucker Release 版本
    implementation("com.github.fengdai.compose:pulltorefresh:0.2.0")

    implementation(libs.compose.richeditor)
}