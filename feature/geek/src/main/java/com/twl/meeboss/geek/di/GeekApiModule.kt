package com.twl.meeboss.geek.di

import com.twl.meeboss.core.network.getService
import com.twl.meeboss.geek.api.GeekApi
import com.twl.meeboss.geek.api.GeekJobApi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * @author: 冯智健
 * @date: 2024年07月11日 18:06
 * @description:
 */
@Module
@InstallIn(SingletonComponent::class)
class GeekApiModule {

    @Provides
    fun provideGeekApi(): GeekApi = getService(GeekApi::class.java)

    @Provides
    fun provideGeekJobApi(): GeekJobApi = getService(GeekJobApi::class.java)
}