package com.twl.meeboss.geek.module.me.myjob

import com.twl.meeboss.base.R
import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.ktx.getDefaultPageSize
import com.twl.meeboss.base.model.enumeration.CollectStatus
import com.twl.meeboss.base.repos.BaseBusinessRepository
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.base.model.job.JobItemResult
import com.twl.meeboss.geek.module.me.myjob.model.GeekMyJobsType
import com.twl.meeboss.geek.repos.GeekJobRepository
import com.twl.meeboss.geek.utils.GeekPointReporter
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class GeekMyJobsViewModel @Inject constructor(
    private val repos: GeekJobRepository,
    private val baseRepos: BaseBusinessRepository,
) : BaseMviViewModel<GeekMyJobsUiState, GeekMyJobsUiIntent>() {

    var tabIndex = 0

    override fun initUiState() = GeekMyJobsUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is GeekMyJobsUiIntent.ResetTabPage -> resetTab()
            is GeekMyJobsUiIntent.SwitchTabPage -> switchTab(intent.tabIndex)
            is GeekMyJobsUiIntent.RemoveJobItem -> removeSavedJobItem(intent.tabIndex, intent.jobItem)
            is GeekMyJobsUiIntent.RemoveInProgressJobItem -> removeInProgressJobItem(intent.tabIndex, intent.jobItem)
            is GeekMyJobsUiIntent.ArchiveJobItem -> {
                GeekPointReporter.myJobsSet(3,intent.jobItem.jobId.notNull())
                archiveJobItem(CollectStatus.TO_COLLECTED, intent.tabIndex, intent.jobItem)
            }
            is GeekMyJobsUiIntent.RemoveArchiveJobItem ->{
                GeekPointReporter.myJobsSet(2,intent.jobItem.jobId.notNull())
                archiveJobItem(CollectStatus.TO_UNCOLLECTED, intent.tabIndex, intent.jobItem)
            }
            is GeekMyJobsUiIntent.AddFriendSuccess -> addFriendSuccess(intent.securityId)
            is GeekMyJobsUiIntent.RefreshJobList -> getGeekMyJobList(intent.tabIndex,true)
            is GeekMyJobsUiIntent.LoadMoreJobList -> getGeekMyJobList(intent.tabIndex,false)
        }
    }

    private fun resetTab() {
        val titleList = listOf(
            R.string.common_saved.toResourceString(),
            R.string.common_in_process.toResourceString(),
            R.string.common_archived.toResourceString()
        )

        val stateList = List(titleList.size) { _ ->
            GeekMyJobsListUiState()
        }

        sendUiState {
            copy(
                tabIndex = <EMAIL>,
                jobTitleList = titleList,
                geekMyJobsUiStateList = stateList
            )
        }
        getGeekMyJobList(tabIndex, true)
        getInProcessTip()
    }

    private fun switchTab(tabIndex: Int) {
        if (uiStateFlow.value.tabIndex == tabIndex) {
            return
        }
        if (!isValidIndex(tabIndex)) {
            return
        }
        sendUiState {
            copy(
                tabIndex = tabIndex,
                )
        }
        getGeekMyJobList(tabIndex, true)
    }

    private fun getInProcessTip() {
        requestData(
            enableLoadState = false,
            request = {
                baseRepos.getInProcessTip()
            },
            success = {
                val myJobsUiStateList = uiStateFlow.value.geekMyJobsUiStateList.toMutableList().mapIndexed { index, geekMyJobsListUiState ->
                    if (index == GeekMyJobsType.IN_PROGRESS.index) {
                        geekMyJobsListUiState.copy(tipText = if(it?.status == 1 && it.desc.isNotBlank()) it.desc else "")
                    } else {
                        geekMyJobsListUiState
                    }
                }

                sendUiState {
                    copy(
                        geekMyJobsUiStateList = myJobsUiStateList
                    )
                }
            }
        )
    }

    fun onCloseTipClick(tabIndex: Int) {
        if (tabIndex == GeekMyJobsType.IN_PROGRESS.index) {
            requestData(
                enableLoadState = false,
                request = {
                    baseRepos.closeInProcessTip()
                }
            )
        }
        sendUiState {
            val list = geekMyJobsUiStateList.toMutableList()
            list[tabIndex] = list[tabIndex].copy(
                tipText = ""
            )
            copy(
                geekMyJobsUiStateList = list
            )
        }
    }


    /**
     * 取消收藏
     */
    private fun removeJobItem(tabIndex: Int, removeItem: JobItemResult) {
        if (!isValidIndex(tabIndex)) {
            return
        }

        sendUiState {
            val jobItemList = geekMyJobsUiStateList[tabIndex].listState.list.filter { jobItem ->
                jobItem.securityId != removeItem.securityId
            }

            val list = geekMyJobsUiStateList.toMutableList()
            list[tabIndex] = list[tabIndex].copy(
                listState = list[tabIndex].listState.refreshSuccess(jobItemList, list[tabIndex].listState.hasMore)
            )
            copy(
                geekMyJobsUiStateList = list
            )
        }
    }


    /**
     * 取消收藏
     */
    private fun removeSavedJobItem(tabIndex: Int, removeItem: JobItemResult) {
        if(tabIndex == GeekMyJobsType.SAVED.index) {
            GeekPointReporter.myJobsSet(1,removeItem.jobId.notNull())
        } else {
            GeekPointReporter.myJobsSet(2,removeItem.jobId.notNull())
        }
        removeItem.securityId.takeIf { !it.isNullOrBlank() }?.let {
            requestData(
                request = {
                    repos.collectJob(it, CollectStatus.TO_UNCOLLECTED)
                },
                success = {
                    removeJobItem(tabIndex, removeItem)
                },
                fail = {
                    T.ss(it.message)
                },
            )
        }
    }

    /**
     * 进行中删除职位
     */
    private fun removeInProgressJobItem(tabIndex: Int, removeItem: JobItemResult) {
        if (!isValidIndex(tabIndex)) {
            return
        }
        GeekPointReporter.myJobsSet(2,removeItem.jobId.notNull())

        removeItem.securityId.takeIf { !it.isNullOrBlank() }?.let {
            requestData(
                request = {
                    repos.geekInteractionRemoveInProgress(it)
                },
                success = {
                    removeJobItem(tabIndex, removeItem)
                },
                fail = {
                    T.ss(it.message)
                },
            )
        }
    }

    /**
     * 归档职位添加 & 删除
     */
    private fun archiveJobItem(
        archiveStatus: @CollectStatus Int, // 0：收藏；1：删除
        tabIndex: Int,
        removeItem: JobItemResult
    ) {
        if (!isValidIndex(tabIndex)) {
            return
        }

        removeItem.securityId.takeIf { !it.isNullOrBlank() }?.let {
            requestData(
                request = {
                    repos.geekInteractionArchive(it, archiveStatus)
                },
                success = {
                    removeJobItem(tabIndex, removeItem)
                },
                fail = {
                    T.ss(it.message)
                },
            )
        }
    }

    /**
     * 加好友成功之后刷新列表状态
     */
    private fun addFriendSuccess(securityId: String) {
        var hasMatchItem = false
        val geekMyJobsUiStateList = uiStateFlow.value.geekMyJobsUiStateList.toMutableList()
        for (index in 0 until geekMyJobsUiStateList.size) {
            val uiState = geekMyJobsUiStateList[index]
            val jobItemList = uiState.listState.list.map {
                if (it.securityId == securityId) {
                    hasMatchItem = true
                    it.copy(
                        friended = true
                    )
                } else {
                    it
                }
            }
            geekMyJobsUiStateList[index] = uiState.copy(listState = uiState.listState.refreshData(list = jobItemList))
        }
        if (hasMatchItem) {
            sendUiState {
                copy(
                    geekMyJobsUiStateList = geekMyJobsUiStateList
                )
            }
        }
    }

    private fun isValidIndex(tabIndex: Int):Boolean {
        return tabIndex in 0..2
    }

    private fun getGeekMyJobList(tabIndex:Int, isRefresh:Boolean) {
        launcherOnIO {
            uiStateFlow.value.geekMyJobsUiStateList[tabIndex].let {

                requestData(
                    enableLoadState = false,
                    request = {
                        when(tabIndex) {
                            GeekMyJobsType.IN_PROGRESS.index -> {
                                repos.geekInteractionInProcess(it.lastId, getDefaultPageSize(), 0)
                            }
                            GeekMyJobsType.ARCHIVED.index -> {
                                repos.geekInteractionInProcess(it.lastId, getDefaultPageSize(), 1)
                            }
                            else -> {
                                repos.geekInteractionFavorites(it.lastId, getDefaultPageSize())
                            }
                        }
                    },
                    success = {
                        sendUiState {
                            val list = geekMyJobsUiStateList.toMutableList()
                            list[tabIndex] = list[tabIndex].copy(
                                lastId = it?.lastId ?: "",
                                listState = if (isRefresh) {
                                    list[tabIndex].listState.refreshSuccess(
                                        it?.content ?: listOf(), it?.hasMore ?: false
                                    )
                                } else {
                                    list[tabIndex].listState.loadMoreSuccess(
                                        it?.content ?: listOf(), it?.hasMore ?: false
                                    )
                                },
                            )
                            copy(
                                geekMyJobsUiStateList = list
                            )
                        }
                    },
                    fail = {
                        sendUiState {
                            val list = geekMyJobsUiStateList.toMutableList()
                            list[tabIndex] = list[tabIndex].copy(
                                listState = if (isRefresh) {
                                    list[tabIndex].listState.refreshFail()
                                } else {
                                    list[tabIndex].listState.loadMoreFail()
                                },
                            )
                            copy(
                                geekMyJobsUiStateList = list
                            )
                        }
                    }
                )
            }
        }
    }

}


data class GeekMyJobsUiState(
    val tabIndex: Int = 0,
    val jobTitleList: List<String> = listOf(""),
    val geekMyJobsUiStateList: List<GeekMyJobsListUiState> = listOf(),
) : IUiState

data class GeekMyJobsListUiState(
    val tipText: String = "",
    val lastId: String = "",
    val listState: XRefreshListState<JobItemResult> = XRefreshListState.getDefault(),
)

sealed class GeekMyJobsUiIntent : IUiIntent {
    data object ResetTabPage : GeekMyJobsUiIntent()
    data class SwitchTabPage(val tabIndex: Int): GeekMyJobsUiIntent()
    data class RemoveJobItem(val tabIndex: Int, val jobItem: JobItemResult): GeekMyJobsUiIntent()
    data class RemoveInProgressJobItem(val tabIndex: Int, val jobItem: JobItemResult): GeekMyJobsUiIntent()
    data class ArchiveJobItem(val tabIndex: Int, val jobItem: JobItemResult): GeekMyJobsUiIntent()
    data class RemoveArchiveJobItem(val tabIndex: Int, val jobItem: JobItemResult): GeekMyJobsUiIntent()
    data class AddFriendSuccess(val securityId:String): GeekMyJobsUiIntent()
    data class RefreshJobList(val tabIndex: Int): GeekMyJobsUiIntent()
    data class LoadMoreJobList(val tabIndex: Int): GeekMyJobsUiIntent()
}