package com.twl.meeboss.geek.module.job.detail

import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.geek.R

/**
 * @author: musa on 2025/06/10
 * @e-mail: <EMAIL>
 * @desc: 签证担保说明弹窗
 */
class VisaSponsorshipDescDialog : CommonBottomDialogFragment() {
    private val visaSponsored by lazy {
        arguments?.getInt(VISA_SPONSORED) ?: 0
    }

    companion object{
        private const val VISA_SPONSORED = "visaSponsored"
        fun newInstance(visaSponsored: Int) = VisaSponsorshipDescDialog().apply {
            arguments = Bundle().apply {
                putInt(VISA_SPONSORED, visaSponsored)
            }
        }
    }

    @Composable
    override fun DialogContent() {
        VisaSponsorshipDescContent(
            title = visaSponsored.visaSponsorshipTitle(),
            desc = visaSponsored.visaSponsorshipDesc(),
            onClose = { dismissSafely() }
        )
    }

    @Composable
    private fun Int.visaSponsorshipTitle() = when(this){
        1 -> R.string.job_seeker_job_detail_visa_sponsored
        2 -> R.string.job_seeker_job_detail_visa_sponsored_no
        else -> R.string.job_seeker_job_detail_visa_sponsored_likely
    }.run {
        stringResource(this)
    }

    @Composable
    private fun Int.visaSponsorshipDesc() = when(this){
        1 -> R.string.job_seeker_job_detail_visa_sponsored_hint
        2 -> R.string.job_seeker_job_detail_visa_sponsored_no_hint
        else -> R.string.job_seeker_job_detail_visa_sponsored_likely_hint
    }.run {
        stringResource(this)
    }

    @Composable
    private fun VisaSponsorshipDescContent(onClose: () -> Unit, title: String, desc: String) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
                )
        ) {
            Image(
                modifier = Modifier
                    .padding(vertical = 20.dp, horizontal = 16.dp)
                    .noRippleClickable { onClose() },
                painter = painterResource(id = R.drawable.ui_dailog_close),
                contentDescription = "close"
            )

            Image(
                painter = painterResource(R.drawable.ui_icon_visa_desc),
                contentDescription = "visa desc",
                modifier = Modifier
                    .padding(top = 12.dp)
                    .align(Alignment.CenterHorizontally)
            )

            Text(
                text = title,
                style = TextStyle(
                    fontSize = 24.sp,
                    fontWeight = FontWeight(590),
                    color = COLOR_222222,
                ),
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(top = 16.dp, bottom = 12.dp)
                    .padding(horizontal = 16.dp)
            )

            Text(
                text = desc,
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 20.sp,
                    fontWeight = FontWeight(400),
                    color = COLOR_484848,
                    textAlign = TextAlign.Center
                ),
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(bottom = 28.dp)
                    .padding(horizontal = 16.dp)
            )
        }
    }

    @Preview
    @Composable
    private fun PreviewVisaSponsorshipDescContent() {
        val visaSponsored = 3
        VisaSponsorshipDescContent(onClose = {}, title = visaSponsored.visaSponsorshipTitle(), desc = visaSponsored.visaSponsorshipDesc())
    }

}