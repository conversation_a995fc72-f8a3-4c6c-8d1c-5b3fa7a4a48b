package com.twl.meeboss.geek.module.profile.components

import android.app.Activity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.ktx.toShowEndDate
import com.twl.meeboss.base.model.enumeration.AuditStatus
import com.twl.meeboss.base.model.profile.CertificateBean
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.component.XConstraintWidthComponent
import com.twl.meeboss.core.ui.component.bar.XInformationBar
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.module.profile.activity.GeekEditCertificationActivity
import com.twl.meeboss.geek.utils.GeekPointReporter

/**
 * 证书模块
 */
@Composable
internal fun GeekCertificationSection(
    modifier: Modifier = Modifier,
    list: List<CertificateBean>,
    canEdit: Boolean = true,
    hideWhenEmpty: Boolean = false,
    showAll: Boolean = false,
    onDenyReasonClick: (CertificateBean) -> Unit = {},
) {
    var showAllCertification by rememberSaveable {
        mutableStateOf(showAll)
    }
    if (!hideWhenEmpty || list.isNotEmpty()) {
        val context = LocalContext.current
        GeekCommonProfileSection(
            modifier = modifier,
            title = R.string.common_certification,
            emptyHint = R.string.geek_edit_certification_hint,
            onAddClick = {
                if (canEdit) {
                    GeekPointReporter.f3tabProfileAdd(6)
                    if(list.size >= 10){
                        T.ss(R.string.jobseeker_profile_certification_max_toast)
                        return@GeekCommonProfileSection
                    }
                    GeekEditCertificationActivity.startActivity(context as Activity, null)
                }
            },
            canAdd = canEdit,
            count = list.size,
            showDivider = false
        ) {
            if (showAllCertification) {
                list.forEachIndexed { index, it ->
                    GeekCertificationItem(
                        bean = it,
                        canEdit = canEdit,
                        modifier = Modifier
                            .padding(bottom = if (index != list.size - 1) 32.dp else 0.dp)
                            .noRippleClickable {
                                if (canEdit) {
                                    GeekEditCertificationActivity.startActivity(
                                        context as Activity,
                                        it
                                    )
                                }
                            },
                        onDenyReasonClick = onDenyReasonClick
                    )
                }
            } else {
                list.take(3).forEachIndexed { index, it ->
                    GeekCertificationItem(
                        bean = it,
                        canEdit = canEdit,
                        modifier = Modifier
                            .padding(bottom = if (index != list.size - 1) 32.dp else 0.dp)
                            .noRippleClickable {
                                if (canEdit) {
                                    GeekEditCertificationActivity.startActivity(
                                        context as Activity,
                                        it
                                    )
                                }
                            },
                        onDenyReasonClick = onDenyReasonClick
                    )
                }
                if (!showAllCertification && list.size > default_list_count) {
                    GeekShowAllContent(content = R.string.geek_view_all_certifications, count = list.size) {
                        showAllCertification = true
                    }
                }
            }
        }
    }
}

@Composable
fun GeekCertificationItem(
    modifier: Modifier = Modifier,
    bean: CertificateBean,
    canEdit: Boolean = true,
    onDenyReasonClick: (CertificateBean) -> Unit = {},
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            XConstraintWidthComponent(
                modifier = Modifier
                    .weight(1f),
                startComponent = {
                    Text(
                        text = bean.name ?: "",
                        maxLines = 5,
                        overflow = TextOverflow.Ellipsis,
                        fontSize = 16.sp, color = Black222222,
                        lineHeight = 22.sp, fontWeight = FontWeight.SemiBold
                    )
                },
                endComponent = {
                    if (bean.newChanged) {
                        GeekProfileNewTag()
                    }
                }
            )
            if (canEdit) {
                Image(painter = painterResource(id = R.drawable.ui_little_gray_arrow), contentDescription = "")
            }
        }
        Text(
            text = if(bean.expirationYear >0) "${stringResource(id = R.string.common_expires)} ${toShowEndDate(endYear = bean.expirationYear, endMonth = bean.expirationMonth)}" else "",
            fontSize = 14.sp,
            color = Black888888,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.padding(top = 4.dp),
            lineHeight = 20.sp,
        )
        if (bean.auditStatus == AuditStatus.AUDIT_FAIL) {
            XInformationBar(
                modifier = Modifier.padding(top = 20.dp),
                content = stringResource(id = R.string.geek_your_certification_audit_failed)
            ) {
                onDenyReasonClick(bean)
            }
        } else if (bean.auditStatus == AuditStatus.AUDIT_UNDER_REVIEW) {
            XInformationBar(
                modifier = Modifier.padding(top = 20.dp),
                content = stringResource(R.string.job_seeker_profile_under_review_hint_cert),
                showIcon = false
            )
        }
    }
}

@Preview
@Composable
private fun PreviewGeekEditProfileCertification() {
    Column(modifier = Modifier
        .fillMaxSize()
        .background(Color.White)) {
        GeekCertificationSection(
            modifier = Modifier.padding(16.dp),
            list = List(3) {
                CertificateBean(
                    name = stringResource(id = R.string.ui_common_long_place_holder),
                    expirationYear = 2022,
                    expirationMonth = 12,
                    auditStatus = AuditStatus.AUDIT_FAIL,
                    denyReason = "Your certificate review failed."
                )
            }
        )
    }
}

@Preview
@Composable
private fun PreviewGeekEditProfileCertificationEmpty() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        GeekCertificationSection(
            modifier = Modifier.padding(16.dp),
            list = emptyList()
        )
    }
}

