package com.twl.meeboss.geek.module.job.recommend.preview

import com.twl.meeboss.base.model.PageList
import com.twl.meeboss.base.model.job.JobItemResult
import java.util.UUID

/**
 * @author: 冯智健
 * @date: 2024年07月11日 13:56
 * @description:
 */
object JobMockData {

    private const val TOTAL_PAGE = 3
    private val lastIdList = List(TOTAL_PAGE - 1) {
        UUID.randomUUID().toString()
    }

    fun getJobList(page: Int, pageSize: Int): PageList<JobItemResult> {
        val list = mutableListOf<JobItemResult>()
        if (page <= TOTAL_PAGE) {
            repeat(pageSize) {
                list.add(
                    JobPreviewData.jobItemResult.copy(
                        securityId = UUID.randomUUID().toString(),
                        groupBy = if (it in 0..1) "2024.08.06" else "2024.08.05"
                    )
                )
            }
        }
        return PageList(
            content = list,
            hasMore = page < TOTAL_PAGE,
            page = page,
            pageSize = pageSize
        )
    }

    fun getJobList(lastId: String?, pageSize: Int): PageList<JobItemResult> {
        val list = mutableListOf<JobItemResult>()
        repeat(pageSize) {
            list.add(
                JobPreviewData.jobItemResult.copy(
                    securityId = UUID.randomUUID().toString(),
                    groupBy = if (it in 0..1) "2024.08.06" else "2024.08.05"
                )
            )
        }
        return lastId?.let {
            val index = lastIdList.indexOf(it)
            if (index < 0) {
                PageList(content = null)
            } else {
                val nextIndex = index + 1
                PageList(
                    content = list,
                    hasMore = index < TOTAL_PAGE - 2,
                    lastId = if (nextIndex < lastIdList.size) lastIdList[nextIndex] else null,
                    pageSize = pageSize
                )
            }
        } ?: run {
            PageList(
                content = list,
                hasMore = true,
                lastId = lastIdList[0],
                pageSize = pageSize
            )
        }
    }
}