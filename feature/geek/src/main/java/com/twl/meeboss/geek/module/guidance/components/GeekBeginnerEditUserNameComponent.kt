package com.twl.meeboss.geek.module.guidance.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.R
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.ktx.subString
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.component.textfield.XCommonTextField
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.geek.export.EditPageScene
import com.twl.meeboss.geek.module.guidance.interact.EditUserNameInteract
import com.twl.meeboss.geek.module.guidance.state.GeekBeginnerGuidanceUserNameUiState

@Composable
fun GeekBeginnerEditUserNameComponent(
    modifier: Modifier = Modifier,
    uiState: GeekBeginnerGuidanceUserNameUiState = GeekBeginnerGuidanceUserNameUiState(),
    interact: EditUserNameInteract = EditUserNameInteract()
) {
    Column(modifier = modifier) {
        Column(
            modifier = Modifier
                .weight(1F)
                .verticalScroll(rememberScrollState(), true)
        ) {
            Text(
                modifier = Modifier.padding(top = 12.dp, start = 16.dp, end = 16.dp),
                text = stringResource(id = R.string.common_your_name),
                style = TextStyle(
                    fontSize = 28.sp,
                    fontWeight = FontWeight(600),
                    color = Black222222,
                )
            )
            if (uiState.isInFlow()) {
                FlowStepBar(step = 0)
            }

            //First name
            XCommonTextField(modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 20.dp),
                value = uiState.firstName,
                innerTitle = R.string.common_first_name,
                placeHolder = com.twl.meeboss.geek.R.string.geek_add_your_first_name,
                onValueChange = {
                    it.subString(DefaultValueConstants.MAX_USER_NAME_LENGTH, true).let { name ->
                        interact.onFirstNameChanged(name)
                    }
                })
            //Last name
            XCommonTextField(modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 12.dp),
                value = uiState.lastName,
                innerTitle = R.string.common_last_name,
                placeHolder = com.twl.meeboss.geek.R.string.geek_add_your_second_name,
                onValueChange = {
                    it.subString(DefaultValueConstants.MAX_USER_NAME_LENGTH, true).let { name ->
                        interact.onLastNameChanged(name)
                    }
                })
        }
        when (uiState.scene) {
            EditPageScene.FLOW_PAGE-> {
                XCommonButton(
                    modifier = Modifier.padding(16.dp),
                    text = stringResource(id = R.string.common_next),
                    enabled = uiState.canSave,
                    onClick = interact::onSaveClick
                )
            }

            else -> {
                XCommonButton(
                    modifier = Modifier.padding(16.dp),
                    text = stringResource(id = R.string.common_button_save),
                    enabled = uiState.canSave,
                    onClick = interact::onSaveClick
                )
            }
        }

    }
}