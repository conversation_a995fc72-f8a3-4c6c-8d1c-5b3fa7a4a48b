package com.twl.meeboss.geek.module.job.detail

import android.content.Intent
import android.view.View
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.geometry.Rect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.blankj.utilcode.util.ClipboardUtils
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.constants.BUNDLE_BOOLEAN
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.eventbus.sendStringLiveEvent
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.showRightMoreDialog
import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.chat.export.ChatPageRouter
import com.twl.meeboss.chat.export.ChatServiceRouter
import com.twl.meeboss.chat.export.constant.ReportScene
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.utils.DisposableEffectWithLifecycle
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.export.GeekRouterPath
import com.twl.meeboss.geek.utils.GeekPointReporter
import dagger.hilt.android.AndroidEntryPoint

/**
 * @author: 冯智健
 * @date: 2024年07月12日 21:05
 * @description:
 */
@AndroidEntryPoint
@RouterPage(path = [GeekRouterPath.GEEK_JOB_DETAIL_PAGE])
class GeekJobDetailActivity : BaseMviActivity<GeekJobDetailViewModel>() {

    private var securityId = ""

    private var isFromChat = false

    override val viewModel: GeekJobDetailViewModel by viewModels()
    override fun preInit(intent: Intent) {
        securityId = intent.getStringExtra(GeekPageRouter.BUNDLE_SECURITY_ID) ?: ""
        isFromChat = intent.getBooleanExtra(BUNDLE_BOOLEAN, false)
    }

    override fun initData() {
        liveEventBusObserve(EventBusKey.GEEK_ADD_FRIEND_SUCCESS) { securityId: String ->
            if (viewModel.uiStateFlow.value.jobDetail?.securityId == securityId) { //添加好友成功，更新数据
                //因为jd接口返回的securityId和外面传进来的securityId不同,所以只能在jd页做一次中转
                sendStringLiveEvent(EventBusKey.JD_GEEK_ADD_FRIEND_SUCCESS, this.securityId)
            }
        }
    }

    @Composable
    override fun ComposeContent() {
        val vm: GeekJobDetailViewModel = viewModel()
        val uiState by vm.uiStateFlow.collectAsStateWithLifecycle()
        val loadState by vm.loadUiStateFlow.collectAsStateWithLifecycle()
        if (uiState.finishPage) {
            finish()
        }
        DisposableEffectWithLifecycle(
            onStart = {
                vm.sendUiIntent(GeekJobDetailUiIntent.GetJobDetail(securityId))
            }
        )
        GeekJobDetailPage(
            loadState = loadState.loadState,
            scrollState = uiState.scrollState,
            jobDetail = uiState.jobDetail,
            collected = uiState.collected,
            jobOffline = uiState.jobOffline,
            jobOfflineMessage = uiState.jobOfflineMessage,
            onShowCompanyDetail = {
                GeekPageRouter.jumpToGeekCompanyDetailActivity(this, it)
            },
            onCloseTabClick = {
                vm.sendUiIntent(GeekJobDetailUiIntent.CloseTabCard(it))
            },
            retryOnClick = {
                vm.sendUiIntent(GeekJobDetailUiIntent.GetJobDetail(securityId))
            },
            collectOnClick = {
                GeekPointReporter.geekJdPageClick(uiState.jobDetail?.jobDetailJobInfo?.jobId?:"","2")
                vm.sendUiIntent(GeekJobDetailUiIntent.CollectJob(securityId, !uiState.collected))
            },
            chatOnClick = this::onChatOrApplyClick,
            onMoreClick = this::showRightMoreDialog,
        )
    }

    private fun showRightMoreDialog(rect: Rect) {
        showRightMoreDialog(rect, R.layout.geek_job_detail_more_dialog) { contentView, popupWindow ->
            contentView.findViewById<View>(R.id.clReport).setOnClickListener {
                popupWindow.dismiss()
                val jobDetail = viewModel.uiStateFlow.value.jobDetail?.jobDetailJobInfo
                GeekPointReporter.geekJdPageClick(jobDetail?.jobId?:"","3")
                ChatPageRouter.jumpToChatReportActivity(
                    this@GeekJobDetailActivity,
                    securityId,
                    ReportScene.JOB_DETAIL
                )
            }
            contentView.findViewById<View>(R.id.clCopyLink).setOnClickListener {
                popupWindow.dismiss()
                val jobDetail = viewModel.uiStateFlow.value.jobDetail?.jobDetailJobInfo
                val shareLink = jobDetail?.shareLink
                GeekPointReporter.geekCopyJobLink(jobDetail?.jobId?:"", shareLink?:"")
                if (!shareLink.isNullOrBlank()) {
                    ClipboardUtils.copyText(shareLink)
                    T.ss(R.string.common_share_job_copy_link_successfully_toast.toResourceString())
                }
            }
        }
    }

    private fun onChatOrApplyClick() {
        GeekPointReporter.geekJdPageClick(viewModel.uiStateFlow.value.jobDetail?.jobDetailJobInfo?.jobId?:"","1")
        if(isFromChat){
            finish()
            return
        }
        viewModel.uiStateFlow.value.jobDetail?.run {
            ChatServiceRouter.checkStartChat(
                context = this@GeekJobDetailActivity,
                securityId = securityId,
                friendId = this.jobDetailBossInfo?.userId.notNull(),
                friendIdentity = UserConstants.BOSS_IDENTITY,
                isFriend = this.friended,
                source = ChatSource.Detail
            )
        }
    }
}