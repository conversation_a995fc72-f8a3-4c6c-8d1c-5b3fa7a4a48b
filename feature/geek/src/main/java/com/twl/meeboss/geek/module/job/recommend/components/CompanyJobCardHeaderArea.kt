package com.twl.meeboss.geek.module.job.recommend.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.core.ui.component.layout.FlowLayout
import com.twl.meeboss.core.ui.theme.Black020202
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.COLOR_CCCCCC
import com.twl.meeboss.base.model.job.JobItemResult
import com.twl.meeboss.geek.module.job.recommend.preview.JobPreviewData

@Composable
fun CompanyJobCardHeaderArea(
    item: JobItemResult,
) {
    Row(
        modifier = Modifier
            .padding(bottom = 8.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        Text(
            modifier = Modifier.weight(1F),
            text = item.jobTitle ?: "",
            color = Black020202,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )
    }
}

@Preview
@Composable
private fun PreviewJobCardHeaderArea() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier.padding(vertical = 20.dp, horizontal = 16.dp)
        ) {
            CompanyJobCardHeaderArea(JobPreviewData.jobItemResult)
        }
    }
}