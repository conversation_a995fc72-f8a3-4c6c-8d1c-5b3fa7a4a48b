package com.twl.meeboss.geek.module.talentpool.preview

import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.base.model.common.CommonTypeBean
import com.twl.meeboss.export_share.model.JobDetailJobInfo
import com.twl.meeboss.geek.model.result.GeekTalentCompanyItemResult
import java.util.UUID
import kotlin.random.Random

object GeekTalentPoolPreviewData {
    val companyItemResult:GeekTalentCompanyItemResult = GeekTalentCompanyItemResult(
        companyId = UUID.randomUUID().toString(),
        companyName = "Love alters not with his brief hours and weeks. But bears it out even to the edge of doom.If this be error and upon me proved.I never writ, nor no man ever loved.<PERSON> alters not with his brief hours and weeks. But bears it out even to the edge of doom.If this be error and upon me proved.I never writ, nor no man ever loved.<PERSON> alters not with his brief hours and weeks. But bears it out even to the edge of doom.If this be error and upon me proved.I never writ, nor no man ever loved.",
        companyShortName = "gg",
        companyLogoThumbnail = "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png",
        sizeType = OptionBean(code = 10000, name = "20-200人"),
        industry = OptionBean(code = 10001, name = "行业"),
    )

    val jobItem = JobDetailJobInfo(
        jobId = UUID.randomUUID().toString(),
        jobTitle = "jobTitle Love alters not with his brief hours and weeks. But bears it out even to the edge of doom.If this be error and upon me proved.I never writ, nor no man ever loved.Love alters not with his brief hours and weeks. But bears it out even to the edge of doom.If this be error and upon me proved.I never writ, nor no man ever loved.Love alters not with his brief hours and weeks. But bears it out even to the edge of doom.If this be error and upon me proved.I never writ, nor no man ever loved.",
        jobType = List(2) {
            CommonTypeBean(Random.nextLong(), "Part-time")
        },
        locationType = CommonTypeBean(Random.nextLong(), "On-site"),
        address = List(2) {
            CommonTypeBean(Random.nextLong(), "Causeway Bay")
        },
        salaryType = CommonTypeBean(Random.nextLong(), "per year"),
        minSalary = 1200000000000000000,
        maxSalary = 2000000000000000000,
    )

    val companyItemResult2 = companyItemResult.copy(
        companyId = UUID.randomUUID().toString(),
    )
    val companyItemResult3 = companyItemResult.copy(
        companyId = UUID.randomUUID().toString(),
    )

    val companiesList = listOf(
        companyItemResult,
        companyItemResult2,
        companyItemResult3,
    )

}
