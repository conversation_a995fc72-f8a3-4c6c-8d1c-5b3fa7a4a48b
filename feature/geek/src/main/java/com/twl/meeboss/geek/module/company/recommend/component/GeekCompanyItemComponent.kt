package com.twl.meeboss.geek.module.company.recommend.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.glide.GlideImage
import com.skydoves.landscapist.placeholder.placeholder.PlaceholderPlugin
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.theme.COLOR_DDDDDD
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.model.result.GeekSuggestCompanyItemResult
import com.twl.meeboss.geek.module.company.recommend.preview.GeekSuggestCompanyPreviewData

@Composable
fun GeekCompanyItemComponent(
    modifier: Modifier = Modifier
        .fillMaxWidth()
        .clip(RoundedCornerShape(12.dp))
        .background(Color.White),
    companyItem: GeekSuggestCompanyItemResult,
    onCompanyItemClick:()->Unit = {},
    onTalentPoolClick:()->Unit = {},
) {
    Column(
        modifier = modifier
            .noRippleClickable(onClick = onCompanyItemClick)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .align(Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(end = 20.dp)
            ) {
                Text(
                    text = if (companyItem.companyShortName.isNullOrBlank()) (companyItem.companyName?:"") else companyItem.companyShortName,
                    style = TextStyle(
                        fontSize = 18.sp,
                        lineHeight = 24.sp,
                        fontWeight = FontWeight(590),
                        color = Black222222,
                    ),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                )
                Text(
                    modifier = Modifier.padding(top = 4.dp),
                    text = companyItem.industry?.name?.takeIf { it.isNotBlank() }?: stringResource(R.string.common_employer_industry_notprovided),
                    style = TextStyle(
                        fontSize = 13.sp,
                        lineHeight = 18.sp,
                        fontWeight = FontWeight(400),
                        color = COLOR_484848,
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
                Text(
                    modifier = Modifier.padding(top = 4.dp),
                    text = companyItem.sizeType?.name?.takeIf { it.isNotBlank() }?: stringResource(R.string.common_employer_companysize_notprovided),
                    style = TextStyle(
                        fontSize = 13.sp,
                        lineHeight = 18.sp,
                        fontWeight = FontWeight(400),
                        color = COLOR_484848,
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }

            GlideImage(
                imageModel = { companyItem.companyLogoThumbnail },
                imageOptions = ImageOptions(contentDescription = "company logo", contentScale = ContentScale.Inside),
                component = rememberImageComponent {
                    +PlaceholderPlugin.Loading(painterResource(id = R.drawable.base_company_default_logo))
                    +PlaceholderPlugin.Failure(painterResource(id = R.drawable.base_company_default_logo))
                },
                previewPlaceholder = painterResource(id = R.drawable.base_company_default_logo),
                modifier = Modifier
                    .size(48.dp)
                    .clip(RoundedCornerShape(6.dp))
            )


        }

        HorizontalDivider(
            thickness = 1.dp,
            color = BlackEBEBEB,
        )

        Spacer(modifier = Modifier.height(16.dp))
        Row(
            modifier = Modifier
                .padding(start = 16.dp)
                .border(
                    width = 1.dp,
                    color = COLOR_DDDDDD,
                    shape = RoundedCornerShape(12.dp)
                )
                .padding(horizontal = 12.dp, vertical = 7.dp)
                .noRippleClickable(onClick = onTalentPoolClick),
        ) {
            Image(
                modifier = Modifier.size(14.dp),
                painter = painterResource(id = if(companyItem.interested == DefaultValueConstants.GEEK_HAS_JOIN_TALENT_POOL_CODE) R.drawable.base_icon_joined_talent_pool else R.drawable.ui_ic_add),
                contentDescription = ""
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = stringResource(id = if(companyItem.interested == DefaultValueConstants.GEEK_HAS_JOIN_TALENT_POOL_CODE) R.string.geek_talent_pool_joined else R.string.geek_talent_pool_join),
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight(510),
                    color = COLOR_484848,
                )
            )
        }
        Spacer(modifier = Modifier.height(16.dp))

    }
}



@Preview(showBackground = true)
@Composable
private fun PreviewGeekCompanyItemComponent() {
    Column(verticalArrangement = Arrangement.spacedBy(10.dp)) {
    GeekCompanyItemComponent(companyItem = GeekSuggestCompanyPreviewData.companyItemResult)
    GeekCompanyItemComponent(companyItem = GeekSuggestCompanyPreviewData.companyItemResult.copy(industry = null, sizeType = null))
    GeekCompanyItemComponent(companyItem = GeekSuggestCompanyPreviewData.companyItemResult.copy( sizeType = null))
    GeekCompanyItemComponent(companyItem = GeekSuggestCompanyPreviewData.companyItemResult.copy(industry = null))
    GeekCompanyItemComponent(companyItem = GeekSuggestCompanyPreviewData.companyItemResult.copy(interested = 1, comment = "comment"))
    }
}
