package com.twl.meeboss.geek.module.talentpool.bottomsheet

import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.relocation.BringIntoViewRequester
import androidx.compose.foundation.relocation.bringIntoViewRequester
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.glide.GlideImage
import com.skydoves.landscapist.placeholder.placeholder.PlaceholderPlugin
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.ktx.stringResourceWithOption
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.component.textfield.XDescOutlineTextField
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.geek.R
import kotlinx.coroutines.launch

class GeekJoinTalentPoolCommentBottomSheet:CommonBottomDialogFragment() {

    private var mSoftInputMode: Int? = null

    val companyLogo:String by lazy {
        arguments?.getString("companyLogo") ?: ""
    }
    val companyName:String by lazy {
        arguments?.getString("companyName") ?: ""
    }
    val originComment:String by lazy {
        arguments?.getString("originComment") ?: ""
    }
    private var onSaveClick: ((String) -> Unit)? = null

    companion object {
        fun newInstance(
            companyLogo:String = "",
            companyName:String = "",
            originComment:String = "",
            onSaveClick: (String) -> Unit = {},
        ) = GeekJoinTalentPoolCommentBottomSheet().apply {
            this.onSaveClick = onSaveClick
            arguments = Bundle().apply {
                putString("companyLogo", companyLogo)
                putString("companyName", companyName)
                putString("originComment", originComment)
            }
        }
    }

    @Composable
    override fun DialogContent() {
        GeekJoinTalentPoolComponent(
            companyLogo = companyLogo,
            companyName = companyName,
            originComment = originComment,
            onCloseClick = {
                dismissSafely()
            },
            onSaveClick = { result ->
                onSaveClick?.invoke(result)
            }
        )
    }

}

@Composable
fun GeekJoinTalentPoolComponent(
    companyLogo:String = "",
    companyName:String = "",
    originComment:String = "",
    onCloseClick: () -> Unit = {},
    onSaveClick: (String) -> Unit = {},
) {
    val textFieldContent: MutableState<String> = rememberSaveable { mutableStateOf("") }
    val saveEnable = remember { mutableStateOf(false) }
    LaunchedEffect(originComment) {
        textFieldContent.value = originComment
        saveEnable.value = originComment.length <= DefaultValueConstants.MAX_TALENT_POOL_COMMENT
    }


    Surface(
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        color = Color.White
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            Column {
                Image(painter = painterResource(id = R.drawable.ui_dailog_close),
                    modifier = Modifier
                        .padding(start = 16.dp, top = 20.dp)
                        .size(24.dp)
                        .align(Alignment.Start)
                        .noRippleClickable {
                            onCloseClick()
                        },
                    contentDescription = "Close dialog")

                HorizontalDivider(modifier = Modifier
                    .padding(top = 16.dp)
                    .height(1.dp)
                    .fillMaxWidth(), color = COLOR_F5F5F5
                )
            }

            Column(
                modifier = Modifier
                    .weight(1f)
                    .verticalScroll(rememberScrollState())
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(color = COLOR_F5F5F5)
                            .padding(horizontal = 16.dp, vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        GlideImage(
                            imageModel = { companyLogo },
                            imageOptions = ImageOptions(contentDescription = "company logo", contentScale = ContentScale.Inside),
                            component = rememberImageComponent {
                                +PlaceholderPlugin.Loading(painterResource(id = R.drawable.base_company_default_logo))
                                +PlaceholderPlugin.Failure(painterResource(id = R.drawable.base_company_default_logo))
                            },
                            previewPlaceholder = painterResource(id = R.drawable.base_company_default_logo),
                            modifier = Modifier
                                .size(48.dp)
                                .clip(RoundedCornerShape(6.dp))
                        )

                        Spacer(modifier = Modifier.width(16.dp))

                        Text(
                            text = stringResource(R.string.geek_talent_pool_joined_successfully),
                            style = TextStyle(
                                fontSize = 14.sp,
                                lineHeight = 20.sp,
                                fontWeight = FontWeight(400),
                                color = COLOR_484848,
                            )
                        )
                    }
                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        modifier = Modifier.padding(horizontal = 16.dp),
                        text = stringResourceWithOption(R.string.geek_talent_pool_comment),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(510),
                            color = Black222222,
                        )
                    )
                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        modifier = Modifier.padding(horizontal = 16.dp),
                        text = stringResource(R.string.geek_talent_pool_joined_successfully_add_comment, companyName),
                        style = TextStyle(
                            fontSize = 13.sp,
                            lineHeight = 18.sp,
                            fontWeight = FontWeight(400),
                            color = COLOR_484848,
                        )
                    )

                    Spacer(modifier = Modifier.height(20.dp))
                }

                XDescOutlineTextField(
                    value = textFieldContent.value,
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .background(Color.White),
                    maxLength = DefaultValueConstants.MAX_TALENT_POOL_COMMENT,
                    placeHolderText = stringResource(R.string.geek_talent_pool_add_comment_placeholder),
                    onChange = { content, overFlow ->
                        textFieldContent.value = content
                        saveEnable.value = !overFlow
                    },
                )
            }


            Box(modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)) {
                XCommonButton(
                    modifier = Modifier.padding(16.dp),
                    enabled = saveEnable.value,
                    text = stringResource(R.string.common_button_save),
                    onClick = {
                        onSaveClick(textFieldContent.value)
                    }
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewGeekJoinTalentPool() {
    GeekJoinTalentPoolComponent()
}