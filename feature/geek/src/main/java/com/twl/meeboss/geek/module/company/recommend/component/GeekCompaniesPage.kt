package com.twl.meeboss.geek.module.company.recommend.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.twl.meeboss.base.components.list.refresh.XRefreshList
import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.core.ui.component.state.XEmptyContent
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.module.company.recommend.manager.GeekCompaniesCallback
import com.twl.meeboss.geek.module.company.recommend.preview.GeekSuggestCompanyPreviewData
import com.twl.meeboss.geek.module.company.recommend.viewmodel.GeekCompaniesUiState
import com.twl.meeboss.geek.module.job.recommend.components.GeekTitleBar
import com.twl.meeboss.geek.module.talentpool.manager.GeekTalentPoolManager
import com.twl.meeboss.geek.module.talentpool.manager.JoinTalentPoolSource
import com.twl.meeboss.geek.utils.GeekPointReporter

@Composable
fun GeekCompaniesPage(
    modifier:Modifier = Modifier
        .background(Color.White)
        .fillMaxSize(),
    uiState: GeekCompaniesUiState,
    geekTalentPoolManager: GeekTalentPoolManager? = null,
    callback: GeekCompaniesCallback,
) {
    val context = LocalContext.current
    Column(
        modifier = modifier
    ) {
        GeekTitleBar(
            title = stringResource(id = R.string.jobseeker_companies_tab),
            showRightIcon = false,
        )
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(color = COLOR_F5F5F5)
                .padding(start = 12.dp, end = 12.dp)
        ) {
            XRefreshList(
                listState = uiState.listState,
                getUniqueKey = { it.getUniqueKey() },
                onRefresh = callback::onRefresh,
                onLoadMore = callback::onLoadMore,
                emptyContent = {
                    XEmptyContent(
                        text = stringResource(id = R.string.jobseeker_companies_empty_page_hint),
                        emptyButtonText = stringResource(id = R.string.common_refresh),
                        onButtonClick = {
                            callback.onRefresh()
                        }
                    )
                },
                onRetryClick = callback::onRetryClick
            ) { bean, index ->
                Column {
                    if (index == 0) {
                        HorizontalDivider(color = Color.Transparent, thickness = 12.dp)
                    }
                    GeekCompanyItemComponent(
                        companyItem = bean,
                        onCompanyItemClick = {
                            GeekPointReporter.geekSuggestCompanyCardClick(bean.jobCount,bean.companyId?:"",1)
                            bean.companyId?.let { companyId->
                                GeekPageRouter.jumpToGeekCompanyDetailActivity(context, companyId)
                            }
                        },
                        onTalentPoolClick = {
                            bean.companyId?.let { companyId ->
                                if (bean.interested != DefaultValueConstants.GEEK_HAS_JOIN_TALENT_POOL_CODE) {
                                    geekTalentPoolManager?.joinTalentPool(source = JoinTalentPoolSource.COMPANY_LIST, companyId = companyId, companyLogo = bean.companyLogoThumbnail?:"", companyName = bean.companyName?:"")
                                } else {
                                    geekTalentPoolManager?.exitTalentPool(source = JoinTalentPoolSource.COMPANY_LIST, companyId = companyId, companyName = bean.companyName)
                                }
                            }
                        },
                    )
                    HorizontalDivider(color = Color.Transparent, thickness = 12.dp)
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewGeekCompaniesPage() {
    GeekCompaniesPage(
        uiState = GeekCompaniesUiState(
            listState = XRefreshListState.getPreviewDefault(list = GeekSuggestCompanyPreviewData.companiesList),
        ),
        callback = GeekCompaniesCallback.PREVIEW_DEFAULT,
    )
}

