package com.twl.meeboss.geek.module.profile.bottomsheet

import android.app.Dialog
import android.os.Bundle
import android.view.KeyEvent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.res.stringResource
import androidx.core.os.bundleOf
import androidx.lifecycle.ViewModelProvider
import com.twl.meeboss.base.components.skill.CommonEditSkillComponent
import com.twl.meeboss.base.ktx.alertContentChangesDialog
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.module.profile.viewmodel.GeekEditSkillsViewModel

class GeekSelectSkillBottomSheet : CommonBottomDialogFragment() {

    private var onSelectCallback: ((MutableList<HighlightBean>) -> Unit)? = null

    private val defaultList: List<HighlightBean> by lazy {
        arguments?.getSerializable("defaultList") as? List<HighlightBean> ?: emptyList()
    }

    private val closeWhenConfirm: Boolean by lazy {
        arguments?.getBoolean("closeWhenConfirm", true) // 提供默认值 true
            ?: true
    }

    companion object {
        fun newInstance(defaultList: List<HighlightBean>,
                        closeWhenConfirm: Boolean = true,
                        callback: (MutableList<HighlightBean>) -> Unit = {}) = GeekSelectSkillBottomSheet().apply {
            this.onSelectCallback = callback
            arguments = bundleOf(
                "defaultList" to defaultList,
                "closeWhenConfirm" to closeWhenConfirm
            )
        }
    }

    private val mViewModel: GeekEditSkillsViewModel by lazy {
        ViewModelProvider(this)[GeekEditSkillsViewModel::class.java]
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        // 设置回退键监听
        dialog.setOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_DOWN) {
                if (hasChanges()) {
                    showExitDialog()
                    true // 消费掉回退键事件
                } else {
                    false // 不消费回退键事件，让系统处理
                }
            } else {
                false // 其他按键不处理
            }
        }
        return dialog
    }

    @Composable
    override fun DialogContent() {
        val vm = mViewModel
        LaunchedEffect(key1 = Unit) {
            vm.selectedList.value = defaultList.map { it }.toMutableList()
            vm.initialSelectedList.value = defaultList.map { it }.toMutableList()
            vm.getRecommendSkills()
        }
        val recommendList by vm.recommendList.observeAsState(initial = emptyList())
        val selectedList by vm.selectedList.observeAsState(initial = emptyList())
        val searchList by vm.tipsList.observeAsState(initial = emptyList())
        CommonEditSkillComponent(
            title = stringResource(id = R.string.common_skills),
            maxCount = vm.maxSelectedCount,
            selectedList = selectedList,
            recommendList = recommendList,
            searchList = searchList,
            onInputChanged = {
                vm.input = it
                vm.getSuggestSkills()
            },
            placeHolder = R.string.geek_search_your_skills,
            onCloseClick = ::onCloseClick,
            onSaveCallback = ::onSaveCallback,
            onRemoveSkill = ::onRemoveCallback,
            onClickSkill = ::onItemClick
        )
    }

    private fun onSaveCallback(list: MutableList<HighlightBean>) {
        onSelectCallback?.let { it(list) }
        if(closeWhenConfirm){
            dismissSafely()
        }
    }

    private fun onCloseClick() {
        if (hasChanges()) {
            showExitDialog()
        } else {
            dismissSafely()
        }
    }

    private fun hasChanges(): Boolean {
        val currentList = mViewModel.selectedList.value ?: emptyList()
        val initialList = mViewModel.initialSelectedList.value ?: emptyList()
        
        if (currentList.size != initialList.size) return true
        
        val currentUniqueKeys = currentList.map { it.uniqueKey() }.toSet()
        val initialUniqueKeys = initialList.map { it.uniqueKey() }.toSet()
        
        return currentUniqueKeys != initialUniqueKeys
    }

    private fun showExitDialog() {
        val context = requireContext()
        context.alertContentChangesDialog(
            onConfirm = {
                dismissSafely()
            }
        )
    }

    private fun onRemoveCallback(item: HighlightBean) {
        mViewModel.removeSkill(item)
    }

    private fun onItemClick(item: HighlightBean) {
        mViewModel.addSkill(item)
    }
}