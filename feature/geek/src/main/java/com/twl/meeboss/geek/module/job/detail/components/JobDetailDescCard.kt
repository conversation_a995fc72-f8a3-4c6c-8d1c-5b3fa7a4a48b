package com.twl.meeboss.geek.module.job.detail.components

import android.text.Html
import android.util.TypedValue
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.ActionBar.LayoutParams
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.text.HtmlCompat
import com.twl.meeboss.base.view.ExpandTextUtil
import com.twl.meeboss.export_share.preview.JobPreviewPreviewData
import com.twl.meeboss.common.ktx.toPx
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.geek.R

/**
 * @author: 冯智健
 * @date: 2024年07月05日 14:32
 * @description:
 */
@Composable
fun JobDetailDescCard(jobDesc: String, jobDescStyle:String? = null) {
    val context = LocalContext.current
    HorizontalDivider(color = BlackEBEBEB)
    Spacer(modifier = Modifier.height(28.dp))
    Text(
        text = stringResource(id = R.string.job_description),
        color = Black222222,
        fontSize = 22.sp,
        fontWeight = FontWeight.SemiBold
    )

    AndroidView(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 16.dp),
        factory = {
            val tv = TextView(it)
            val layoutParams = LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            tv.layoutParams = layoutParams
            tv.setTextColor(Black484848.toArgb())
            tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15f)
            tv.lineHeight = 22f.toPx
            tv
        },
        update = {
            val expandTextUtil = ExpandTextUtil(context).setMaxLine(8)
            val realText = if (jobDescStyle.isNullOrBlank()) jobDesc else Html.fromHtml(
                translateSortList(jobDescStyle),
                HtmlCompat.FROM_HTML_MODE_COMPACT
            )
            expandTextUtil.show(it, realText)

            /*val expandTextUtil = ExpandTextUtil(context).setMaxLine(8)
            val originStr = "<ol><li>有序列表</li><li>1</li><li>2</li><li>3</li><li>4</li></ol><p></p><p><strong>这行是加粗文本</strong></p><ul><li>无需列表</li><li>1</li><li>2</li><li>3</li><li>4</li><li>3</li><li>3</li></ul><ol><li>有序列表</li><li>有序列表1</li></ol>"
            val realText = Html.fromHtml(
                translateSortList(originStr),
                HtmlCompat.FROM_HTML_MODE_COMPACT
            )
            expandTextUtil.show(it, realText)*/
        }
    )

    Spacer(modifier = Modifier.height(28.dp))
}

fun translateSortList(originHtml: String): String {
    var html = originHtml
    if (html.contains("<ol>")) {
        var start = html.indexOf("<ol>")
        var end = html.indexOf("</ol>", start)
        var count = 1
        while (html.indexOf("<li>", start) > start && html.indexOf("<li>", start) < end) {
            val liStart = html.indexOf("<li>", start)
            val liEnd = html.indexOf("</li>", liStart)
            val builder = StringBuilder(html)
            html = builder.replace(liStart, liStart + 4, "$count. ")
                .replace(liEnd-1, liEnd+4,"<br>")
                .toString()
//            XLog.info("shy", "liEnd:${liEnd},html:${html}")
            count++
            start = html.indexOf("<ol>")
            end = html.indexOf("</ol>", start)
        }
        html = html.replaceFirst("<ol>".toRegex(), "")
        html = html.replaceFirst("</ol>".toRegex(), "")
        if (html.contains("<ol>")) {
            html = translateSortList(html)
        }
//        XLog.info("shy", "result html:${html}")
    }

    return html
}

@Preview
@Composable
private fun PreviewJobDetailDescCard() {
    Column(
        modifier = Modifier
            .background(Color.White)
            .padding(horizontal = 16.dp)
    ) {
        JobPreviewPreviewData.jobDetail.jobDetailJobInfo?.jobDesc?.let {
            JobDetailDescCard(it, JobPreviewPreviewData.jobDetail.jobDetailJobInfo?.jobDescStyle)
        }
    }
}