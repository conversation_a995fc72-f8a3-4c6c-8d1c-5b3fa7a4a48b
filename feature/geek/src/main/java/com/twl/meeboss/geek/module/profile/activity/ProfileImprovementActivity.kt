package com.twl.meeboss.geek.module.profile.activity

import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawing
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.core.view.WindowCompat
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.sendIntLiveEvent
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_666666
import com.twl.meeboss.core.ui.theme.COLOR_888888
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.export.GeekRouterPath
import com.twl.meeboss.geek.export.ResumeAnalysisType
import com.twl.meeboss.geek.model.bean.GeekListExtendType
import com.twl.meeboss.geek.model.bean.ImprovementSuggestions
import com.twl.meeboss.geek.model.bean.SuggestionItem
import com.twl.meeboss.geek.module.profile.bottomsheet.GeekSelectSkillBottomSheet
import com.twl.meeboss.geek.module.profile.viewmodel.ProfileImprovementUiIntent
import com.twl.meeboss.geek.module.profile.viewmodel.ProfileImprovementUiState
import com.twl.meeboss.geek.module.profile.viewmodel.ProfileImprovementViewModel
import com.twl.meeboss.geek.utils.GeekPointReporter
import dagger.hilt.android.AndroidEntryPoint

@RouterPage(path = [GeekRouterPath.GEEK_IMPROVEMENT_SUGGESTION])
@AndroidEntryPoint
class ProfileImprovementActivity : BaseMviActivity<ProfileImprovementViewModel>() {

    override val viewModel: ProfileImprovementViewModel by viewModels()

    override fun preInit(intent: Intent) {
        WindowCompat.setDecorFitsSystemWindows(window, false)

        val data = intent.getIntExtra(EXTRA_IMPROVE_COUNT, 0)
        viewModel.initData(data)
    }

    override fun initData() {
        // 初始化数据，如果需要的话
    }

    override fun onDestroy() {
        super.onDestroy()
        val count = viewModel.uiStateFlow.value.improvementCount
        sendIntLiveEvent(EventBusKey.GEEK_IMPROVEMENT_COUNT, count)
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsState()
        // 使用 systemUiController 控制系统 UI 样式
        val systemUiController = rememberSystemUiController()

        // 设置状态栏颜色透明
        SideEffect {
            systemUiController.setStatusBarColor(
                color = Color.Transparent,
                darkIcons = true
            )
        }
        ProfileImprovementScreen(
            uiState = uiState,
            onSuggestionClick = { suggestion ->
                when (suggestion.tabType) {
                    GeekListExtendType.JS_GUIDE_WORK_EXP,
                    GeekListExtendType.JS_GUIDE_IMPROVE_WORK_EXP -> GeekEditWorkExpActivity.startActivity(this, suggestion.id)

                    GeekListExtendType.JS_GUIDE_AVATAR,
                    GeekListExtendType.JS_GUIDE_NAME,
                    GeekListExtendType.JS_GUIDE_LOCATION -> GeekPageRouter.jumpToGeekPersonalDetailActivity(this)

                    GeekListExtendType.JS_GUIDE_SKILL -> GeekSelectSkillBottomSheet.newInstance(emptyList()) {
                        if (it.isNotEmpty()) {
                            viewModel.sendUiIntent(ProfileImprovementUiIntent.SaveSkills(it))
                        }
                    }.showSafely(this)
                }
                GeekPointReporter.geekImprovementItemClick(suggestion.tabType)
            },
            onUpdateWithResumeClick = {
                GeekPageRouter.jumpToGeekQuickCompleteResumeActivity(this, ResumeAnalysisType.PROFILE_UPDATE, "")
                GeekPointReporter.geekImprovementThirdpartyUpload()
            }
        )
    }

    companion object {
        private const val EXTRA_IMPROVE_COUNT = "extra_improve_count"

        fun intent(context: Context, improveCount: Int) {
            val intent = Intent(context, ProfileImprovementActivity::class.java)
            intent.putExtra(EXTRA_IMPROVE_COUNT, improveCount)
            context.startActivity(intent)
        }
    }
}

@Composable
private fun ProfileImprovementScreen(
    uiState: ProfileImprovementUiState,
    onSuggestionClick: (SuggestionItem) -> Unit,
    onUpdateWithResumeClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    0.1f to Color(0xFFDCF9ED),
                    0.4f to Color(0xFFF5F5F5),
                )
            )
            .padding(WindowInsets.safeDrawing.asPaddingValues())
    ) {
        XTitleBar(showDivider = false)

        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
                .padding(bottom = 24.dp)
        ) {
            val (header, icon, content) = createRefs()

            ImprovementHeader(
                suggestionCount = uiState.improvementCount,
                modifier = Modifier.constrainAs(header) {
                    top.linkTo(parent.top)
                }
            )

            Image(
                painter = painterResource(id = R.drawable.geek_bg_icon_profile_improvement),
                contentDescription = null,
                modifier = Modifier.constrainAs(icon) {
                    top.linkTo(header.bottom, margin = (-10).dp)
                    end.linkTo(parent.end)
                }
            )

            ImprovementList(
                suggestions = uiState.improvementSuggestions,
                onSuggestionClick = onSuggestionClick,
                modifier = Modifier.constrainAs(content) {
                    top.linkTo(header.bottom)
                }
            )
        }

        UpdateWithResumeButton(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(16.dp),
            onClick = onUpdateWithResumeClick
        )
    }
}

@Composable
fun ImprovementHeader(suggestionCount: Int, modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 16.dp, bottom = 8.dp)
    ) {
        Text(
            text = buildAnnotatedString {
                val realCount = suggestionCount.toString()
                val template = stringResource(R.string.job_seeker_profile_improve_guidance_page_title)
                val replacement = "\${improveCount}"
                val start = template.indexOf(replacement)
                if (start >= 0) {
                    append(template.replace(replacement, realCount))
                    addStyle(SpanStyle(color = Secondary), start, start + realCount.length)
                }
            },
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = COLOR_222222
        )

        Text(
            text = stringResource(R.string.job_seeker_profile_improve_guidance_page_hint),
            fontSize = 13.sp,
            color = COLOR_666666,
            modifier = Modifier.padding(top = 8.dp)
        )
    }
}

@Composable
fun ImprovementList(
    suggestions: List<ImprovementSuggestions>,
    onSuggestionClick: (SuggestionItem) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        suggestions.forEach {
            SuggestionTitle(text = it.title)
            SuggestionCard(suggestions = it.items, onClick = onSuggestionClick)
        }
    }
}

@Composable
private fun SuggestionTitle(text: String) {
    Text(
        text = text,
        fontSize = 20.sp,
        fontWeight = FontWeight.SemiBold,
        color = COLOR_222222,
        modifier = Modifier.padding(top = 24.dp, bottom = 16.dp)
    )
}

@Composable
private fun SuggestionCard(
    suggestions: List<SuggestionItem>,
    onClick: (SuggestionItem) -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color.White,
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(modifier = Modifier.padding(horizontal = 12.dp, vertical = 20.dp)) {
            suggestions.forEachIndexed { index, suggestion ->
                if (index > 0) {
                    HorizontalDivider(modifier = Modifier.padding(vertical = 16.dp), color = Color(0xFFEBEBEB))
                }

                if (suggestion.title.isNotEmpty()) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp)
                            .noRippleClickable { onClick(suggestion) },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 绿色圆点
                        Box(
                            modifier = Modifier
                                .size(6.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(Color(0xFF00C389))
                        )

                        Spacer(modifier = Modifier.width(6.dp))

                        Text(
                            text = suggestion.title,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = COLOR_222222
                        )
                    }
                }

                Row(
                    modifier = Modifier.noRippleClickable { onClick(suggestion) },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 文本内容
                    Text(
                        text = suggestion.desc,
                        fontSize = 14.sp,
                        color = COLOR_666666,
                        modifier = Modifier.weight(1f)
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    // 右箭头
                    Icon(
                        painter = painterResource(id = R.drawable.ui_right_arrow),
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = COLOR_888888
                    )
                }
            }
        }
    }
}

@Composable
private fun UpdateWithResumeButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        modifier = modifier,
        shape = CircleShape,
        colors = ButtonDefaults.buttonColors(
            containerColor = Color.Black
        )
    ) {
        Text(
            text = stringResource(R.string.job_seeker_profile_improve_guidance_page_button),
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ProfileImprovementScreenPreview() {
    val mockSuggestions = listOf(
        ImprovementSuggestions(
            title = stringResource(R.string.common_work_experience),
            items = listOf(SuggestionItem(title = "Politecnio di MILANO", desc = "Your work experience description is quite brief. Enriching it will leave a more professional impression on employers."))
        ),
        ImprovementSuggestions(
            title = stringResource(R.string.common_skills),
            items = listOf(SuggestionItem(desc = "Employers value your skills, don't let them go unnoticed!")),
        ),
        ImprovementSuggestions(
            title = stringResource(R.string.geek_personal_details),
            items = listOf(
                SuggestionItem(title = "Name", desc = "Starts with a capital letter appears more professional."),
                SuggestionItem(title = "Avatar", desc = "Upload a real profile picture to enhance profile's appeal to employers."),
                SuggestionItem(title = "Location", desc = "Add your location, as employers find this very useful in hiring."),
            ),
        )
    )

    ProfileImprovementScreen(
        uiState = ProfileImprovementUiState(improvementCount = 5, improvementSuggestions = mockSuggestions),
        onSuggestionClick = {},
        onUpdateWithResumeClick = {}
    )
}
