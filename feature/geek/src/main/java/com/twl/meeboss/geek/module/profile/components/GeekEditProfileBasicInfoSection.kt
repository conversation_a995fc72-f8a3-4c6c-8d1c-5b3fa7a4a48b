package com.twl.meeboss.geek.module.profile.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.glide.GlideImage
import com.twl.meeboss.base.R
import com.twl.meeboss.base.ktx.toFormatPhoneNumber
import com.twl.meeboss.base.model.enumeration.AuditStatus
import com.twl.meeboss.base.model.geek.GeekUserInfoBean
import com.twl.meeboss.core.ui.component.bar.XInformationBar
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.withPlaceHolder
import com.twl.meeboss.geek.export.GeekPageRouter

/**
 * 基本信息模块
 */
@Composable
fun GeekEditProfileBasicInfo(
    modifier: Modifier = Modifier,
    bean: GeekUserInfoBean? = null,
    canEdit: Boolean = true,
    completeBaseInfo:Boolean = false,
    onDenyReasonClick: (GeekUserInfoBean) -> Unit = {},
) {
    val context = LocalContext.current
    Column(modifier = modifier.noRippleClickable {
        if (canEdit) {
            GeekPageRouter.jumpToGeekPersonalDetailActivity(context)
        }
    }) {
        Row(modifier = Modifier.fillMaxWidth()) {
            Text(
                modifier = Modifier.weight(1F),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                text = bean?.getFullName().withPlaceHolder(id = R.string.common_unknown),
                fontSize = 24.sp,
                fontWeight = FontWeight.SemiBold,
                color = Black222222, lineHeight = 38.sp)
            InfoRequiredLabel(modifier = Modifier.padding(top = 10.dp),!completeBaseInfo)
            if (bean?.newChanged == true) {
                GeekProfileNewTag(top = 10.dp)
            }
            Spacer(modifier = Modifier.width(10.dp))
            GlideImage(
                imageModel = {
                    bean?.avatar
                },
                imageOptions = ImageOptions(
                    contentScale = ContentScale.Crop,
                    contentDescription = "avatar"
                ),
                failure = {
                    Image(painter = painterResource(id = R.mipmap.base_avatar_placeholder), contentDescription = "", modifier = Modifier
                        .size(54.dp)
                        .clip(RoundedCornerShape(27.dp)))
                },
                previewPlaceholder = painterResource(id = R.mipmap.base_avatar_placeholder),
                modifier = Modifier
                    .size(54.dp)
                    .clip(RoundedCornerShape(27.dp))
            )
        }
        Row(modifier = Modifier.padding(top = 4.dp)) {
            Column(modifier = Modifier.weight(1F)) {
                Text(
                    text = bean?.email.withPlaceHolder(id = R.string.geek_add_email),
                    fontSize = 13.sp, color = Black484848,
                    maxLines = 1,
                    lineHeight = 14.sp,
                    overflow = TextOverflow.Ellipsis,
                    fontWeight = FontWeight.Normal
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = bean?.phoneNumber.toFormatPhoneNumber(true,bean?.countryCodeVO).withPlaceHolder(id = R.string.geek_add_phone_number),
                    fontSize = 13.sp,
                    lineHeight = 14.sp,
                    color = Black484848,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    fontWeight = FontWeight.Normal
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = bean?.city?.name.withPlaceHolder(id = R.string.geek_add_your_location),
                    fontSize = 13.sp,
                    lineHeight = 14.sp,
                    color = Black484848, maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    fontWeight = FontWeight.Normal
                )
            }
            if (canEdit) {
                Image(
                    painter = painterResource(id = R.drawable.ui_little_gray_arrow),
                    contentDescription = null,
                    modifier = Modifier.align(Alignment.CenterVertically)
                )
            }
        }
        if (bean?.auditStatus == AuditStatus.AUDIT_FAIL) {
            XInformationBar(
                modifier = Modifier.padding(top = 20.dp),
                content = stringResource(id = R.string.geek_your_name_audit_failed)
            ) {
                onDenyReasonClick(bean)
            }
        }
    }
}

@Preview
@Composable
private fun PreviewGeekEditProfileBasicInfo() {
    Column(modifier = Modifier
        .fillMaxSize()
        .background(Color.White)) {
        GeekEditProfileBasicInfo(
            modifier = Modifier.padding(16.dp),
            bean = GeekUserInfoBean(
                firstName = "Trump",
                lastName = "Donald",
                avatar = "https://img2.baidu.com/it/u=1459566101,1482467068&fm=253&fmt=auto&app=138&f=JPEG?w=400&h=400",
                email = "<EMAIL>",
                phoneNumber = "18234567890",
                address = "Beijing China",
                auditStatus = AuditStatus.AUDIT_FAIL
            )
        )
    }
}

@Preview
@Composable
private fun PreviewGeekEditProfileBasicInfoEmpty() {
    Column(modifier = Modifier
        .fillMaxSize()
        .background(Color.White)) {
        GeekEditProfileBasicInfo(
            modifier = Modifier.padding(16.dp),
            bean = GeekUserInfoBean()
        )
    }
}