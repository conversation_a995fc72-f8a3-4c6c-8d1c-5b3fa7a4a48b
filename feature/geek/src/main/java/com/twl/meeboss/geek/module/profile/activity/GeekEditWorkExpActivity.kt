package com.twl.meeboss.geek.module.profile.activity

import android.app.Activity
import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.components.company.CommonSearchCompanyBottomSheet
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.constants.BUNDLE_BOOLEAN
import com.twl.meeboss.base.constants.BUNDLE_OBJECT
import com.twl.meeboss.base.constants.BUNDLE_STRING
import com.twl.meeboss.base.constants.BUNDLE_TYPE
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.alertContentChangesDialog
import com.twl.meeboss.base.ktx.isPresentYear
import com.twl.meeboss.base.ktx.showDeleteDialog
import com.twl.meeboss.base.ktx.showSelectTimeDialog
import com.twl.meeboss.base.ktx.stringResourceWithOption
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.profile.WorkExperienceBean
import com.twl.meeboss.core.ui.theme.Black020202
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.EditPageScene
import com.twl.meeboss.geek.export.GeekRouterPath
import com.twl.meeboss.geek.export.GuidanceType
import com.twl.meeboss.geek.module.guidance.utils.GeekGuidanceEditPointHelper
import com.twl.meeboss.geek.module.guidance.utils.GuidanceEditType
import com.twl.meeboss.geek.module.profile.bottomsheet.GeekEditDescriptionBottomSheet
import com.twl.meeboss.geek.module.profile.bottomsheet.GeekEditDescriptionType
import com.twl.meeboss.geek.module.profile.bottomsheet.GeekJobTitleBottomSheet
import com.twl.meeboss.geek.module.profile.components.GeekEditBottomButtons
import com.twl.meeboss.geek.module.profile.components.GeekEditCommonItem
import com.twl.meeboss.geek.module.profile.components.GeekEditEndDateItem
import com.twl.meeboss.geek.module.profile.viewmodel.GeekEditWorkExpUiIntent
import com.twl.meeboss.geek.module.profile.viewmodel.GeekEditWorkExpUiState
import com.twl.meeboss.geek.module.profile.viewmodel.GeekWorkExpViewModelFactory
import com.twl.meeboss.geek.module.profile.viewmodel.IGeekEditWorkExpViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@RouterPage(path = [GeekRouterPath.GEEK_EDIT_WORK_EXP])
@AndroidEntryPoint
class GeekEditWorkExpActivity() : BaseMviActivity<IGeekEditWorkExpViewModel>() {

    private var pointHelper: GeekGuidanceEditPointHelper? = null
    private var useLocalData: Boolean = false

    @Inject
    lateinit var viewModelFactory: GeekWorkExpViewModelFactory

    override val viewModel: IGeekEditWorkExpViewModel by viewModels {
        viewModelFactory.useLocalData = useLocalData
        viewModelFactory
    }

    override fun preInit(intent: Intent) {
        useLocalData = intent.getBooleanExtra(BUNDLE_BOOLEAN, false)
        val bean = intent.getSerializableExtra(BUNDLE_OBJECT)
        val id = intent.getStringExtra(BUNDLE_STRING)
        if (bean != null) {
            viewModel.sendUiIntent(GeekEditWorkExpUiIntent.Init(bean = bean as WorkExperienceBean, isAdd = false))
        } else if (!id.isNullOrEmpty()) {
            viewModel.sendUiIntent(GeekEditWorkExpUiIntent.InitById(id = id))
        } else {
            viewModel.sendUiIntent(GeekEditWorkExpUiIntent.Init(bean = WorkExperienceBean(), isAdd = true))
        }
        val guidanceType = intent.getSerializableExtra(BUNDLE_TYPE) as? GuidanceType
        guidanceType?.let {
            pointHelper = GeekGuidanceEditPointHelper(GuidanceEditType.Work, EditPageScene.SINGLE_PAGE, it)
            pointHelper?.pageShow()
        }
    }

    override fun initData() {
        viewModel.saveResult.observe(this) {
            finish()
        }

        viewModel.deleteResult.observe(this) {
            finish()
        }
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        GeekEditWorkExpContent(uiState, ::onClickJobTitle,
            ::onCompanyNameClick,
            ::onFromClick,
            ::onToCurrentChecked,
            ::onToClick,
            ::onDescriptionClick,
            ::onDeleteClick,
            ::onSaveClick)
    }


    private fun onClickJobTitle() {
        withBean {
            GeekJobTitleBottomSheet.newInstance(HighlightBean(code = this.jobCategoryCode, name = this.jobTitle?:"")) {
                viewModel.sendUiIntent(GeekEditWorkExpUiIntent.UpdateJobTitle(it.name,it.code))
            }.showSafely(this@GeekEditWorkExpActivity)
        }
    }

    private fun onCompanyNameClick() {
        withBean {
            val companyName = this.companyName ?: ""
            CommonSearchCompanyBottomSheet.newInstance(hasCustomItem = true,
                defaultInputValue = if (companyName.isNotBlank()) TextFieldValue(
                    text = companyName,
                    selection = TextRange(companyName.length)
                ) else null) { _, item ->
                viewModel.sendUiIntent(GeekEditWorkExpUiIntent.UpdateCompanyName(item.companyName,item.companyId))
            }.showSafely(this@GeekEditWorkExpActivity)
        }

    }

    private fun onFromClick() {
        withBean {
            showSelectTimeDialog(R.string.common_from.toResourceString(),this.startMonth, this.startYear) { month, year ->
                viewModel.sendUiIntent(GeekEditWorkExpUiIntent.UpdateFrom(year,month))
            }
        }

    }

    private fun onToClick() {
        withBean {
            showSelectTimeDialog(R.string.common_to.toResourceString(),this.endMonth, this.endYear) { month, year ->
                viewModel.sendUiIntent(GeekEditWorkExpUiIntent.UpdateTo(year,month))
            }
        }

    }

    private fun onToCurrentChecked(isChecked:Boolean){
        if (isChecked) {
            viewModel.sendUiIntent(GeekEditWorkExpUiIntent.UpdateTo(DefaultValueConstants.PRESENT,DefaultValueConstants.PRESENT_MONTH))

        } else {
            viewModel.sendUiIntent(GeekEditWorkExpUiIntent.UpdateTo(0,0))

        }
    }

    private fun onDescriptionClick() {
        withBean {
            GeekEditDescriptionBottomSheet.newInstance(description ?: "", GeekEditDescriptionType.JobDescription()) {
                viewModel.sendUiIntent(GeekEditWorkExpUiIntent.UpdateDescription(it))
            }.showSafely(this@GeekEditWorkExpActivity)
        }
    }

    private fun onDeleteClick() {
        showDeleteDialog(title = R.string.geek_delete_experience_dialog_title.toResourceString(),
            content = R.string.geek_delete_experience_dialog_content.toResourceString(),
            confirmCallback = {
            withBean {
                viewModel.sendUiIntent(GeekEditWorkExpUiIntent.Delete)
            }
        })
    }

    private fun onSaveClick() {
        withBean {
            viewModel.sendUiIntent(GeekEditWorkExpUiIntent.Save)
        }
        pointHelper?.saveClick()
    }


    private fun withBean(callback: WorkExperienceBean.() -> Unit) {
        viewModel.uiStateFlow.value.bean.run {
            callback()
        }
    }

    override fun onBackPressed() {
        pointHelper?.backClick()

        if (viewModel.uiStateFlow.value.hasChange()) {
            showExitDialog()
            return
        }
        super.onBackPressed()
    }

    private fun showExitDialog() {
        alertContentChangesDialog(
            onConfirm = {
                finish()
            }
        )
    }

    companion object {

        fun startActivity(
            context: Activity,
            bean: WorkExperienceBean?,
            guidanceType: GuidanceType? = null,
            localData: Boolean = false
        ) {
            Intent(context, GeekEditWorkExpActivity::class.java).apply {
                putExtra(BUNDLE_OBJECT, bean)
                putExtra(BUNDLE_TYPE, guidanceType)
                putExtra(BUNDLE_BOOLEAN, localData)
                context.startActivity(this)
            }
        }

        fun startActivity(context: Activity, workId: String) {
            Intent(context, GeekEditWorkExpActivity::class.java).apply {
                putExtra(BUNDLE_STRING, workId)
                context.startActivity(this)
            }
        }
    }
}

@Composable
fun GeekEditWorkExpContent(uiState: GeekEditWorkExpUiState = GeekEditWorkExpUiState(),
                           onJobTitleClick: () -> Unit = {},
                           onCompanyNameClick: () -> Unit = {},
                           onFromClick: () -> Unit = {},
                           onToCurrentChecked:(Boolean)->Unit = {},
                           onToClick: () -> Unit = {},
                           onDescriptionClick: () -> Unit = {},
                           onDeleteClick: () -> Unit = {},
                           onSaveClick: () -> Unit = {}
) {
    XTheme {
        Surface(modifier = Modifier
            .fillMaxSize()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.White)
            ) {
                XTitleBar()
                Column(modifier = Modifier
                    .weight(1F)
                    .verticalScroll(rememberScrollState(), true)) {
                    Text(text = stringResource(id = if (uiState.isAdd) R.string.geek_add_work_experience else R.string.geek_edit_work_experience),
                        modifier = Modifier.padding(top = 12.dp, start = 16.dp, end = 16.dp),
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 28.sp,
                        color = Black020202,
                        lineHeight = 32.sp)
                    // job title
                    GeekEditCommonItem(modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .noRippleClickable {
                            onJobTitleClick()
                        },
                        title = stringResource(id = R.string.common_job_title),
                        content = uiState.bean.jobTitle ?: "",
                        placeHolder = stringResource(id = R.string.geek_edit_job_title_example_placeholder))
                    // company name
                    GeekEditCommonItem(modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .noRippleClickable {
                            onCompanyNameClick()
                        },
                        title = stringResource(id = R.string.common_company_name),
                        content = uiState.bean.companyName ?: "",
                        placeHolder = stringResource(id = R.string.geek_edit_company_name_example_placeholder))
                    // start date
                    GeekEditCommonItem(modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .noRippleClickable {
                            onFromClick()
                        },
                        title = stringResource(id = R.string.common_from),
                        content = uiState.bean.getShowStartDate(),
                        placeHolder = stringResource(id = R.string.geek_date_format_1))
                    // end date
                    GeekEditEndDateItem(modifier = Modifier
                        .padding(horizontal = 16.dp),
                        onClick = {onToClick()},
                        isCheck = uiState.bean.endYear.isPresentYear(),
                        checkBoxText = stringResource(id = R.string.geek_i_current_work_here),
                        title = stringResource(id = R.string.common_to),
                        content = uiState.bean.getShowEndDate(),
                        placeHolder = stringResource(id = R.string.geek_date_format_1),
                        onCheckChange = {
                            onToCurrentChecked(it)


                        })
                    // description
                    GeekEditCommonItem(modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .noRippleClickable {
                            onDescriptionClick()
                        },
                        title = stringResourceWithOption(id = R.string.job_description),
                        content = uiState.bean.description ?: "",
                        placeHolder = stringResource(id = R.string.geek_edit_job_description_hint),
                        annotation = if (uiState.showDescriptionTip) stringResource(R.string.job_seeker_profile_improve_strategy5) else ""
                    )
                }
                GeekEditBottomButtons(modifier = Modifier.padding(16.dp),
                    showDelete = !uiState.isAdd,
                    onDeleteClick = {
                        onDeleteClick()
                    },
                    canSave = uiState.canSave,
                    onSaveClick = {
                        onSaveClick()
                    })

            }
        }
    }
}


@Preview
@Composable
private fun PreviewGeekEditWorkExpContentEmpty() {
    GeekEditWorkExpContent()
}

@Preview
@Composable
private fun PreviewGeekEditWorkExpContentFull() {
    val uiState = GeekEditWorkExpUiState(bean =  WorkExperienceBean(jobTitle = "Android Developer",
        companyName = "Google",
        startYear = 2020,
        startMonth = 1,
        endYear = 2021,
        endMonth = 1,
        description = "Developed Android applications"), canSave = true, isAdd = true)
    GeekEditWorkExpContent(uiState)
}

@Preview
@Composable
private fun PreviewGeekEditWorkExpContentPresent() {
    val uiState = GeekEditWorkExpUiState(bean =  WorkExperienceBean(jobTitle = "Android Developer",
        companyName = "Google",
        startYear = 2020,
        startMonth = 1,
        endYear = 9999,
        endMonth = 1,
        description = "Developed Android applications"), canSave = false, isAdd = false)
    GeekEditWorkExpContent(uiState)
}
