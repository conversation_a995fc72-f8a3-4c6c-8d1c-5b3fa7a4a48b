package com.twl.meeboss.geek.model.result

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.export_share.model.JobDetailJobInfo
import com.twl.meeboss.core.ui.theme.COLOR_DDDDDD
import java.util.UUID

data class GeekSuggestCompanyItemResult(
    val companyId: String? = null,
    val companyName: String? = null,
    val companyShortName: String? = null,
    val companyLogo: String? = null,
    val companyLogoThumbnail: String? = null,
    val sizeType: OptionBean? = null,
    val industry: OptionBean? = null,
    val jobCount:Int = 0,
    val interested:Int = 0, // 是否关注，0未关注，1已关注
    val comment: String? = null, //加入公司talent pool时评论
    val jobs:List<JobDetailJobInfo> = listOf()
):BaseEntity {
    fun getUniqueKey(): String {
        return UUID.randomUUID().toString()
    }

    fun buildCompanyDescAnnotatedString(): AnnotatedString? {
        val splitStr = "  |  "
        val industry = this.industry?.name
        val sizeType = this.sizeType?.name
        if (industry == null && sizeType == null) {
            return null
        }
        if (industry != null && sizeType != null) {
            return buildAnnotatedString {
                append(industry)
                withStyle(
                    style = SpanStyle(
                        color = COLOR_DDDDDD,
                    )
                ) {
                    append(splitStr)
                }
                append(sizeType)
            }
        }
        industry?.apply {
            return buildAnnotatedString {
                append(industry)
            }
        }
        sizeType?.apply {
            return buildAnnotatedString {
                append(sizeType)
            }
        }
        return null
    }

    fun buildJobDescAnnotatedString(): AnnotatedString? {
        if (this.jobs.isEmpty()) {
            return null
        }
        val jobItem = this.jobs[0]
        val splitStr = "  |  "
        val jobType = jobItem.jobType?.map { it.name }?.joinToString(", ")
        val address = mutableListOf(jobItem.locationType?.name).apply {
            addAll(jobItem.address?.map { it.name }?: listOf())
        }.joinToString(", ")

        if (jobType.isNullOrBlank() && address.isBlank()) {
            return null
        }
        if (!jobType.isNullOrBlank() && address.isNotBlank()) {
            return buildAnnotatedString {
                append(jobType)
                withStyle(
                    style = SpanStyle(
                        color = COLOR_DDDDDD,
                    )
                ) {
                    append(splitStr)
                }
                append(address)
            }
        }
        if (!jobType.isNullOrBlank()) {
            return buildAnnotatedString {
                append(jobType)
            }
        }
        if (address.isNotBlank()) {
            return buildAnnotatedString {
                append(address)
            }
        }
        return null
    }

    fun buildJobSalaryString(): String? {
        if (this.jobs.isEmpty()) {
            return null
        }
        val jobItem = this.jobs[0]
        return jobItem.salaryShortDesc
    }
}