package com.twl.meeboss.geek.module.me.home.components

import android.content.Context
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.from.GeekFromData
import com.twl.meeboss.base.model.geek.GeekMeCardTabBean
import com.twl.meeboss.base.model.geek.GeekMeTabBean
import com.twl.meeboss.base.model.geek.GeekUserInfo
import com.twl.meeboss.base.protocol.ProtocolHelper
import com.twl.meeboss.core.ui.component.button.XCommonSmallButton
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.COLOR_888888
import com.twl.meeboss.core.ui.theme.COLOR_EB5721
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.model.bean.GeekListExtendType
import com.twl.meeboss.geek.module.me.home.preview.GeekMeGuideCardPreviewData
import com.twl.meeboss.geek.module.me.home.preview.GeekMeGuideCardPreviewParameterProvider
import com.twl.meeboss.geek.utils.GeekPointReporter
import com.twl.meeboss.setting.export.SettingPageRouter

/**
 * @author: 冯智健
 * @date: 2024年08月10日 10:16
 * @description:
 */
@Composable
fun GeekMeContent(
    modifier: Modifier = Modifier,
    geekUserInfo: GeekUserInfo? = null,
    hideBanner: Boolean = false,
    onCloseBanner: (Int) -> Unit = {},
    onClearRedDot: () -> Unit = {},
) {
    val context = LocalContext.current
    Column(
        modifier = modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .background(COLOR_F5F5F5)
    ) {
        if (!hideBanner) {
            GeekBanner(
                context = context,
                geekUserInfo = geekUserInfo,
                onCloseBanner = onCloseBanner,
                modifier = Modifier.padding(top = 4.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
        }

        Column(
            modifier = Modifier
                .padding(start = 16.dp, end = 16.dp, top = 4.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(Color.White)
        ) {
            GeekMeItem(
                icon = R.drawable.geek_icon_profile,
                title = R.string.geek_profile,
                onClick = {
                    onClearRedDot()
                    GeekPageRouter.jumpToGeekEditProfileActivity(context)
                    GeekPointReporter.f3tabMenuClick(1)
                },
                showRedDot = geekUserInfo?.displayRedDot == 1,
                tag = {
                    val label = geekUserInfo?.mineNoticeLabel
                    if (!label.isNullOrBlank()) {
                        if (geekUserInfo.mineNoticeType == 3) {
                            HighlightTag(
                                text = label,
                                contentColor = Secondary,
                                backgroundColor = Color(0xFFE0F8EB)
                            )
                        } else {
                            HighlightTag(text = label)
                        }
                    }
                }
            )
            GeekMeItem(
                icon = R.drawable.geek_icon_job_preference,
                title = R.string.geek_job_preference,
                onClick = {
                    GeekPageRouter.jumpToGeekJobPreferenceActivity(context, GeekFromData.GEEK_MY)
                },
            )
            GeekMeItem(
                icon = R.drawable.geek_icon_resume_attachment,
                title = R.string.geek_resume_attachment,
                onClick = {
                    GeekPageRouter.jumpToGeekResumeAttachmentActivity(context)
                    GeekPointReporter.f3tabMenuClick(3)
                },
            )
            GeekMeItem(
                icon = R.drawable.geek_icon_my_jobs,
                title = R.string.common_my_jobs,
                onClick = {
                    GeekPageRouter.jumpToGeekMyJobsActivity(context)
                    GeekPointReporter.f3tabMenuClick(4)
                },
            )
            GeekMeItem(
                icon = R.drawable.geek_icon_talent_pool,
                title = R.string.boss_talent_pool_list,
                onClick = {
                    GeekPageRouter.jumpToGeekTalentPoolActivity(context)
                    GeekPointReporter.f3tabMenuClick(6)
                },
            )
            GeekMeItem(
                icon = R.drawable.geek_icon_setting,
                title = R.string.common_settings,
                onClick = {
                    SettingPageRouter.jumpToSettingMainActivity(context)
                    GeekPointReporter.f3tabMenuClick(5)
                    GeekPointReporter.f3TabSettings()
                },
                showDivider = false,
            )
        }
        Spacer(modifier = Modifier.height(40.dp))
    }
}

@Composable
private fun HighlightTag(
    text: String,
    contentColor: Color = COLOR_EB5721,
    backgroundColor: Color = Color(0xFFFFE9E1)
) {
    Box(
        modifier = Modifier.background(color = backgroundColor, shape = RoundedCornerShape(size = 8.dp))
    ) {
        Text(
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp),
            text = text,
            style = TextStyle(
                fontSize = 13.sp,
                lineHeight = 20.sp,
                fontWeight = FontWeight.Medium,
                color = contentColor,
            )
        )
    }
}

data class GeekMeCardResource(
    @StringRes val title: Int,
    @StringRes val content: Int = -1,
    @StringRes val actionButton: Int = -1,
    @DrawableRes val bgIcon: Int = -1
)

private fun GeekMeCardTabBean.getResources(): GeekMeCardResource? {
    return when (type) {
        GeekListExtendType.JS_GUIDE_NAME -> GeekMeCardResource(
            title = R.string.geek_personal_details,
            content = R.string.job_seeker_profile_improve_strategy1,
            actionButton = R.string.common_button_edit,
            bgIcon = R.drawable.geek_guide_card_edit_name
        )
        GeekListExtendType.JS_GUIDE_LOCATION -> GeekMeCardResource(
            title = R.string.geek_personal_details,
            content = R.string.job_seeker_profile_improve_strategy2,
            actionButton = R.string.common_add,
            bgIcon = R.drawable.geek_guide_card_edit_name
        )
        GeekListExtendType.JS_GUIDE_AVATAR -> GeekMeCardResource(
            title = R.string.geek_personal_details,
            content = R.string.job_seeker_profile_improve_strategy3,
            actionButton = R.string.common_upload,
            bgIcon = R.drawable.geek_guide_card_edit_name
        )
        GeekListExtendType.JS_GUIDE_WORK_EXP -> GeekMeCardResource(
            title = R.string.common_work_experience,
            content = R.string.job_seeker_profile_improve_strategy4,
            actionButton = R.string.common_add,
            bgIcon = R.drawable.geek_guide_card_work_exp
        )
        GeekListExtendType.JS_GUIDE_IMPROVE_WORK_EXP -> GeekMeCardResource(
            title = R.string.common_work_experience,
            content = R.string.job_seeker_profile_improve_strategy5,
            actionButton = R.string.common_button_edit,
            bgIcon = R.drawable.geek_guide_card_work_exp
        )
        GeekListExtendType.JS_GUIDE_SKILL -> GeekMeCardResource(
            title = R.string.common_skills,
            content = R.string.job_seeker_profile_improve_strategy6,
            actionButton = R.string.common_add,
            bgIcon = R.drawable.geek_guide_card_edit_skills
        )
        else -> null
    }
}

@DrawableRes
private fun GeekMeTabBean.getIconRes() = when (type) {
    2, 5, 6, 7, 8, 9, 10 -> R.drawable.geek_guide_card_edit_name
    4, 11, 12 -> R.drawable.geek_guide_card_work_exp
    13 -> R.drawable.geek_guide_card_edit_skills
    14 -> R.drawable.geek_guide_beginner_flow
    else -> R.mipmap.geek_icon_guide_upload
}

@Composable
private fun GeekBanner(
    context: Context,
    geekUserInfo: GeekUserInfo?,
    onCloseBanner: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    val priorityTab = geekUserInfo?.tabs?.firstOrNull()
    if (priorityTab == null) {
        val newTab = geekUserInfo?.cardTabs?.firstOrNull()
        newTab?.getResources()?.let { item ->
            GeekMeGuideCard(
                modifier = modifier,
                title = stringResource(item.title),
                content = stringResource(item.content),
                button = stringResource(item.actionButton),
                icon = item.bgIcon,
                onButtonClick = {
                    if (newTab.type == 1) {
                        GeekPageRouter.jumpToGeekResumeAttachmentActivity(context)
                    } else {
                        ProtocolHelper.parseProtocol(newTab.url)
                    }
                    GeekPointReporter.f3tabCardClick(newTab.type)
                    GeekPointReporter.completeGuideClick(newTab.type, 5, 1)
                },
                showClose = newTab.closeable,
                onCloseClick = {
                    onCloseBanner(newTab.type)
                    GeekPointReporter.completeGuideClick(newTab.type, 5, 2)
                }
            )
            LaunchedEffect(newTab) {
                GeekPointReporter.completeGuideShow(newTab.type, 3)
            }
        }
    } else {
        GeekMeGuideCard(
            modifier = modifier,
            title = priorityTab.title,
            content = priorityTab.desc,
            button = priorityTab.btnName,
            icon = R.mipmap.geek_icon_guide_upload,
            onButtonClick = {
                if (priorityTab.type == 1) {
                    GeekPageRouter.jumpToGeekResumeAttachmentActivity(context)
                }
                GeekPointReporter.f3tabCardClick(priorityTab.type)
            },
            showClose = false,
        )
    }
}

@Composable
private fun GeekMeGuideCard(
    modifier: Modifier = Modifier,
    title: String,
    content: String,
    button: String,
    @DrawableRes icon: Int,
    showClose: Boolean,
    onButtonClick: () -> Unit = {},
    onCloseClick: () -> Unit = {},
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(Color.White)
    ) {
        Image(
            painter = painterResource(id = icon),
            modifier = Modifier.align(Alignment.BottomEnd),
            contentDescription = null
        )
        Column(
            modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 20.dp, bottom = 16.dp)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Black222222
            )
            Text(
                text = content,
                modifier = Modifier.padding(top = 8.dp),
                fontSize = 13.sp,
                fontWeight = FontWeight.Normal,
                color = Black888888
            )
            XCommonSmallButton(
                modifier = Modifier
                    .padding(top = 16.dp)
                    .height(34.dp),
                contentPadding = PaddingValues(vertical = 0.dp, horizontal = 24.dp),
                buttonText = button,
                onClick = onButtonClick
            )
        }
        if (showClose) {
            Icon(
                painter = painterResource(R.drawable.ui_item_close),
                contentDescription = null,
                modifier = Modifier
                    .padding(top = 16.dp, end = 16.dp)
                    .align(Alignment.TopEnd)
                    .noRippleClickable { onCloseClick() },
                tint = COLOR_888888
            )
        }
    }
}

@Preview
@Composable
private fun PreviewGeekMeContent() {
    GeekMeContent(geekUserInfo = GeekUserInfo(tabs = listOf(GeekMeGuideCardPreviewData.cardData)))
}

@Preview
@Composable
private fun PreviewGeekMeGuideCard(
    @PreviewParameter(GeekMeGuideCardPreviewParameterProvider::class)
    item: GeekMeTabBean
) {
    GeekMeGuideCard(
        title = item.title,
        content = item.desc,
        button = item.btnName,
        icon = item.getIconRes(),
        showClose = true
    )
}


