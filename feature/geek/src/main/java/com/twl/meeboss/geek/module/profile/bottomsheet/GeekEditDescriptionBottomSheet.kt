package com.twl.meeboss.geek.module.profile.bottomsheet

import android.app.Dialog
import android.os.Parcelable
import android.text.TextUtils
import android.view.WindowManager
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.os.bundleOf
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewmodel.compose.viewModel
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.foundation.viewmodel.BaseViewModel
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black020202
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.GRAY_AAAAAA
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.theme.alpha
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.geek.R
import kotlinx.parcelize.Parcelize

class GeekEditDescriptionBottomSheet : CommonBottomDialogFragment() {
    private var onSelectCallback:((String) -> Unit) ?= null

    private val type by lazy {
        arguments?.getParcelable("type") as? GeekEditDescriptionType ?: GeekEditDescriptionType.JobDescription()
    }
    private val defaultValue by lazy {
        arguments?.getString("defaultValue") ?: ""
    }

    companion object {
        fun newInstance(defaultValue: String,
                        type: GeekEditDescriptionType,
                        onSelectCallback: (String) -> Unit) = GeekEditDescriptionBottomSheet().apply {
            this.onSelectCallback = onSelectCallback
            arguments = bundleOf(
                "defaultValue" to defaultValue,
                "type" to type
            )
        }
    }

    @Composable
    override fun DialogContent() {
        val vm: GeekEditDescriptionViewModel = viewModel()
        if(defaultValue.isNotBlank()){
            vm.input.value = TextFieldValue(text = defaultValue, selection = TextRange(defaultValue.length, defaultValue.length))
        }
        vm.maxInputLength = type.maxCount
        vm.placeHolder = R.string.common_describe_filed_hint.toResourceString(type.minCount.toString(),type.maxCount.toString())
        GeekEditDescriptionContent(defaultValue = defaultValue, title = type.title, closeCallback = this::closeDialog, confirmCallback = this::confirm)
    }

    private fun closeDialog() {
        dismissSafely()
    }

    override fun setDialogAttributes(dialog: Dialog?) {
        super.setDialogAttributes(dialog)
        dialog?.run {
            this.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        }
    }

    private fun confirm(value: String) {
        val count = value.length
        if (count < type.minCount) {
            T.ss(R.string.common_description_out_of_min_range.toResourceString(type.minCount.toString()))
            return
        } else if (count > type.maxCount) {
            T.ss(R.string.common_description_out_of_max_range.toResourceString(type.maxCount.toString()))
            return
        }
        onSelectCallback?.let { it(value) }
        dismissSafely()
    }
}

@Parcelize
sealed class GeekEditDescriptionType(@StringRes val title: Int,
                                     val minCount: Int = 10,
                                     val maxCount: Int = DefaultValueConstants.GEEK_MAX_INPUT_COMMON_DESCRIPTION) : Parcelable {
    @Parcelize
    class JobDescription : GeekEditDescriptionType(title = R.string.geek_describe_your_job, minCount = 0, maxCount = DefaultValueConstants.MAX_INPUT_JOB_DESCRIPTION)
    @Parcelize
    class EducationDescription : GeekEditDescriptionType(R.string.geek_describe_your_education, minCount = 0)
    @Parcelize
    class ProjectDescription : GeekEditDescriptionType(R.string.geek_describe_your_project, minCount = 0)
    @Parcelize
    class ProjectPerformance : GeekEditDescriptionType(R.string.geek_describe_your_performance)
}

/**
 * boss编辑职位表述
 */
@Composable
fun GeekEditDescriptionContent(vm: GeekEditDescriptionViewModel = viewModel(), defaultValue: String = "",
                               @StringRes title: Int,
                               closeCallback: () -> Unit = {}, confirmCallback: (String) -> Unit = {}) {

    Column(modifier = Modifier
        .fillMaxSize()
        .imePadding()
        .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
        .background(Color.White)) {
        val input by vm.input.observeAsState(TextFieldValue(text = defaultValue, selection = TextRange(defaultValue.length, defaultValue.length)))
        Box(modifier = Modifier
            .fillMaxWidth()
            .height(60.dp)) {
            Image(painter = painterResource(id = com.twl.meeboss.core.ui.R.drawable.ui_dailog_close),
                modifier = Modifier
                    .padding(16.dp, 0.dp)
                    .size(24.dp)
                    .align(Alignment.CenterStart)
                    .noRippleClickable {
                        closeCallback()
                    },
                contentDescription = "Close dialog")
            Text(text = stringResource(id = com.twl.meeboss.core.ui.R.string.common_button_save),
                fontSize = 16.sp,
                modifier = Modifier
                    .padding(16.dp, 0.dp)
                    .align(Alignment.CenterEnd)
                    .noRippleClickable {
                        confirmCallback(vm.input.value?.text ?: "")
                    },
                textAlign = TextAlign.Center,
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                color = Secondary.alpha(if(TextUtils.isEmpty(vm.input.value?.text)) 0.5f else 1f),
                fontWeight = FontWeight.Medium)
        }
        Spacer(modifier = Modifier.height(8.dp))
        Text(text = stringResource(id = title),
            modifier = Modifier.padding(16.dp, 0.dp),
            fontSize = 28.sp, fontWeight = FontWeight.SemiBold,
            color = Black020202)
        Spacer(modifier = Modifier.height(20.dp))
        val focusRequester = remember { FocusRequester() }


        BasicTextField(value = input,
            modifier = Modifier
                .focusRequester(focusRequester)
                .fillMaxWidth()
                .padding(16.dp, 0.dp)
                .weight(1F), onValueChange = {
                vm.input.value = it
            }) { innerTextField ->
            Box {
                if (input.text.isEmpty()) {
                    Text(text = vm.placeHolder,
                        fontWeight = FontWeight.Normal,
                        fontSize = 16.sp, color = GRAY_AAAAAA)
                }
                innerTextField()

            }
        }
        Spacer(modifier = Modifier.height(10.dp))
        Row {
            Spacer(modifier = Modifier.weight(1F))
            val count = input.text.length
            Text(text = "$count",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = if (count > 0) Secondary else Secondary.alpha(0.5f))
            Text(text = "/${vm.maxInputLength}",
                modifier = Modifier.padding(0.dp, 0.dp, 16.dp, 0.dp),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Black484848)

        }
        Spacer(modifier = Modifier.height(10.dp))
        LaunchedEffect(key1 = Unit) {
            focusRequester.requestFocus()
        }
    }


}

/**
 * boss编辑职位表述ViewModel

 */
class GeekEditDescriptionViewModel : BaseViewModel() {
    var maxInputLength = DefaultValueConstants.MAX_INPUT_JOB_DESCRIPTION
    var placeHolder:String = ""
    var input: MutableLiveData<TextFieldValue> = MutableLiveData()

}

@Preview
@Composable
private fun PreviewGeekEditDescriptionContent() {
    GeekEditDescriptionContent(defaultValue = "", title = R.string.geek_describe_your_job)
}
