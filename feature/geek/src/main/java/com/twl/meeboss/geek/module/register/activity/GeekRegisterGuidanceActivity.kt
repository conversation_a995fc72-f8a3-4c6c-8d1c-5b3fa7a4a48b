package com.twl.meeboss.geek.module.register.activity

import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.constants.BUNDLE_BOOLEAN
import com.twl.meeboss.base.constants.BUNDLE_TYPE
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.showRegisterRightMoreDialog
import com.twl.meeboss.base.main.router.BaseServiceRouter
import com.twl.meeboss.base.mudule.ModuleManager
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.EditPageScene
import com.twl.meeboss.geek.export.GeekEventBusKey
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.export.GeekRouterPath
import com.twl.meeboss.geek.export.GuidanceType
import com.twl.meeboss.geek.export.ResumeAnalysisType
import com.twl.meeboss.geek.module.guidance.components.GuidancePageProxy
import com.twl.meeboss.geek.module.me.quickcomplete.model.GeekResumeAnalyzeEventData
import com.twl.meeboss.geek.module.register.components.GeekRegisterGuidanceContent
import com.twl.meeboss.geek.module.register.viewmodel.GeekRegisterGuidanceUiIntent
import com.twl.meeboss.geek.module.register.viewmodel.GeekRegisterGuidanceViewModel
import com.twl.meeboss.geek.utils.GeekRegisterApmReporter
import dagger.hilt.android.AndroidEntryPoint

@RouterPage(path = [GeekRouterPath.GEEK_REGISTER_GUIDE_PAGE])
@AndroidEntryPoint
class GeekRegisterGuidanceActivity() : BaseMviActivity<GeekRegisterGuidanceViewModel>() {
    private val guidancePageProxy: GuidancePageProxy by lazy {
        RegisterGuidancePageEventProxy(this)
    }
    override val viewModel: GeekRegisterGuidanceViewModel by viewModels()
    private val mCanGoBack:Boolean by lazy {
        intent.getBooleanExtra(BUNDLE_BOOLEAN, true)
    }

    override fun preInit(intent: Intent) {
        viewModel.guidanceType = intent.getSerializableExtra(BUNDLE_TYPE) as? GuidanceType ?: GuidanceType.RegisterGuidance
        guidancePageProxy.init()
    }

    override fun initData() {
        liveEventBusObserve<GeekResumeAnalyzeEventData>(GeekEventBusKey.GEEK_CHAT_APPLY_GUIDE_FINISH){
            if (it.isSuccess) {
                ModuleManager.updateUserInfo()
            }
        }

        liveEventBusObserve<GeekResumeAnalyzeEventData>(GeekEventBusKey.GEEK_COMPLETE_MANUALLY_FINISH){
            if (it.isSuccess) {
                ModuleManager.updateUserInfo()
            }
        }
    }

    override fun onBackPressed() {
        if (mCanGoBack) {
            super.onBackPressed()
        }
    }

    override fun onResume() {
        super.onResume()
        TLog.info("GEEK_CHAT_APPLY_GUIDE_FINISH", "onResume")
        viewModel.sendUiIntent(GeekRegisterGuidanceUiIntent.Update)
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        GeekRegisterGuidanceContent(
            pageProxy = guidancePageProxy,
            canGoBack = mCanGoBack,
            uiState = uiState,
            onAddNameClick = this::onAddNameClick,
            onAddEduExpClick = this::onAddEduExpClick,
            onAddWorkExpClick = this::onAddWorkExpClick,
            onButtonClick = this::onButtonClick,
            onClickRightMore = this::showRightMoreDialog,
        )
    }

    private fun showRightMoreDialog(rect: Rect) {
        showRegisterRightMoreDialog(
            rect,
        )
    }

    private fun onAddNameClick(isAdd: Boolean) {
        GeekRegisterApmReporter.reportApm("profile","addName")
        GeekPageRouter.jumpToGeekBeginnerAddNameActivity(
            this,
            EditPageScene.SINGLE_PAGE,
            guidanceType = viewModel.guidanceType
        )
        viewModel.pointHelper.click(1, isAdd = isAdd)
    }

    private fun onAddWorkExpClick(isAdd: Boolean) {
        GeekRegisterApmReporter.reportApm("profile","addWorkExp")
        if (isAdd) {
            GeekPageRouter.jumpToGeekBeginnerAddWorkExpActivity(
                this,
                EditPageScene.SINGLE_PAGE,
                viewModel.guidanceType
            )
        } else {
            GeekPageRouter.jumpToGeekChatGuidanceWorkExpListActivity(
                context = this,
                guidanceType = viewModel.guidanceType
            )
        }
        viewModel.pointHelper.click(2, isAdd = isAdd)
    }

    private fun onAddEduExpClick(isAdd: Boolean) {
        GeekRegisterApmReporter.reportApm("profile","addEduExp")
        if (isAdd) {
            GeekPageRouter.jumpToGeekBeginnerAddEduExpActivity(
                context = this,
                scene = EditPageScene.SINGLE_PAGE,
                guidanceType = viewModel.guidanceType
            )
        } else {
            GeekPageRouter.jumpToGeekChatGuidanceEduExpListActivity(
                context = this,
                guidanceType = viewModel.guidanceType
            )
        }
        viewModel.pointHelper.click(3, isAdd = isAdd)
    }

    private fun onButtonClick(allCompleted: Boolean) {
        if (allCompleted) {
            viewModel.pointHelper.clickAllComplete()
            GeekRegisterApmReporter.reportApm("profile","allCompleted")
            guidancePageProxy.onButtonClickWhenCompleted()
        } else {
            GeekRegisterApmReporter.reportApm("profile","quickComplete")
            GeekPageRouter.jumpToGeekChatGuidanceCompleteMethodActivity(
                this,
                ResumeAnalysisType.GEEK_FIRST_COMPLETE,
                viewModel.showManually()
            )
            viewModel.pointHelper.clickQuickComplete()
        }
    }


}


@Preview
@Composable
private fun PreviewGeekChatGuidanceContent() {
    val context = LocalContext.current
    GeekRegisterGuidanceContent(pageProxy = RegisterGuidancePageEventProxy(context))
}



class RegisterGuidancePageEventProxy(val context: Context) :
    GuidancePageProxy {

    override val titleForNotComplete: String = ""
    override val titleForFromQuickComplete: String = R.string.job_seeker_onboard_profile_title.toResourceString(context)
    override val subTitleForFromQuickComplete: String = R.string.job_seeker_onboard_profile_hint.toResourceString(context)
    override val titleForAllCompleted: String = R.string.job_seeker_initiate_info_check_allset.toResourceString(context)
    override val subtitleForAllComplete: String = R.string.job_seeker_onboard_profile_review.toResourceString(context)
    override val buttonForNotComplete: String = R.string.geek_quick_complete.toResourceString(context)
    override val buttonTextForAllCompleted: String =R.string.job_seeker_guide_complete_done.toResourceString(context)

    override fun init() {
    }

    override fun onButtonClickWhenCompleted() {
        if(context is FragmentActivity){
            BaseServiceRouter.afterLogin(context)
        }
    }
}
