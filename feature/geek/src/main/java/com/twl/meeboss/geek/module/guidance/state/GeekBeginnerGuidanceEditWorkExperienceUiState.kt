package com.twl.meeboss.geek.module.guidance.state

import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.model.profile.WorkExperienceBean
import com.twl.meeboss.geek.export.EditPageScene
import com.twl.meeboss.geek.export.GuidanceType

data class GeekBeginnerGuidanceEditWorkExperienceUiState(
    val canSave: Boolean = false,
    @EditPageScene
    val scene:Int = EditPageScene.SINGLE_PAGE,
    val guidanceType: GuidanceType = GuidanceType.BeginnerGuidance,
    val hasNoWorExperience: Boolean = false,
    val completed: Boolean = false,
    val isSinglePage: Boolean = false,
    val showNoWorkExpCheckbox: Boolean = true,
    val bean: WorkExperienceBean = WorkExperienceBean(),
    val initialBean: WorkExperienceBean = WorkExperienceBean(),  // 添加初始bean存储
    val initialHasNoWorkExperience: Boolean = false              // 添加初始无工作经验状态
) : IUiState {
    fun checkCanSave(): GeekBeginnerGuidanceEditWorkExperienceUiState {
        return if (hasNoWorExperience) {
            copy(canSave = true)
        } else {
            val save = !bean.jobTitle.isNullOrBlank()
                    && !bean.companyName.isNullOrBlank()
                    && bean.startYear != 0
                    && bean.endYear != 0
                    && bean.endYear >= bean.startYear
            copy(canSave = save)
        }
    }

    fun hasInformation(): Boolean {
        return hasNoWorExperience || (!bean.jobTitle.isNullOrBlank()
                || !bean.companyName.isNullOrBlank()
                || bean.startYear > 0
                || bean.endYear > 0
                || !bean.description.isNullOrBlank())
    }

    fun isInFlow():Boolean{
        return scene == EditPageScene.FLOW_PAGE
    }

    // 添加方法来记录初始状态
    fun recordInitialState(): GeekBeginnerGuidanceEditWorkExperienceUiState {
        return copy(
            initialBean = bean.copy(),
            initialHasNoWorkExperience = hasNoWorExperience
        )
    }
    
    // 添加hasChange方法检测变更
    fun hasChange(): Boolean {
        // 检查"无工作经验"状态是否变更
        if (hasNoWorExperience != initialHasNoWorkExperience) {
            return true
        }
        
        // 无工作经验状态下不需要比较bean内容
        if (hasNoWorExperience && initialHasNoWorkExperience) {
            return false
        }
        
        // 比较工作经验数据变化
        return bean.jobTitle != initialBean.jobTitle ||
                bean.companyName != initialBean.companyName ||
                bean.companyId != initialBean.companyId ||
                bean.startYear != initialBean.startYear ||
                bean.startMonth != initialBean.startMonth ||
                bean.endYear != initialBean.endYear ||
                bean.endMonth != initialBean.endMonth ||
                bean.description != initialBean.description
    }
}