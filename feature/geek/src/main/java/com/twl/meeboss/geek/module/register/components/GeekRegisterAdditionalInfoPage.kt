package com.twl.meeboss.geek.module.register.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.components.dialog.CommonDataType
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.COLOR_AAAAAA
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.geek.R

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun GeekRegisterAdditionalInfoPage(
    selectLocationStr: String = "",
    selectEmploymentStr: String = "",
    selectRoleLevelStr: String = "",
    saveButtonNext:String = stringResource(id = R.string.common_next),
    onClickBack: () -> Unit = {},
    onClickSkip: () -> Unit = {},
    onClickSave: () -> Unit = {},
    onClickItem: (CommonDataType) -> Unit = {},
) {
    val context = LocalContext.current
    val canSaveAddition: MutableLiveData<Boolean> = MutableLiveData(selectLocationStr.isNotEmpty() || selectEmploymentStr.isNotEmpty() || selectRoleLevelStr.isNotEmpty())
    val canSave by canSaveAddition.observeAsState(initial = false)

    XTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
        ) {
            XTitleBar(
                modifier = Modifier.fillMaxWidth(),
                onBackClick = {
                    onClickBack()
                },
                rightContent = {
                    Text(
                        text = stringResource(id = R.string.geek_register_skip),
                        fontSize = 16.sp,
                        modifier = Modifier
                            .padding(end = 18.dp)
                            .noRippleClickable {
                                onClickSkip()
                            },
                        lineHeight = 18.sp,
                    )
                }
            )

            Text(
                text = stringResource(id = R.string.common_additional_information),
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp, top = 12.dp),
                lineHeight = 38.sp,
                fontSize = 28.sp,
                fontWeight = FontWeight.SemiBold,
                color = Black222222
            )

            Text(
                text = stringResource(id = R.string.js_setup_additional),
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp, top = 8.dp),
                fontSize = 14.sp,
                color = Black484848
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 16.dp, end = 16.dp, top = 20.dp)
                    .noRippleClickable {
                        onClickItem(CommonDataType.WORKPLACE_TYPE)
                    },
            ) {
                Text(
                    text = stringResource(id = R.string.common_location_type),
                    modifier = Modifier
                        .padding(top = 20.dp),
                    lineHeight = 18.sp,
                    fontSize = 13.sp,
                    color = Black888888
                )
                Text(
                    text = if (selectLocationStr.isEmpty()) {
                        stringResource(id = R.string.geek_register_desc_location_type)
                    } else {
                        selectLocationStr?:""
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .padding(top = 8.dp),
                    fontSize = 17.sp,
                    color = if (selectLocationStr.isEmpty()){
                        COLOR_AAAAAA
                    } else {
                        Black222222
                    }
                )
            }

            HorizontalDivider(
                modifier = Modifier.padding(top = 20.dp),
                thickness = 1.dp,
                color = BlackEBEBEB
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 16.dp, end = 16.dp)
                    .noRippleClickable {
                        onClickItem(CommonDataType.EMPLOYMENT_TYPE)
                    },
            ) {
                Text(
                    text = stringResource(id = R.string.common_employment_type),
                    modifier = Modifier
                        .padding(top = 20.dp),
                    lineHeight = 18.sp,
                    fontSize = 13.sp,
                    color = Black888888
                )

                Text(
                    text = if (selectEmploymentStr.isEmpty()) {
                        stringResource(id = R.string.job_select_employment_type)
                    } else {
                        selectEmploymentStr?:""
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .padding(top = 8.dp),
                    fontSize = 17.sp,
                    color = if (selectEmploymentStr.isEmpty()){
                        COLOR_AAAAAA
                    } else {
                        Black222222
                    }
                )
            }

            HorizontalDivider(
                modifier = Modifier.padding(top = 20.dp),
                thickness = 0.5.dp,
                color = BlackEBEBEB
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 16.dp, end = 16.dp)
                    .noRippleClickable {
                        onClickItem(CommonDataType.LEVEL_OF_ROLE)
                    },
            ) {
                Text(
                    text = stringResource(id = R.string.geek_register_title_level_of_role),
                    modifier = Modifier
                        .padding(top = 20.dp),
                    lineHeight = 18.sp,
                    fontSize = 13.sp,
                    color = Black888888
                )

                Text(
                    text = if (selectRoleLevelStr.isEmpty()) {
                        stringResource(id = R.string.geek_register_desc_level_of_role)
                    } else {
                        selectRoleLevelStr?:""
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .padding(top = 8.dp),
                    fontSize = 17.sp,
                    color = if (selectRoleLevelStr.isEmpty()){
                        COLOR_AAAAAA
                    } else {
                        Black222222
                    }
                )
            }

            HorizontalDivider(
                modifier = Modifier.padding(top = 20.dp),
                thickness = 0.5.dp,
                color = BlackEBEBEB
            )

            Spacer(modifier = Modifier.weight(1F))

            XCommonButton(
                modifier = Modifier.padding(16.dp), enabled = canSave,
                text = saveButtonNext,
                onClick = {
                    onClickSave()
                }
            )

        }
    }
}


@Preview
@Composable
private fun PreviewGeekRegisterAdditionalInfoPage() {
    GeekRegisterAdditionalInfoPage()
}