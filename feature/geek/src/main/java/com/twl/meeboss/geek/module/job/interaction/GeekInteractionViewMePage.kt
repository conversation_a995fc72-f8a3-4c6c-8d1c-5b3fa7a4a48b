package com.twl.meeboss.geek.module.job.interaction

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.paging.compose.LazyPagingItems
import com.twl.meeboss.base.main.router.BasePageRouter
import com.twl.meeboss.core.ui.component.state.XEmptyContent
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.base.model.job.JobItemResult
import com.twl.meeboss.geek.module.job.recommend.components.JobListContent
import com.twl.meeboss.geek.utils.GeekPointReporter

@Composable
fun GeekInteractionViewMePage(
    isComplete: Boolean = false,
    lazyPagingItems: LazyPagingItems<JobItemResult>,
    lazyListState: LazyListState) {

    if (lazyPagingItems.itemCount == 0) {
        val context = LocalContext.current
        if(isComplete){
            XEmptyContent(title = stringResource(id = R.string.chat_no_more_yet),
                text = stringResource(id = R.string.geek_viewed_me_list_empty_content_tips),
                emptyButtonText = stringResource(id = R.string.common_find_job), onButtonClick = {
                    BasePageRouter.jumpToMainActivity(context,0,false)
                })
        }else{
            XEmptyContent(title = stringResource(id = R.string.geek_interaction_noviewed_title),
                text = stringResource(id = R.string.geek_interaction_noviewed_desc),
                emptyButtonText = stringResource(id = R.string.chat_list_nomessage_button), onButtonClick = {
                    GeekPointReporter.completeGuideClick(0,4,1)
                    GeekPageRouter.jumpToGeekEditProfileActivity(context)
                })
        }

    } else {
        JobListContent(
            stringResource(id = R.string.geek_viewed_me_list_empty_content_tips),
            stringResource(id = R.string.chat_no_more_yet),
            lazyPagingItems,
            lazyListState
        )
    }

}