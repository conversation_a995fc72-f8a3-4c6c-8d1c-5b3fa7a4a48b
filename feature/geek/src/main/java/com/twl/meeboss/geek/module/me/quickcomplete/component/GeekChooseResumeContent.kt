package com.twl.meeboss.geek.module.me.quickcomplete.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.model.ResumeItemBean
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.theme.Black020202
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.module.me.resumeattachment.preview.GeekResumeAttachmentProvider

@Composable
fun GeekChooseResumeContent(
    resumeList: List<ResumeItemBean>,
    defaultChooseItemIndex:Int = 0,
    isFromChat:Boolean = false,
    titleText:String? = null,
    subTitle:String? = null,
    buttonText:String? = null,
    onCloseClick: () -> Unit = {},
    onResumeAttachmentClick:()->Unit = {},
    onConfirmClick: (Int, ResumeItemBean) -> Unit = { _, _->},
) {

    val checkIndex = remember { mutableIntStateOf(defaultChooseItemIndex) }
    LaunchedEffect(defaultChooseItemIndex) {
        checkIndex.intValue = defaultChooseItemIndex
    }
    XTheme {
        Column(
            modifier = Modifier
                .background(
                    Color.White,
                    shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                )
        ) {
            Box(modifier = Modifier
                .fillMaxWidth()
                .height(58.dp)) {
                Text(
                    text = titleText ?: stringResource(id = if(resumeList.isEmpty()) R.string.common_upload_resume else R.string.common_send_resume),
                    style = TextStyle(
                        fontSize = 18.sp,
                        lineHeight = 26.sp,
                        fontWeight = FontWeight(590),
                        color = Black020202,
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier.align(Alignment.Center)
                )

                Image(painter = painterResource(id = com.twl.meeboss.core.ui.R.drawable.ui_dailog_close),
                    contentDescription = "Close dialog",
                    modifier = Modifier
                        .noRippleClickable(onClick = onCloseClick)
                        .padding(end = 12.dp)
                        .size(24.dp)
                        .align(Alignment.CenterEnd)
                )
            }

            HorizontalDivider(
                thickness = 1.dp,
                color = BlackEBEBEB
            )

            if (!subTitle.isNullOrBlank()) {
                Text(
                    text = subTitle,
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 21.sp,
                        fontWeight = FontWeight.W400,
                        color = Black222222,
                    ),
                    modifier = Modifier.padding(16.dp, 16.dp, 16.dp, 12.dp)
                )
            }


            LazyColumn {
                itemsIndexed(resumeList) {index, item ->
                    GeekResumeChooseItemComponent(
                        item = item,
                        showAnalyzed = !isFromChat,
                        isChecked = index == checkIndex.intValue,
                        onClickItem = {
                            checkIndex.value = index
                        }
                    )
                }
            }

            Column (
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 16.dp, end = 16.dp, top = 8.dp)
                    .background(COLOR_F5F5F5)
                    .border(
                        width = 1.dp,
                        color = Color.Transparent,
                        shape = RoundedCornerShape(size = 8.dp)
                    )
                ,
            ){

                val baseText = stringResource(id = R.string.geek_choose_resume_dialog_desc_base)
                val clickText = stringResource(id = R.string.geek_resume_attachment)
                val annotated = buildAnnotatedString {
                    withStyle(
                        style = SpanStyle(
                            fontSize = 12.sp,
                            color = Black222222
                        )
                    ) {
                        append(baseText)
                    }
                    append(" ")
                    withStyle(
                        style = SpanStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Black020202,
                            textDecoration = TextDecoration.Underline,
                        )
                    ) {
                        append(clickText)
                    }
                }

                ClickableText(
                    text = annotated,
                    modifier = Modifier
                        .padding(18.dp, 8.dp)
                        .fillMaxWidth(),
                ) {
                    if (it in baseText.length until baseText.length + clickText.length) {
                        onResumeAttachmentClick()
                    }
                }
            }


            XCommonButton(
                text = buttonText ?: stringResource(id = R.string.common_button_confirm),
                modifier = Modifier.padding(16.dp),
                enabled = (checkIndex.intValue in resumeList.indices) && !resumeList[checkIndex.intValue].localIsUploading,
                onClick = {
                    onConfirmClick(checkIndex.intValue, resumeList[checkIndex.intValue])
                },
            )
        }
    }
}

@Preview
@Composable
fun PreviewGeekChooseResumeContent(
    @PreviewParameter(GeekResumeAttachmentProvider::class)
    list: List<ResumeItemBean>
) {
    GeekChooseResumeContent(
        resumeList = list,
        subTitle = "By sending your resume, you’re applying for this job."
    )
}