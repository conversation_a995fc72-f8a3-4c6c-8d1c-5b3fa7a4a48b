package com.twl.meeboss.geek.module.guidance.activity

import javax.inject.Inject
import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.blankj.utilcode.util.ActivityUtils
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.constants.BUNDLE_BOOLEAN
import com.twl.meeboss.base.constants.BUNDLE_INT
import com.twl.meeboss.base.constants.BUNDLE_TYPE
import com.twl.meeboss.base.eventbus.sendObjectLiveEvent
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.alertContentChangesDialog
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.EditPageScene
import com.twl.meeboss.geek.export.GeekEventBusKey
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.export.GeekRouterPath
import com.twl.meeboss.geek.export.GuidanceType
import com.twl.meeboss.geek.module.guidance.components.GeekBeginnerEditEduExpComponent
import com.twl.meeboss.geek.module.guidance.intent.UpdateEduExpUiIntent
import com.twl.meeboss.geek.module.guidance.interact.EditEduExpInteract
import com.twl.meeboss.geek.module.guidance.interact.GeekGuideEduExpInteract
import com.twl.meeboss.geek.module.guidance.state.GeekBeginnerGuidanceEduExpUiState
import com.twl.meeboss.geek.module.guidance.viewmodel.GeekBeginnerAddEduExpViewModelFactory
import com.twl.meeboss.geek.module.guidance.viewmodel.IGeekBeginnerAddEduExpViewModel
import com.twl.meeboss.geek.module.me.quickcomplete.model.GeekResumeAnalyzeEventData
import com.twl.meeboss.geek.module.me.quickcomplete.model.QuickCompleteType
import com.twl.meeboss.geek.module.register.activity.GeekRegisterGuidanceActivity
import dagger.hilt.android.AndroidEntryPoint


@RouterPage(path = [GeekRouterPath.GEEK_BEGINNER_GUIDE_ADD_EDU_EXP_PAGE])
@AndroidEntryPoint
class GeekBeginnerAddEduExpActivity() : BaseMviActivity<IGeekBeginnerAddEduExpViewModel>() {

    private var useLocalData: Boolean = false

    //编辑教育经历相关交互
    private val editEduExpInteract: GeekGuideEduExpInteract by lazy {
        GeekGuideEduExpInteract(this, getBean = {
            viewModel.uiStateFlow.value.bean
        }, sendUiIntent = {
            viewModel.sendUiIntent(it)
        })
    }

    @Inject
    lateinit var viewModelFactory: GeekBeginnerAddEduExpViewModelFactory

    override val viewModel: IGeekBeginnerAddEduExpViewModel by viewModels {
        viewModelFactory.useLocalData = useLocalData
        viewModelFactory
    }

    override fun preInit(intent: Intent) {
        useLocalData = intent.getBooleanExtra(BUNDLE_BOOLEAN, false)

        val scene = intent.getIntExtra(BUNDLE_INT, EditPageScene.SINGLE_PAGE)
        val guidanceType = intent.getSerializableExtra(BUNDLE_TYPE) as? GuidanceType ?: GuidanceType.BeginnerGuidance
        viewModel.sendUiIntent(UpdateEduExpUiIntent.Init(scene,guidanceType))
    }

    override fun initData() {
        viewModel.saveResult.observe(this) {
            if (it) {
                viewModel.uiStateFlow.value.let { state ->
                    if (state.isInflow()) {
                        sendObjectLiveEvent(GeekEventBusKey.GEEK_COMPLETE_MANUALLY_FINISH,
                            GeekResumeAnalyzeEventData(true, QuickCompleteType.MANUALLY)
                        )
                        ActivityUtils.finishToActivity(
                            if (viewModel.uiStateFlow.value.guidanceType == GuidanceType.RegisterGuidance) GeekRegisterGuidanceActivity::class.java else GeeBeginnerGuidanceActivity::class.java,
                            false
                        )
                    } else {
                        T.ss(R.string.common_saved_successfully)
                        if (viewModel.canAddMore) {
                            GeekPageRouter.jumpToGeekBeginnerAddEduExpActivity(
                                this,
                                EditPageScene.SINGLE_PAGE,
                                guidanceType = state.guidanceType,
                                local = useLocalData
                            )
                        }
                        finish()
                    }
                }

            }
        }
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        GeekBeginnerAddEduExpContent(uiState = uiState, eduExpInteract = editEduExpInteract){
            viewModel.pointHelper.backClick()
            onBackPressed()
        }
    }

    override fun onBackPressed() {
        val currentState = viewModel.uiStateFlow.value
        if (currentState.hasChange()) {
            showExitDialog()
            return
        }
        super.onBackPressed()
    }

    private fun showExitDialog() {
        alertContentChangesDialog(
            onConfirm = {
                finish()
            }
        )
    }

}

@Composable
fun GeekBeginnerAddEduExpContent(
    uiState: GeekBeginnerGuidanceEduExpUiState = GeekBeginnerGuidanceEduExpUiState(),
    eduExpInteract: EditEduExpInteract = EditEduExpInteract(),
    onBackPress: (Context) -> Unit = {}
) {
    XTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            XTitleBar(onBackClick = onBackPress)
            GeekBeginnerEditEduExpComponent(
                Modifier.fillMaxSize(),
                uiState = uiState,
                interact = eduExpInteract
            )
        }
    }
}

@Preview
@Composable
private fun PreviewGeekBeginnerAddEduExpContent1() {
    GeekBeginnerAddEduExpContent(uiState = GeekBeginnerGuidanceEduExpUiState(scene = EditPageScene.SINGLE_PAGE))

}

@Preview(locale = "de")
@Composable
private fun PreviewGeekBeginnerAddEduExpContent2() {
    GeekBeginnerAddEduExpContent(uiState = GeekBeginnerGuidanceEduExpUiState(scene = EditPageScene.FLOW_PAGE))

}
