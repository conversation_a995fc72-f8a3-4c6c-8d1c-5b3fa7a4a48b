package com.twl.meeboss.geek.module.guidance.model

import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.base.model.geek.GeekGetExpectationBean
import com.twl.meeboss.base.model.geek.GeekUserInfoBean
import com.twl.meeboss.base.model.profile.EducationExperienceBean
import com.twl.meeboss.base.model.profile.WorkExperienceBean

/**
 * 检查当前用户是否完成信息
 */
data class GeekGuidanceUserInfoV1(val completeWorkExp: <PERSON>olean,
                                  val completeEduExp: <PERSON>olean,
                                  val completeBaseInfo: <PERSON>olean,
                                  val completeSkills: <PERSON>olean,
                                  val completeLanguage: <PERSON><PERSON><PERSON>,
                                  val completeCertificate: <PERSON><PERSON>an,
                                  val noWorkExp: <PERSON>olean,
                                  val workExp: WorkExperienceBean? = null,
                                  val eduExp: EducationExperienceBean? = null,
                                  val baseInfo: GeekUserInfoBean,
                                  val firstConfirmComplete: Boolean = false,
                                  val expectation: GeekGetExpectationBean? = null
) : BaseEntity