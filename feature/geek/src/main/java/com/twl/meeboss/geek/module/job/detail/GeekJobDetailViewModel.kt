package com.twl.meeboss.geek.module.job.detail

import androidx.compose.foundation.ScrollState
import com.twl.meeboss.base.R
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.constants.JobConstants
import com.twl.meeboss.base.eventbus.sendStringLiveEvent
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.http.HttpErrorCodes
import com.twl.meeboss.base.model.enumeration.CollectStatus
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.export_share.model.JobTabBean
import com.twl.meeboss.common.exp.ApiException
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.geek.repos.GeekJobRepository
import com.twl.meeboss.geek.repos.GeekRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * @author: 冯智健
 * @date: 2024年07月12日 21:09
 * @description:
 */
@HiltViewModel
class GeekJobDetailViewModel @Inject constructor(
    private val jobRepo: GeekJobRepository,
    private val repos: GeekRepository
) : BaseMviViewModel<GeekJobDetailUiState, GeekJobDetailUiIntent>() {

    override fun initUiState() = GeekJobDetailUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is GeekJobDetailUiIntent.GetJobDetail -> getJobDetail(intent.securityId)
            is GeekJobDetailUiIntent.CollectJob -> collectJob(intent.securityId, intent.collected)
            is GeekJobDetailUiIntent.CloseTabCard -> closeTabCard(intent.tabBean)
        }
    }

    private fun getJobDetail(securityId: String) {
        requestData(
            enableLoadState = true,
            request = {
                jobRepo.getJobDetail(securityId)
            },
            success = {
                it?.let { result ->
                    sendUiState {
                        copy(jobDetail = result)
                    }
                    sendUiState {
                        copy(collected = result.jobDetailBizInfo?.favoriteJob != CollectStatus.TO_COLLECTED)
                    }
                }
            },
            fail = {
                if (it is ApiException) {
                    when (it.code) {
                        JobConstants.JOB_STATUS_DELETE, JobConstants.JOB_STATUS_NOT_FOUND, HttpErrorCodes.BOSS_UNABLE -> {
                            T.ss(it.message)
                            sendUiState {
                                copy(
                                    finishPage = true
                                )
                            }
                        }
                        JobConstants.JOB_STATUS_OFFLINE -> {
                            sendUiState {
                                copy(
                                    jobOffline = true,
                                    jobOfflineMessage = it.message ?: ""
                                )
                            }
                        }
                        else -> {
                            T.ss(it.message)
                        }
                    }
                }
                XLog.error(TAG, "getJobDetail fail: ${it.message}")
            }
        )
    }

    private fun collectJob(securityId: String, collected: Boolean) {
        requestData(
            request = {
                jobRepo.collectJob(
                    securityId,
                    if (collected) CollectStatus.TO_COLLECTED else CollectStatus.TO_UNCOLLECTED
                )
            },
            success = {
                sendStringLiveEvent(EventBusKey.GEEK_COLLECT_JOB_CHANGE, securityId)
                sendUiState {
                    copy(collected = collected)
                }
                if (collected) {
                    T.ss(R.string.common_saved_successfully)
                } else {
                    T.ss(R.string.common_unsaved)
                }
            },
            fail = {
                XLog.error(TAG, "collectJob fail: ${it.message}")
                T.ss(it.message)
            }
        )
    }

    /**
     * 关闭新手引导卡片
     */
    private fun closeTabCard(tabBean: JobTabBean) {
        requestData(
            enableLoadState = false,
            request = {
                repos.geekSettingCloseTab(scene = tabBean.scene, type = tabBean.type)
            },
            success = {
                it?.let { _ ->
                    sendUiState {
                        copy(jobDetail = jobDetail?.copy(tab = null))
                    }
                }
            },
            fail = {
                XLog.error(TAG, it.message)
            }
        )
    }
}

data class GeekJobDetailUiState(
    val finishPage: Boolean = false,
    val collected: Boolean = false,
    val scrollState: ScrollState = ScrollState(0),
    val jobDetail: JobDetailResult? = null,
    val jobOffline:Boolean = false,
    val jobOfflineMessage:String = "",
) : IUiState

sealed class GeekJobDetailUiIntent : IUiIntent {
    data class GetJobDetail(val securityId: String) : GeekJobDetailUiIntent()
    data class CollectJob(val securityId: String, val collected: Boolean) : GeekJobDetailUiIntent()
    // 关闭新手引导卡片
    data class CloseTabCard(val tabBean: JobTabBean) : GeekJobDetailUiIntent()
}