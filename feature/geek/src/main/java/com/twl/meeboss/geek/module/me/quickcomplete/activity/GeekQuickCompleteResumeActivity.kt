package com.twl.meeboss.geek.module.me.quickcomplete.activity

import android.content.Intent
import androidx.activity.result.ActivityResult
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.constants.BUNDLE_INT
import com.twl.meeboss.base.constants.BUNDLE_OBJECT
import com.twl.meeboss.base.constants.BUNDLE_STRING
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.manager.ActivityResultManager
import com.twl.meeboss.base.manager.IActivityResult
import com.twl.meeboss.base.model.ResumeItemBean
import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.core.ui.component.XDivider
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.GeekEventBusKey
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.export.GeekRouterPath
import com.twl.meeboss.geek.export.ResumeAnalysisType
import com.twl.meeboss.geek.module.me.quickcomplete.component.CompleteMethodItem
import com.twl.meeboss.geek.module.me.quickcomplete.model.GeekResumeAnalyzeEventData
import com.twl.meeboss.geek.utils.GeekPointReporter
import dagger.hilt.android.AndroidEntryPoint
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@RouterPage(path = [GeekRouterPath.GEEK_QUICK_COMPLETE_RESUME_PAGE])
@AndroidEntryPoint
class GeekQuickCompleteResumeActivity : BaseMviActivity<GeekQuickCompleteViewModel>() {

    override val viewModel: GeekQuickCompleteViewModel by viewModels()
    private var resumeAnalysisType: String = ResumeAnalysisType.PROFILE_UPDATE
    private var securityId:String? = null
    private lateinit var activityResultManager: ActivityResultManager
    private var source:ChatSource = ChatSource.Detail
    private val REQ_CODE_CHOOSE_RESUME = 1002

    override fun preInit(intent: Intent) {
        activityResultManager = ActivityResultManager(this).register(REQ_CODE_CHOOSE_RESUME)
        resumeAnalysisType = intent.getStringExtra(BUNDLE_STRING)?:ResumeAnalysisType.PROFILE_UPDATE
        securityId = intent.getStringExtra(GeekPageRouter.BUNDLE_SECURITY_ID)
        source = intent.getSerializableExtra(BUNDLE_OBJECT) as? ChatSource ?: ChatSource.Detail
        liveEventBusObserve<GeekResumeAnalyzeEventData>(GeekEventBusKey.GEEK_CHAT_APPLY_GUIDE_FINISH){
            if (it.isSuccess) {
                finish()
            }
        }
        liveEventBusObserve<Boolean>(GeekEventBusKey.GEEK_RESUME_ANALYZE_PROFILE_UPDATE_FINISH){
            if (it) {
                finish()
            }
        }
        GeekPointReporter.quickCompleteShow(resumeAnalysisType)
    }

    private fun showChooseResumeDialog() {
        activityResultManager.startActivityForResult(REQ_CODE_CHOOSE_RESUME,object :IActivityResult{
            override fun getIntent(): Intent = GeekChooseResumeActivity.getIntent(this@GeekQuickCompleteResumeActivity)

            override fun sendCallback(result: ActivityResult) {
                val resultIntent = result.data
                resultIntent?.apply {
                    val index = resultIntent.getIntExtra(BUNDLE_INT,0)
                    val resumeItemBean = resultIntent.getSerializableExtra(BUNDLE_OBJECT) as ResumeItemBean
                    GeekFileResumeAnalyzeActivity.intent(this@GeekQuickCompleteResumeActivity, resumeAnalysisType, resumeItemBean, securityId, source = source)
                }
            }

        })
    }

    override fun initData() {
    }

    @Composable
    override fun ComposeContent() {
        GeekQuickCompleteContent(
            titleId = if (resumeAnalysisType == ResumeAnalysisType.PROFILE_UPDATE) R.string.geek_resume_analysis_update else R.string.geek_resume_analysis,
            onSelectResumeClick = {
                showChooseResumeDialog()
//                GeekPointReporter.quickCompletePathContinue(1)
                GeekPointReporter.quickCompletePath(1, resumeAnalysisType)
            },
            onLinkedinClick = {
                GeekLinkedinAnalyzeActivity.intent(this, resumeAnalysisType, securityId,source)
//                GeekPointReporter.quickCompletePathContinue(2)
                GeekPointReporter.quickCompletePath(2, resumeAnalysisType)
            }
        )
    }

}

@Composable
fun GeekQuickCompleteContent(
    modifier: Modifier = Modifier,
    titleId:Int = R.string.geek_resume_analysis,
    onSelectResumeClick:()->Unit = {},
    onLinkedinClick:()->Unit = {},
) {
    XTheme {
        Column(modifier = modifier
            .background(Color.White)
            .fillMaxSize()) {

            XTitleBar(
                title = stringResource(id = titleId)
            )

            Column(
                modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 28.dp, bottom = 20.dp)
                    .border(
                        width = 1.dp,
                        color = BlackEBEBEB,
                        shape = RoundedCornerShape(size = 8.dp)
                    )
            ) {

                CompleteMethodItem(
                    icon = R.drawable.geek_icon_quick_complete_resume,
                    title = stringResource(id = R.string.common_resume),
                    onClick = onSelectResumeClick
                )
                XDivider()
                CompleteMethodItem(
                    icon = R.drawable.geek_icon_quick_complete_linkedin,
                    title = stringResource(id = R.string.common_linkedin_profile),
                    onClick = onLinkedinClick
                )

            }

        }
    }
}

@Preview
@Composable
private fun PreviewGeekQuickCompleteContent() {
    GeekQuickCompleteContent(
        titleId = R.string.geek_resume_analysis_update
    )
}

@HiltViewModel
class GeekQuickCompleteViewModel @Inject constructor(
//    private val geekRepo: GeekRepository,
) : BaseMviViewModel<GeekQuickCompleteUiState, GeekQuickCompleteUiIntent>() {
    override fun initUiState(): GeekQuickCompleteUiState = GeekQuickCompleteUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is GeekQuickCompleteUiIntent.GetResumeList ->{
            }
            else->{

            }

        }
    }


}

data class GeekQuickCompleteUiState(
    val resumeList: List<ResumeItemBean> = listOf(),
) : IUiState

sealed class GeekQuickCompleteUiIntent : IUiIntent {
    data object GetResumeList : GeekQuickCompleteUiIntent()
}