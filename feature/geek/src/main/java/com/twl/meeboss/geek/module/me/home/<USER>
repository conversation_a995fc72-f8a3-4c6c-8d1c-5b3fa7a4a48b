package com.twl.meeboss.geek.module.me.home

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.twl.meeboss.base.model.CityBean
import com.twl.meeboss.base.model.geek.GeekUserInfo
import com.twl.meeboss.base.model.geek.GeekUserInfoBean
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.utils.observeLifecycleEvents
import com.twl.meeboss.geek.foundation.moduleservice.GeekUserInfoService
import com.twl.meeboss.geek.module.me.home.components.GeekMeContent
import com.twl.meeboss.geek.module.me.home.components.GeekMeHeader

@Composable
fun GeekMePage(vm: GeekMeViewModel = viewModel()) {
    vm.observeLifecycleEvents(LocalLifecycleOwner.current.lifecycle)
    val geekUserInfo = GeekUserInfoService.geekUserInfo.observeAsState(null)
    val uiState by vm.uiStateFlow.collectAsStateWithLifecycle()
    GeekMePageContent(
        geekUserInfo = geekUserInfo.value,
        hideBanner = uiState.hideBanner,
        onBannerClose = {
            vm.sendUiIntent(GeekMeUiIntent.CloseBanner(it))
        },
        onClearRedDot = {
            vm.sendUiIntent(GeekMeUiIntent.ClearRedDot)
        }
    )
}

@Composable
private fun GeekMePageContent(
    geekUserInfo: GeekUserInfo? = null,
    hideBanner: Boolean = false,
    onBannerClose: (Int) -> Unit = {},
    onClearRedDot: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(COLOR_F5F5F5)
            .verticalScroll(rememberScrollState())
    ) {
        GeekMeHeader(geekUserInfo)
        GeekMeContent(
            geekUserInfo = geekUserInfo,
            hideBanner = hideBanner,
            onCloseBanner = onBannerClose,
            onClearRedDot = onClearRedDot
        )
    }
}

@Preview
@Composable
private fun PreviewGeekMePage() {
    GeekMePageContent(GeekUserInfo(GeekUserInfoBean(
        firstName = "Donald",
        lastName = "Trump",
        city = CityBean(city = "Washington", country = "Unite State", shortCountry = "USA"),
        avatar = "https://p0.itc.cn/images01/20220319/9cbf818d9bcb449195e5285cde381e93.jpeg")
    ))
}