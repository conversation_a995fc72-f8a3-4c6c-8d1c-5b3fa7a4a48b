package com.twl.meeboss.geek.module.talentpool.manager

import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.sendObjectLiveEvent
import com.twl.meeboss.base.foundation.viewmodel.BaseViewModel
import com.twl.meeboss.base.http.HttpErrorCodes
import com.twl.meeboss.common.preference.SpManager
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.HttpResult
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.core.ui.dialog.ConfirmDialog
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.api.GeekApi
import com.twl.meeboss.geek.model.bean.GeekTalentPoolChangeData
import com.twl.meeboss.geek.module.talentpool.bottomsheet.GeekJoinTalentPoolCommentBottomSheet
import com.twl.meeboss.geek.module.talentpool.bottomsheet.GeekJoinTalentPoolGuideBottomSheet
import com.twl.meeboss.geek.module.talentpool.bottomsheet.GeekJoinTalentPoolMoreBottomSheet
import com.twl.meeboss.geek.utils.GeekPointReporter

class GeekTalentPoolManager(val activity:FragmentActivity) {
    private val SP_KEY_GEEK_TALENT_POOL_GUIDE = "sp_key_geek_talent_pool_guide"
    val joinTalentPoolSuccess:MutableLiveData<String?> = MutableLiveData(null)
    val joinTalentPoolNotComplete:MutableLiveData<Boolean> = MutableLiveData(false)
    val exitTalentPoolSuccess:MutableLiveData<Boolean> = MutableLiveData(false)
    private var commentDialog:GeekJoinTalentPoolCommentBottomSheet? = null

    private val mViewModel: GeekTalentPoolViewModel by lazy {
        ViewModelProvider(activity)[GeekTalentPoolViewModel::class.java].also {
            it.joinTalentPoolSuccess = joinTalentPoolSuccess
            it.joinTalentPoolNotComplete = joinTalentPoolNotComplete
            it.exitTalentPoolSuccess = exitTalentPoolSuccess
        }
    }

    fun joinTalentPool(
        source:JoinTalentPoolSource,
        companyId:String,
        companyLogo:String = "",
        companyName:String = "",
    ) {
        val hasShowGuide = SpManager.getUserBoolean(SP_KEY_GEEK_TALENT_POOL_GUIDE, false)
        if (!hasShowGuide) {
            SpManager.putUserBoolean(SP_KEY_GEEK_TALENT_POOL_GUIDE, true)
            GeekJoinTalentPoolGuideBottomSheet.newInstance(confirmText = R.string.common_join.toResourceString()) {
                internalJoinTalentPool(
                    source = source,
                    companyId = companyId,
                    companyLogo = companyLogo,
                    companyName = companyName,
                )
            }.showSafely(activity)
        } else {
            internalJoinTalentPool(
                source = source,
                companyId = companyId,
                companyLogo = companyLogo,
                companyName = companyName,
            )
        }
    }

    private fun internalJoinTalentPool(
        source:JoinTalentPoolSource,
        companyId:String,
        companyLogo:String = "",
        companyName:String = "",
    ) {
        mViewModel.joinTalentPool(source = source, companyId = companyId, comment = "", isJoin = true) {
            showCommentDialog(
                source = source,
                companyId = companyId,
                companyLogo = companyLogo,
                companyName = companyName,
            )
        }
    }

    private fun showCommentDialog(
        source:JoinTalentPoolSource,
        companyId:String,
        companyLogo:String = "",
        companyName:String = "",
        comment:String = "",
    ) {
        commentDialog = GeekJoinTalentPoolCommentBottomSheet.newInstance(
            companyLogo = companyLogo,
            companyName = companyName,
            originComment = comment,
            onSaveClick = { result ->
                mViewModel.joinTalentPool(source = source, companyId = companyId, comment = result, isJoin = false) { isSuccess ->
                    if (isSuccess) {
                        GeekPointReporter.commentTalentPool(type = if(comment.isEmpty()) "1" else "2", comment = result, companyId = companyId)
                        T.ss(R.string.common_saved_successfully.toResourceString())
                        commentDialog?.dismissSafely()
                    }
                }
            }
        )
        commentDialog?.showSafely(activity)
    }

    fun exitTalentPool(source:JoinTalentPoolSource, companyId:String, companyName:String?) {
        ConfirmDialog.newInstance(
            title = R.string.geek_talent_pool_exit_confirm_title.toResourceString(),
            content = R.string.geek_talent_pool_exit_confirm_subtitle.toResourceString(companyName?:""),
            confirmText = R.string.common_button_confirm.toResourceString(),
            cancelText = R.string.common_button_cancel.toResourceString(),
            onConfirm = {
                mViewModel.exitTalentPool(source, companyId)
            }
        ).showSafely(activity)
    }

    fun showMoreDialog(
        source:JoinTalentPoolSource,
        hasJoined:Boolean,
        companyId:String,
        companyLogo:String = "",
        companyName:String = "",
        comment:String = "",
        ) {
        GeekJoinTalentPoolMoreBottomSheet.newInstance(
            hasJoined = hasJoined,
            comment = comment,
            onCommentClick = {
                showCommentDialog(source, companyId, companyLogo, companyName, comment)
            },
            onGuideClick = {
                GeekJoinTalentPoolGuideBottomSheet.newInstance(confirmText = R.string.common_got_it_button.toResourceString()){}.showSafely(activity)
            },
            onExitClick = {
                exitTalentPool(source = source, companyId = companyId,companyName = companyName)
            }
        ).showSafely(activity)
    }
}

class GeekTalentPoolViewModel:BaseViewModel() {
    var joinTalentPoolSuccess:MutableLiveData<String?>? = null
    var joinTalentPoolNotComplete:MutableLiveData<Boolean>? = null
    var exitTalentPoolSuccess:MutableLiveData<Boolean>? = null

    fun joinTalentPool(source:JoinTalentPoolSource, companyId:String, comment:String, isJoin:Boolean, requestFinish:((isSuccess:Boolean)->Unit)? = null) {
        async {
            val trimComment = comment.trim()
            val api = getService(GeekApi::class.java)
            val result = api.joinTalentPool(companyId = companyId, comment = trimComment)
            if (isJoin) {
                val hasComplete = !(result is HttpResult.ApiError && result.code == HttpErrorCodes.GEEK_JOIN_TALENT_POOL_NOT_COMPLETE)
                GeekPointReporter.joinTalentPoolClick(hasComplete, companyId)
            }
            requestFinish?.invoke(result.isSuccess)
            if (result.isSuccess) {
                GeekPointReporter.joinTalentPoolSuccess(source.type.toString(), companyId)
                sendObjectLiveEvent(key = EventBusKey.GEEK_TALENT_POOL_STATUS_CHANGE, value = GeekTalentPoolChangeData(isJoin = true, comment = trimComment, companyId = companyId))
                joinTalentPoolSuccess?.postValue(trimComment)
            } else if (result is HttpResult.ApiError) {
                if (result.code == HttpErrorCodes.GEEK_JOIN_TALENT_POOL_NOT_COMPLETE) {
                    joinTalentPoolNotComplete?.postValue(true)
                } else {
                    T.ss(result.message)
                }
            } else {
                T.ss(result.exceptionOrNull()?.message)
            }
        }
    }

    fun exitTalentPool(source:JoinTalentPoolSource, companyId:String) {
        async {
            val api = getService(GeekApi::class.java)
            val result = api.exitTalentPool(companyId = companyId)
            if (result.isSuccess) {
                GeekPointReporter.exitTalentPoolSuccess(source.type.toString(), companyId)
                sendObjectLiveEvent(key = EventBusKey.GEEK_TALENT_POOL_STATUS_CHANGE, value = GeekTalentPoolChangeData(isJoin = false, comment = "", companyId =  companyId))
                T.ss(R.string.common_updated_successfully.toResourceString())
                exitTalentPoolSuccess?.postValue(true)
            } else if (result is HttpResult.ApiError) {
                T.ss(result.message)
            } else {
                T.ss(result.exceptionOrNull()?.message)
            }
        }
    }
}

enum class JoinTalentPoolSource(val type:Int) {
    COMPANY_LIST(1),
    COMPANY_DETAIL(2),
    TALENT_POOL_LIST(3)
}