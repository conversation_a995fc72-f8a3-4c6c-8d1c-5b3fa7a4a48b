package com.twl.meeboss.geek.module.guidance.activity

import javax.inject.Inject
import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.constants.BUNDLE_BOOLEAN
import com.twl.meeboss.base.constants.BUNDLE_INT
import com.twl.meeboss.base.constants.BUNDLE_TYPE
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.alertContentChangesDialog
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.ChatGuidanceFlowStep
import com.twl.meeboss.geek.export.EditPageScene
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.export.GeekRouterPath
import com.twl.meeboss.geek.export.GuidanceType
import com.twl.meeboss.geek.module.guidance.components.GeekBeginnerEditWorkExpComponent
import com.twl.meeboss.geek.module.guidance.intent.UpdateWorkExperience
import com.twl.meeboss.geek.module.guidance.interact.GeekGuideEditWorkExperienceInteract
import com.twl.meeboss.geek.module.guidance.interact.WorkExperienceInteract
import com.twl.meeboss.geek.module.guidance.state.GeekBeginnerGuidanceEditWorkExperienceUiState
import com.twl.meeboss.geek.module.guidance.viewmodel.GeekBeginnerAddWorkViewModelFactory
import com.twl.meeboss.geek.module.guidance.viewmodel.IGeekBeginnerAddWorkExpViewModel
import dagger.hilt.android.AndroidEntryPoint

@RouterPage(path = [GeekRouterPath.GEEK_BEGINNER_GUIDE_ADD_WORK_EXP_PAGE])
@AndroidEntryPoint
class GeekBeginnerAddWorkExpActivity() : BaseMviActivity<IGeekBeginnerAddWorkExpViewModel>() {

    private var useLocalData: Boolean = false

    @Inject
    lateinit var viewModelFactory: GeekBeginnerAddWorkViewModelFactory

    override val viewModel: IGeekBeginnerAddWorkExpViewModel by viewModels {
        viewModelFactory.useLocalData = useLocalData
        viewModelFactory
    }

    //编辑工作经验相关交互
    private val editWorkExpInteract: GeekGuideEditWorkExperienceInteract by lazy {
        GeekGuideEditWorkExperienceInteract(this, sendIntent = {
            viewModel.sendUiIntent(it)
        }, getBean = {
            viewModel.uiStateFlow.value.bean
        })
    }

    override fun preInit(intent: Intent) {
        useLocalData = intent.getBooleanExtra(BUNDLE_BOOLEAN, false)

        val scene = intent.getIntExtra(BUNDLE_INT, EditPageScene.SINGLE_PAGE)
        val guidanceType = intent.getSerializableExtra(BUNDLE_TYPE) as? GuidanceType ?:GuidanceType.BeginnerGuidance
        viewModel.sendUiIntent(UpdateWorkExperience.Init(scene,guidanceType))
    }

    override fun initData() {
        viewModel.saveResult.observe(this) { success ->
            viewModel.uiStateFlow.value.run {
                if (success) {
                    if (isInFlow()) {
                        if (guidanceType == GuidanceType.ChatGuidance || guidanceType == GuidanceType.RegisterGuidance) {
                            GeekPageRouter.jumpToGeekChatGuidanceFlow(
                                this@GeekBeginnerAddWorkExpActivity,
                                ChatGuidanceFlowStep.EDUCATION_EXPERIENCE,
                                guidanceType
                            )
                        }
                    } else {
                        T.ss(R.string.common_saved_successfully)
                        if (viewModel.canAddMore) {
                            GeekPageRouter.jumpToGeekBeginnerAddWorkExpActivity(
                                context = this@GeekBeginnerAddWorkExpActivity,
                                scene = scene,
                                guidanceType = guidanceType,
                                local = useLocalData
                            )
                        }
                        finish()
                    }
                }
            }

        }
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        GeekBeginnerAddWorkExpContent(
            uiState = uiState,
            workExperienceInteract = editWorkExpInteract
        ){
            viewModel.pointHelper.backClick()
            onBackPressed()
        }
    }

    override fun onBackPressed() {
        val currentState = viewModel.uiStateFlow.value
        if (currentState.hasChange()) {
            showExitDialog()
            return
        }
        super.onBackPressed()
    }

    private fun showExitDialog() {
        alertContentChangesDialog(
            onConfirm = {
                finish()
            }
        )
    }

}

@Composable
fun GeekBeginnerAddWorkExpContent(
    uiState: GeekBeginnerGuidanceEditWorkExperienceUiState = GeekBeginnerGuidanceEditWorkExperienceUiState(),
    workExperienceInteract: WorkExperienceInteract = WorkExperienceInteract(),
    onBackPress: (Context) -> Unit = {},
): Unit {

    XTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            XTitleBar(onBackClick = onBackPress)
            GeekBeginnerEditWorkExpComponent(
                modifier = Modifier.fillMaxSize(),
                uiState = uiState,
                interact = workExperienceInteract,
            )
        }
    }

}

@Preview
@Composable
private fun PreviewGeekBeginnerAddWorkExpContent1() {
    GeekBeginnerAddWorkExpContent(uiState = GeekBeginnerGuidanceEditWorkExperienceUiState(scene = EditPageScene.SINGLE_PAGE, showNoWorkExpCheckbox = false))
}

@Preview
@Composable
private fun PreviewGeekBeginnerAddWorkExpContent2() {
    GeekBeginnerAddWorkExpContent(uiState = GeekBeginnerGuidanceEditWorkExperienceUiState(scene = EditPageScene.FLOW_PAGE))
}
