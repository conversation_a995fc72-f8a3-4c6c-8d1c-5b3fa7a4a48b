package com.twl.meeboss.geek.api

import com.twl.meeboss.base.model.PageList
import com.twl.meeboss.base.model.enumeration.CollectStatus
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.core.network.HttpResult
import com.twl.meeboss.geek.model.bean.GeekF1PageList
import com.twl.meeboss.base.model.job.JobItemResult
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * @author: 冯智健
 * @date: 2024年07月11日 11:52
 * @description:
 */
interface GeekJobApi {
    /**
     * 获取推荐的职位列表
     */
    @GET("api/jobseeker/jobf1/list")
    suspend fun getRecommendJobList(
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): HttpResult<GeekF1PageList>

    /**
     * 获取看过我的职位列表
     */
    @GET("api/jobseeker/interaction/viewedMe")
    suspend fun getViewedMeJobList(
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): HttpResult<PageList<JobItemResult>>

    /**
     * 获取喜欢我的职位列表
     */
    @GET("api/jobseeker/interaction/favoredMe")
    suspend fun getFavoredMeJobList(
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): HttpResult<PageList<JobItemResult>>

    /**
     * 获取职位详情
     */
    @GET("api/jobseeker/job/detail")
    suspend fun getJobDetail(@Query("securityId") securityId: String): HttpResult<JobDetailResult>

    @GET("api/explore/job/detail")
    suspend fun getExploreJobDetail(
        @Query("securityId") securityId: String,
        @Query("scene") scene: Int = 0,
        @Query("jobId") jobId: Int = 0,
    ): HttpResult<JobDetailResult>

    /**
     * 收藏/取消收藏职位
     */
    @FormUrlEncoded
    @POST("api/jobseeker/interaction/favor")
    suspend fun collectJob(
        @Field("securityId") securityId: String,
        @Field("deleted") deleted: @CollectStatus Int  // 0：收藏；1：删除
    ): HttpResult<Any?>
}