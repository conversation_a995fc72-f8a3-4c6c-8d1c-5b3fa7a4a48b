package com.twl.meeboss.geek.module.register

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.view.KeyEvent
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.components.dialog.CommonDataType
import com.twl.meeboss.base.components.dialog.DialogHelper
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.showRegisterRightMoreDialog
import com.twl.meeboss.base.main.router.BaseServiceRouter.afterLogin
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.export.GeekRouterPath
import com.twl.meeboss.geek.module.preference.viewmodel.GeekJobPreferenceBaseUiIntent
import com.twl.meeboss.geek.module.preference.viewmodel.GeekRegisterUiIntent
import com.twl.meeboss.geek.module.preference.viewmodel.GeekRegisterViewModel
import com.twl.meeboss.geek.module.register.components.GeekRegisterAdditionalInfoPage
import com.twl.meeboss.geek.module.register.components.GeekRegisterJobCategoryPage
import com.twl.meeboss.geek.module.register.components.GeekRegisterLocationPage
import com.twl.meeboss.geek.module.register.components.GeekRegisterSalaryPage
import com.twl.meeboss.geek.module.register.model.GeekRegisterPageType
import com.twl.meeboss.geek.utils.GeekRegisterApmReporter
import com.twl.meeboss.base.model.ab.ABConfigManager
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.model.bean.JobCategory
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
@RouterPage(path = [GeekRouterPath.GEEK_REGISTER_PAGE])
class GeekRegisterActivity : BaseMviActivity<GeekRegisterViewModel>() {
    override val viewModel: GeekRegisterViewModel by viewModels()
    private val dialogHelper by lazy {
        DialogHelper(this)
    }
    override fun preInit(intent: Intent) {
        viewModel.sendUiIntent(GeekRegisterUiIntent.Init)
        viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.InitJobTitles(intent))
        viewModel.jumpToMain.observe(this) {
            if (it) {
                jumpToMain()
            }
        }
        viewModel.jumpToGuidance.observe(this) {
            if (it) {
                GeekPageRouter.jumpToGeekRegisterGuidanceActivity(context = this, canGoBack = true)
            }
        }
        viewModel.adjustResize.observe(this) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                window.setDecorFitsSystemWindows(it)
            } else {
                if (it) {
                    window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
                } else {
                    window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
                }
            }
        }
    }
    override fun initData() {
        GeekRegisterApmReporter.reportApm("register","begin")
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        val geekRegisterPageType = uiState.geekRegisterPageType
        XTheme {
            Surface(
                modifier = Modifier
                    .background(Color.White)
                    .fillMaxSize()
            ) {
                AnimatedVisibility(visible = geekRegisterPageType == GeekRegisterPageType.JOB_TITLE) {
                    GeekRegisterJobCategoryPage(
                        source = 1,
                        isLoading = uiState.isLoading,
                        onClickRightMore = { showRightMoreDialog(rect = it) },
                        primaryCategories = uiState.primaryCategories,
                        showSecondaryDialog = uiState.showSecondaryDialog,
                        secondaryCategories = uiState.secondaryCategories,
                        selectedPrimaryCategory = uiState.selectedPrimaryCategory,
                        selectedJobCategories = uiState.selectedJobCategories,
                        onClickSave = {
                            viewModel.sendUiIntent(GeekRegisterUiIntent.ClickJobTitleNext)
                            GeekRegisterApmReporter.reportApm("jobTitle")
                        },
                        onSecondaryDialogDismiss = { viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.CloseSecondaryDialog) },
                        onSelectPrimaryCategory = { position, title, code ->
                            if (code == JobCategory.CODE_OTHER) {
                                // 当选择"其它"选项时，显示自定义输入弹窗
                                viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.ShowOtherInputDialog)
                            } else {
                                viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.LoadSecondaryJobCategories(position, title)) 
                            }
                        },
                        onRemoveJobCategory = {
                            viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.RemoveJobCategory(it))
                        },
                        onSecondaryCategoriesSave = { categories  -> viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.SelectJobCategory(categories)) },
                        showOtherInputDialog = uiState.showOtherInputDialog,
                        onOtherInputDialogDismiss = { viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.CloseOtherInputDialog) },
                        onSaveCustomJobCategory = { name -> viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.AddCustomJobCategory(name)) }
                    )
                }
                AnimatedVisibility(visible = geekRegisterPageType == GeekRegisterPageType.LOCATION) {
                    GeekRegisterLocationPage(
                        locationSuggestList = uiState.locationSuggestList,
                        locationSelectedList =  uiState.locationSelectedList,
                        onInputValueChange = {
                            viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.GetSuggestLocation(it.text))
                        },
                        onClickBack = {
                            viewModel.sendUiIntent(GeekRegisterUiIntent.OnBackClick)
                        },
                        onClickSave = {
                            viewModel.sendUiIntent(GeekRegisterUiIntent.ClickLocationNext)
                            GeekRegisterApmReporter.reportApm("location")
                        },
                        onDeleteItem = {
                            viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.DeleteSelectedLocation(it))
                        },
                        onAddItem = {
                            viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.AddSelectedLocation(it))
                            viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.ClearSuggestLocation)
                        },
                        onClickRightMore = {
                            showRightMoreDialog(rect = it)
                        },
                    )
                }
                AnimatedVisibility(visible = geekRegisterPageType == GeekRegisterPageType.SALARY) {
                    GeekRegisterSalaryPage(
                        salaryMinValue = if (uiState.salaryMinValue > 0) uiState.salaryMinValue.toString() else "",
                        salaryCanSave = uiState.salaryCanSave,
                        currentSalaryType = uiState.currentSalaryType,
                        salaryFirstBean = uiState.salaryFirstBean,
                        salarySecondBean = uiState.salarySecondBean,
                        salaryThirdBean = uiState.salaryThirdBean,
                        onValueChange = {
                            viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.SetMinSalaryValue(it))
                        },
                        onClickBack = {
                            viewModel.sendUiIntent(GeekRegisterUiIntent.OnBackClick)
                        },
                        onClickNext = {
                            viewModel.sendUiIntent(GeekRegisterUiIntent.SaveExpectationData)
                            GeekRegisterApmReporter.reportApm("salary")
                        },
                        onSalaryTypeClick = {
                            viewModel.sendUiIntent(GeekJobPreferenceBaseUiIntent.SelectSalaryType(it))
                        },
                        onClickRightMore = {
                            showRightMoreDialog(rect = it)
                        },
                    )
                }
                AnimatedVisibility(visible = geekRegisterPageType == GeekRegisterPageType.ADDITIONAL_INFO) {
                    GeekRegisterAdditionalInfoPage(
                        selectLocationStr = uiState.selectLocationList.joinToString(",") { it.name },
                        selectEmploymentStr = uiState.selectEmploymentList.joinToString(",") { it.name },
                        selectRoleLevelStr = uiState.selectRoleLevelList.joinToString(",") { it.name },
                        saveButtonNext = stringResource(id = R.string.common_next),
                        onClickBack = {
                            viewModel.sendUiIntent(GeekRegisterUiIntent.OnBackClick)
                        },
                        onClickSkip = {
                            viewModel.sendUiIntent(GeekRegisterUiIntent.SkipAdditionalInfo)
                        },
                        onClickSave = {
                            viewModel.sendUiIntent(GeekRegisterUiIntent.SaveAdditionalInfo)
                        },
                        onClickItem = { itemType ->
                            if (itemType == CommonDataType.LEVEL_OF_ROLE) {
                                showSingleChoiceDialog(itemType)
                            } else {
                                showMultiChoiceDialog(itemType)
                            }
                        },
                    )
                }
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            viewModel.sendUiIntent(GeekRegisterUiIntent.OnBackClick)
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun showMultiChoiceDialog(itemType: CommonDataType) {
        if (itemType != CommonDataType.WORKPLACE_TYPE && itemType != CommonDataType.EMPLOYMENT_TYPE) {
            TLog.error(TAG, "showMultiChoiceDialog type not match:${itemType}")
            return
        }
        dialogHelper.showMultiChoiceDialog(
            type = itemType,
            defaultValue = viewModel.getSelectListByType(itemType)
                .map { it.name },
            callback = {
                viewModel.sendUiIntent(GeekRegisterUiIntent.SetSelectAdditionalList(itemType, it))

                //自动弹起下一项的弹窗
                if (itemType == CommonDataType.WORKPLACE_TYPE && viewModel.uiStateFlow.value.selectEmploymentList.isEmpty()) {
                    showMultiChoiceDialog(CommonDataType.EMPLOYMENT_TYPE)
                } else if (itemType == CommonDataType.EMPLOYMENT_TYPE && viewModel.uiStateFlow.value.selectRoleLevelList.isEmpty()) {
                    showSingleChoiceDialog(CommonDataType.LEVEL_OF_ROLE)
                }
            },
        )
    }

    private fun showSingleChoiceDialog(itemType: CommonDataType) {
        dialogHelper.showSingleChoiceDialog(
            type = itemType,
            defaultValue = viewModel.getSelectStringByType(itemType),
            callback = {
                viewModel.sendUiIntent(
                    GeekRegisterUiIntent.SetSelectAdditionalSingle(
                        type = itemType,
                        selectValue = it
                    )
                )
            })
    }

    private fun showRightMoreDialog(rect: Rect) {
        showRegisterRightMoreDialog(
            rect,
        )
    }

    private fun jumpToMain() {
        XLog.info(TAG,"afterLogin")
        afterLogin(this)
    }


    companion object{
        fun intent(activity: Activity){
            Intent(activity, GeekRegisterActivity::class.java).apply {
                activity.startActivity(this)
            }
        }
    }
}