<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="100dp"
    android:height="80dp"
    android:viewportWidth="100"
    android:viewportHeight="80">
  <group>
    <clip-path
        android:pathData="M0,0h100v80h-100z"/>
    <path
        android:pathData="M66.85,7.67H86.58C93.84,7.67 99.73,13.56 99.73,20.82V24.11H66.85V7.67Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="83.29"
            android:startY="7.67"
            android:endX="83.29"
            android:endY="24.11"
            android:type="linear">
          <item android:offset="0" android:color="#FF43D098"/>
          <item android:offset="1" android:color="#FF93ECC8"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M23.04,7.67H85.48C61.37,8.99 60.82,56.44 63.56,80H3.31C-0.19,32.22 15,11.87 23.04,7.67Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="47.14"
            android:startY="7.67"
            android:endX="47.14"
            android:endY="80"
            android:type="linear">
          <item android:offset="0" android:color="#FFBAF3DC"/>
          <item android:offset="1" android:color="#FF93ECC8"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M21.66,18.63L56.73,18.63A1.64,1.64 0,0 1,58.37 20.27L58.37,20.27A1.64,1.64 0,0 1,56.73 21.92L21.66,21.92A1.64,1.64 0,0 1,20.01 20.27L20.01,20.27A1.64,1.64 0,0 1,21.66 18.63z"
        android:fillColor="#43D098"/>
    <path
        android:pathData="M19.47,26.3L38.1,26.3A1.64,1.64 0,0 1,39.74 27.95L39.74,27.95A1.64,1.64 0,0 1,38.1 29.59L19.47,29.59A1.64,1.64 0,0 1,17.82 27.95L17.82,27.95A1.64,1.64 0,0 1,19.47 26.3z"
        android:fillColor="#43D098"/>
    <path
        android:pathData="M32.58,44.64L53.58,44.64A1.5,1.5 0,0 1,55.08 46.14L55.08,46.14A1.5,1.5 0,0 1,53.58 47.64L32.58,47.64A1.5,1.5 0,0 1,31.08 46.14L31.08,46.14A1.5,1.5 0,0 1,32.58 44.64z"
        android:fillColor="#43D098"/>
    <path
        android:pathData="M16.87,46.5m-6,0a6,6 0,1 1,12 0a6,6 0,1 1,-12 0"
        android:fillColor="#00BC5E"
        android:fillAlpha="0.2"/>
    <path
        android:pathData="M14.73,45.25L17.36,48.12L25.68,41.55"
        android:strokeLineJoin="round"
        android:strokeWidth="2.19178"
        android:fillColor="#00000000"
        android:strokeColor="#00B86E"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M6.49,2.24C6.67,1.6 7.58,1.6 7.76,2.24L8.91,6.33C9,6.63 9.21,6.88 9.49,7.01L13.02,8.72C13.52,8.96 13.52,9.67 13.02,9.91L9.49,11.62C9.21,11.75 9,12 8.91,12.3L7.76,16.39C7.58,17.03 6.67,17.03 6.49,16.39L5.33,12.3C5.25,12 5.04,11.75 4.76,11.62L1.22,9.91C0.73,9.67 0.73,8.96 1.22,8.72L4.76,7.01C5.04,6.88 5.25,6.63 5.33,6.33L6.49,2.24Z"
        android:fillColor="#00BC5E"/>
    <path
        android:pathData="M79.87,56.64C80.01,56.11 80.76,56.11 80.91,56.64L81.86,60C81.93,60.25 82.1,60.46 82.34,60.57L85.25,61.98C85.65,62.18 85.65,62.76 85.25,62.95L82.34,64.36C82.1,64.47 81.93,64.68 81.86,64.93L80.91,68.3C80.76,68.82 80.01,68.82 79.87,68.3L78.91,64.93C78.84,64.68 78.67,64.47 78.44,64.36L75.53,62.95C75.12,62.76 75.12,62.18 75.53,61.98L78.44,60.57C78.67,60.46 78.84,60.25 78.91,60L79.87,56.64Z"
        android:fillColor="#43D098"/>
  </group>
</vector>
