package com.twl.meeboss.setting.module.account.verify.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.components.LoginCodeTextField
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.setting.R

/**
 * 验证码输入页面
 * 如果要修改，请慎重，涉及到多个页面：邮箱验证码、手机号验证码
 */
@Composable
fun AccountVerifyCodeContent(
    title: String = "",
    subTitle: String = "",
    account: String = "",
    code: String = "",
    count: Int = 0,
    isError: Boolean = false,
    errorMessage: String = "",
    onValueChanged: (String) -> Unit = {},
    onSendCodeClick: () -> Unit = {},
    onSaveClick: () -> Unit = {}
) {
    XTheme {
        Surface(modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)) {
            Column {
                XTitleBar()
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    modifier = Modifier.padding(16.dp, 0.dp, 0.dp, 0.dp),
                    text = title,
                    color = COLOR_222222,
                    fontSize = 28.sp, fontWeight = FontWeight(590)
                )
                Spacer(modifier = Modifier.height(6.dp))
                Text(
                    modifier = Modifier.padding(16.dp, 0.dp),
                    text = "${subTitle}\n${account}",
                    color = COLOR_484848,
                    fontSize = 14.sp, fontWeight = FontWeight.W400
                )
                Spacer(modifier = Modifier.height(10.dp))
                // 验证码输入框
                LoginCodeTextField(
                    code = code,
                    modifier = Modifier.padding(6.dp, 0.dp),
                    isError = isError,
                    errorMessage = errorMessage,
                    onValueChanged = {
                        onValueChanged(it)
                    }
                )
                Spacer(modifier = Modifier.height(10.dp))
                Row {
                    Text(
                        text = stringResource(id = R.string.login_haven_not_receive_a_code),
                        modifier = Modifier.padding(16.dp, 0.dp, 0.dp, 0.dp),
                        color = Black888888,
                        fontSize = 14.sp, fontWeight = FontWeight.Normal
                    )
                    if (count == 0) {
                        Text(
                            text = stringResource(id = R.string.common_send_again),
                            modifier = Modifier
                                .padding(6.dp, 0.dp)
                                .clickable {
                                    onSendCodeClick()
                                },
                            textDecoration = TextDecoration.Underline,
                            color = COLOR_222222,
                            fontSize = 14.sp, fontWeight = FontWeight.Medium)
                    } else {
                        Text(
                            text = "${stringResource(id = R.string.common_send_again)} (${count}s)",
                            modifier = Modifier.padding(6.dp, 0.dp),
                            color = Black888888,
                            fontSize = 14.sp, fontWeight = FontWeight.Normal
                        )
                    }
                }
                Spacer(modifier = Modifier.weight(1F))
                //登录按钮
                XCommonButton(modifier = Modifier.padding(start = 16.dp, end = 16.dp, bottom = 20.dp),
                    text = stringResource(id = R.string.common_next),
                    enabled = code.length == 4, onClick = {
                        onSaveClick()
                    })
            }
        }
    }
}



@Preview
@Composable
private fun PreviewAccountVerifyCodeContentPhone() {
    AccountVerifyCodeContent(
        title = "Verify your phone number",
        subTitle = "Enter the verification code sent to:",
        account = "+1 (555)555-1234",
        code = "1234",
        count = 0,
        isError = false,
        errorMessage = "",
        onValueChanged = {},
        onSendCodeClick = {},
        onSaveClick = {}
    )
}