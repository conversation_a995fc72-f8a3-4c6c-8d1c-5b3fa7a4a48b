package com.twl.meeboss.setting.module.account.password.activity

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.core.ui.component.InputState
import com.twl.meeboss.setting.R
import com.twl.meeboss.setting.export.SettingRouterPath
import com.twl.meeboss.setting.module.account.password.components.AccountPasswordPage
import com.twl.meeboss.setting.module.account.password.viewmodel.AccountPasswordUiIntent
import com.twl.meeboss.setting.module.account.password.viewmodel.AccountPasswordViewModel
import dagger.hilt.android.AndroidEntryPoint


@RouterPage(path = [SettingRouterPath.ACCOUNT_PASSWORD_PAGE])
@AndroidEntryPoint
class AccountPasswordActivity() : BaseMviActivity<AccountPasswordViewModel>() {

    override val viewModel: AccountPasswordViewModel by viewModels()

    override fun preInit(intent: Intent) {

    }

    override fun initData() {
        viewModel.setPasswordResult.observe(this) { success ->
            if (success) {
                finish()
            }
        }
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()

        AccountPasswordPage(
            title = stringResource(id = R.string.setting_set_your_password),
            code = uiState.password,
            inputState = uiState.inputState,
            saveButtonText = stringResource(id = R.string.common_button_save),
            showForgotPassword = false,
            errorMessage = uiState.errorMessage,
            onValueChanged = this::onValueChanged,
            onStateChanged = this::onStateChanged,
            onSaveClick = {
                viewModel.sendUiIntent(AccountPasswordUiIntent.Save)
            }
        )
    }

    private fun onValueChanged(value: String) {
        viewModel.sendUiIntent(AccountPasswordUiIntent.InputChanged(value))
    }

    private fun onStateChanged(state: InputState) {
        viewModel.sendUiIntent(AccountPasswordUiIntent.InputStateChanged(state))
    }
}