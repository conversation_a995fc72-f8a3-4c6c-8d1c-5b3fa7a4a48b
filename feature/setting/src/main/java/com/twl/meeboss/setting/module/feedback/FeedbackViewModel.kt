package com.twl.meeboss.setting.module.feedback

import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.Utils
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.media.imagepicker.util.FileUtil
import com.twl.meeboss.base.model.common.FileItem
import com.twl.meeboss.base.model.enumeration.UploadFileBizType
import com.twl.meeboss.base.usecase.ImageUploadUseCase
import com.twl.meeboss.common.ktx.toJson
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.sentry.SentryHelper
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.setting.R
import com.twl.meeboss.setting.module.feedback.FeedbackActivity.Companion.IMAGE_MAX_SIZE
import com.twl.meeboss.setting.repos.SettingRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * @author: 冯智健
 * @date: 2024年08月01日 09:58
 * @description:
 */
@HiltViewModel
class FeedbackViewModel @Inject constructor(
    private val imageUploadUseCase: ImageUploadUseCase,
    private val settingRepo: SettingRepository
): BaseMviViewModel<FeedbackUiState, FeedbackUiIntent>() {
    override fun initUiState() = FeedbackUiState()

    override fun handleIntent(intent: IUiIntent) {
        when(intent) {
            is FeedbackUiIntent.AddImage -> addImage(intent.mediaItem)
            is FeedbackUiIntent.DeleteImage -> deleteImage(intent.mediaItem)
            is FeedbackUiIntent.Submit -> submit(intent.content)
            is FeedbackUiIntent.Reset -> reset()
        }
    }

    private fun addImage(mediaItem: List<FileItem>) {
        mediaItem.forEach { e ->
            e.uri?.let {
                if (FileUtil.getFileSize(Utils.getApp(), it) > IMAGE_MAX_SIZE) {
                    T.ss(
                        R.string.common_upload_image_size_exceed_limit_tips.toResourceString("10")
                    )
                } else {
                    sendUiState {
                        copy(mediaList = mediaList.toMutableList().apply {
                            add(e)
                        })
                    }
                }
            }
        }
    }

    private fun deleteImage(mediaItem: FileItem) {
        sendUiState {
            copy(mediaList = mediaList.toMutableList().apply {
                if (mediaList.contains(mediaItem)) {
                    remove(mediaItem)
                }
            })
        }
    }

    private fun submit(content: String) {
        sendLoadUiState(LoadState.Loading)
        if (uiStateFlow.value.mediaList.isNotEmpty()) {
            imageUploadUseCase(
                viewModelScope = viewModelScope,
                bizType = UploadFileBizType.FEEDBACK,
                fileItemList = uiStateFlow.value.mediaList,
                success = {
                    val list = it.mapNotNull { fileUploadResult ->
                        fileUploadResult.originUrl
                    }
                    submitFeedback(content, list)
                },
                fail = {
                    it?.let {
                        sendLoadUiState(LoadState.Fail(content = it.message))
                        XLog.error(TAG, "uploadImageFilesUseCase fail: ${it.message}")
                    } ?: run {
                        sendLoadUiState(LoadState.Empty())
                        XLog.error(TAG, "uploadImageFilesUseCase is empty data")
                    }
                }
            )
        } else {
            submitFeedback(content, listOf())
        }
    }

    private fun submitFeedback(content: String, screenShots: List<String>) {
        requestData(
            request = {
                SentryHelper.sendUserFeedback(GsonUtils.toJson(UserFeedbackEntity(content, screenShots)))
                settingRepo.submitFeedback(content, screenShots.toJson())
            },
            success = {
                sendLoadUiState(LoadState.Success)
                sendUiState {
                    copy(submitFinish = true)
                }
            },
            fail = {
                sendLoadUiState(LoadState.Fail(it.message))
                XLog.error(TAG, "submitFeedback fail: ${it.message}")
            }
        )
    }

    private fun reset() {
        sendUiState {
            copy(submitFinish = false, mediaList = listOf())
        }
    }

    data class UserFeedbackEntity(val content: String, val urls: List<String>)
}

data class FeedbackUiState(
    val mediaList: List<FileItem> = listOf(),
    val submitFinish: Boolean = false
): IUiState

sealed class FeedbackUiIntent: IUiIntent {
    data class AddImage(val mediaItem: List<FileItem>): FeedbackUiIntent()
    data class DeleteImage(val mediaItem: FileItem): FeedbackUiIntent()
    data class Submit(val content: String): FeedbackUiIntent()
    data object Reset: FeedbackUiIntent()
}