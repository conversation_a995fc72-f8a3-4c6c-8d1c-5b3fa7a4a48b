package com.twl.meeboss.setting.module.feedback

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.components.list.MediaPickerGridList
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.base.ktx.stringResourceWithOption
import com.twl.meeboss.base.model.common.FileItem
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.component.textfield.XDescOutlineTextField
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.COLOR_4DFFFFFF
import com.twl.meeboss.core.ui.theme.COLOR_B3000000
import com.twl.meeboss.core.ui.theme.GRAY_AAAAAA
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.setting.R

/**
 * @author: 冯智健
 * @date: 2024年08月01日 10:21
 * @description:
 */
@Preview
@Composable
fun FeedbackPage(
    loadState: LoadState = LoadState.Loading,
    textFieldContent: MutableState<String> = rememberSaveable { mutableStateOf("") },
    mediaList: List<FileItem> = listOf(),
    onChooseMediaClick: (count: Int) -> Unit = {},
    onMediaDeleteClick: (mediaItem: FileItem) -> Unit = {},
    onMediaPreviewClick: (mediaItem: FileItem) -> Unit = {},
    onSubmitClick: (content: String) -> Unit = {}
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    val context = LocalContext.current
    val textFieldOverFlow = rememberSaveable { mutableStateOf(false) }
    Column(
        modifier = Modifier
            .background(Color.White)
            .fillMaxSize()
    ) {
        XTitleBar(
            title = stringResource(id = R.string.setting_support_center),
            showDivider = true
        )
        Box(
            modifier = Modifier.fillMaxSize().weight(1F)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp).noRippleClickable {
                        keyboardController?.hide()
                    }
            ) {
                Text(
                    text = stringResource(id = R.string.setting_feedback_content),
                    modifier = Modifier.padding(top = 24.dp),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Black222222
                )
                XDescOutlineTextField(
                    value = textFieldContent.value,
                    modifier = Modifier.padding(top = 12.dp),
                    maxLines = 6,
                    placeHolderText = stringResource(id = R.string.setting_feedback_content_tips)
                ) { content, overflow ->
                    textFieldContent.value = content
                    textFieldOverFlow.value = overflow
                }
                Text(
                    text = stringResourceWithOption(id = R.string.common_images,true),
                    modifier = Modifier.padding(top = 24.dp),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Black222222
                )
                MediaPickerGridList(
                    modifier = Modifier.padding(top = 12.dp),
                    mediaList = mediaList,
                    onMediaDeleteClick = onMediaDeleteClick,
                    onMediaPreviewClick = onMediaPreviewClick,
                    onChooseMediaClick = onChooseMediaClick
                )
                Text(
                    text = stringResource(id = R.string.setting_feedback_guide),
                    modifier = Modifier.padding(top = 20.dp),
                    fontSize = 14.sp,
                    color = GRAY_AAAAAA,
                    lineHeight = 20.sp
                )
            }
            when(loadState) {
                is LoadState.Loading -> {
                    UploadingContent()
                }
                is LoadState.Empty,
                is LoadState.Fail -> {
                    T.ss(context.getString(R.string.common_upload_image_failed_tips))
                }
                else -> {

                }
            }
        }
        XCommonButton(
            modifier = Modifier.padding(16.dp),
            text = stringResource(id = R.string.common_submit),
            onClick = {
                 if (textFieldContent.value.isBlank()) {
                    T.ss(context.getString(R.string.setting_feedback_content_empty_tips))
                } else {
                    onSubmitClick.invoke(textFieldContent.value)
                }
            },
            enabled = !textFieldOverFlow.value
        )
    }
}

@Composable
private fun UploadingContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Transparent),
        contentAlignment = Alignment.Center
    ) {
        Card(
            colors = CardDefaults.cardColors(
                containerColor = COLOR_B3000000,
                contentColor = Color.White
            ),
            shape = RoundedCornerShape(16.dp),
        ) {
            Column(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CircularProgressIndicator(
                    modifier = Modifier
                        .padding(top = 20.dp)
                        .size(24.dp)
                        .align(Alignment.CenterHorizontally),
                    strokeWidth = 2.dp,
                    color = Color.White,
                    trackColor = COLOR_4DFFFFFF,
                )
                Text(
                    text = stringResource(id = R.string.common_uploading),
                    fontSize = 14.sp,
                    modifier = Modifier
                        .padding(top = 8.dp, start = 24.dp, end = 24.dp, bottom = 16.dp)
                        .align(Alignment.CenterHorizontally),
                    textAlign = TextAlign.Center,
                )
            }
        }
    }
}