package com.twl.meeboss.setting.module.account.verify.viewmodel

import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.repo.toResult
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.setting.R
import com.twl.meeboss.setting.api.AccountSettingApi
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class AccountVerifyEmailViewModel @Inject constructor() :
    BaseMviViewModel<AccountVerifyEmailUiState, AccountVerifyEmailUiIntent>() {

    var email: String = ""

    override fun initUiState(): AccountVerifyEmailUiState = AccountVerifyEmailUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is AccountVerifyEmailUiIntent.Save -> {
                resendVerifyEmail()
            }

            else -> {

            }

        }
    }


    private fun resendVerifyEmail() {
        launcherOnIO {
            getService(AccountSettingApi::class.java).sendEmailAuth().toResult().onSuccess {
                T.ss(R.string.setting_sent_successfully)

            }.onFailure {
                T.ss(it.message)
            }
        }
    }
}

data class AccountVerifyEmailUiState(
    val canSave: Boolean = false,
) : IUiState

sealed class AccountVerifyEmailUiIntent : IUiIntent {
    data object Save : AccountVerifyEmailUiIntent()
}