package com.twl.meeboss.setting.module.account.password.viewmodel

import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.repo.toResult
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.manager.PasswordValidator
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.core.ui.component.InputState
import com.twl.meeboss.setting.R
import com.twl.meeboss.setting.api.AccountSettingApi
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import com.twl.meeboss.base.ktx.checkPasswordSecVersion
import com.twl.meeboss.base.ktx.toEncryptPassword
import com.twl.meeboss.core.ui.utils.toResourceString

@HiltViewModel
class AccountPasswordViewModel @Inject constructor() : BaseMviViewModel<AccountPasswordUiState, AccountPasswordUiIntent>() {

    val setPasswordResult: MutableLiveData<Boolean> = MutableLiveData()

    override fun initUiState(): AccountPasswordUiState = AccountPasswordUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is AccountPasswordUiIntent.Save -> {
                setPassword()
            }
            is AccountPasswordUiIntent.InputChanged -> {
                onValueChanged(intent.password)
            }

            is AccountPasswordUiIntent.InputStateChanged -> {
                launcherOnIO {
                    val errorMessage = if (intent.state != InputState.Focus) {
                        val code = uiStateFlow.value.password
                        if (code.isEmpty()) {
                            ""
                        } else {
                            PasswordValidator.checkPasswordStrength(code).toast ?: ""
                        }
                    } else {
                        ""
                    }
                    sendUiState { copy(inputState = intent.state, errorMessage = errorMessage) }
                }
            }

            else -> {

            }

        }
    }

    private fun onValueChanged(value: String) {
        //去除前后空格
        val input = value.trim()
        if (input.isNotEmpty() && input.length !in 8..32) {
            sendUiState {
                copy(
                    password = input,
                    inputState = InputState.Error,
                    errorMessage = R.string.common_password_range.toResourceString()
                )
            }
        } else {
            sendUiState {
                copy(
                    password = input,
                    inputState = InputState.Focus,
                    errorMessage = ""
                )
            }
        }
    }

    private fun setPassword() {
        val psw = uiStateFlow.value.password
        //如果包含空格，显示toast
        if (psw.contains(" ")) {
            T.ss(R.string.common_cant_contain_space)
            return
        }
        showLoadingDialog()
        launcherOnIO {
            val result = PasswordValidator.checkPasswordStrength(psw)
            if (!result.checkResult) {
                dismissLoadingDialog()
                val error = result.toast
                if (!error.isNullOrBlank()) {
                    sendUiState { copy(inputState = InputState.Error, errorMessage = error) }
                }
                return@launcherOnIO
            }

            checkPasswordSecVersion { isSecVersion2, version ->
                val api = getService(AccountSettingApi::class.java)
                api.phoneSetPassword(
                    password = if (isSecVersion2) psw.toEncryptPassword() else psw,
                    secVersion = version
                ).toResult().onSuccess {
                    dismissLoadingDialog()
                    setPasswordResult.postValue(true)
                }.onFailure {
                    dismissLoadingDialog()
                    T.ss(it.message)
                }
            }
        }
    }
}

data class AccountPasswordUiState(
    val password: String = "",
    val inputState: InputState = InputState.None,
    val errorMessage: String = ""
) : IUiState

sealed class AccountPasswordUiIntent : IUiIntent {
    data object Save : AccountPasswordUiIntent()
    data class InputChanged(val password: String) : AccountPasswordUiIntent()
    data class InputStateChanged(val state: InputState) : AccountPasswordUiIntent()
}