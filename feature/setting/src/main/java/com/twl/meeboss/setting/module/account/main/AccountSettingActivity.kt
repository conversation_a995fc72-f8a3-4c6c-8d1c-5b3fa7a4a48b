package com.twl.meeboss.setting.module.account.main

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.protocol.ProtocolHelper
import com.twl.meeboss.setting.export.SettingPageRouter
import com.twl.meeboss.setting.export.SettingRouterPath
import com.twl.meeboss.webview.export.WebUrls
import dagger.hilt.android.AndroidEntryPoint

/**
 * 账号设置页面
 */
@AndroidEntryPoint
@RouterPage(path = [SettingRouterPath.ACCOUNT_SETTING_PAGE])
class AccountSettingActivity : BaseMviActivity<AccountSettingViewModel>() {
    override val viewModel: AccountSettingViewModel by viewModels()
    override fun preInit(intent: Intent) {
    }

    override fun initData() {
        viewModel.sendUiIntent(AccountSettingUiIntent.GetData(true))
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        val loadState by viewModel.loadUiStateFlow.collectAsStateWithLifecycle()
        val settingDetail = uiState.accountDetail
        AccountSettingPage(
            settingDetail = settingDetail,
            loadingState = loadState.loadState,
            onEmailClick = {
                settingDetail?.let {
                    when (it.emailStatus) {
                        0 -> SettingPageRouter.jumpToAccountEmailActivity(this, true)
                        1 -> SettingPageRouter.jumpToAccountVerifyEmailActivity(this, it.email ?: "")
                        else -> SettingPageRouter.jumpToAccountEmailActivity(this, false)
                    }
                }
            },
            onPhoneClick = {
                settingDetail?.run {
                    SettingPageRouter.jumpToAccountPhoneActivity(this@AccountSettingActivity, phoneNumber.isNullOrBlank())
                }
            },
            onPasswordClick = {
                settingDetail?.run {
                    if (password.isNullOrBlank()) {
                        SettingPageRouter.jumpToAccountPasswordActivity(this@AccountSettingActivity)
                    } else {
                        if (phoneNumber.isNullOrBlank()) {
                            SettingPageRouter.jumpToAccountOldPasswordActivity(
                                this@AccountSettingActivity,
                                email ?: "",
                                false
                            )
                        } else {
                            SettingPageRouter.jumpToAccountOldPasswordActivity(
                                this@AccountSettingActivity,
                                phoneNumber!!,
                                true
                            )
                        }
                    }
                }
            },
            onClickDeleteAccount = {
                ProtocolHelper.parseProtocol(WebUrls.getDeleteAccountUrl())
            },
            onRetryClick = {
                viewModel.sendUiIntent(AccountSettingUiIntent.GetData(true))
            }
        )
    }

    override fun onStart() {
        super.onStart()
        //每次回来都刷新页面
        viewModel.sendUiIntent(AccountSettingUiIntent.GetData(false))
    }

}