package com.twl.meeboss.setting.api

import com.twl.meeboss.base.api.resp.CommonRegionConfigResult
import com.twl.meeboss.base.model.CommonListResult
import com.twl.meeboss.base.model.enumeration.ShowStatus
import com.twl.meeboss.base.model.setting.SettingCheckItemBean
import com.twl.meeboss.core.network.HttpResult
import com.twl.meeboss.setting.export.model.EmployersVisibilityResult
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * @author: 冯智健
 * @date: 2024年08月01日 10:06
 * @description:
 */
interface SettingApi {
    /**
     * 提交反馈
     */
    @FormUrlEncoded
    @POST("api/feedback/submit")
    suspend fun submitFeedback(
        @Field("content") content: String,
        @Field("screenShots") screenShots: String
    ): HttpResult<Any>

    /**
     * 【0.13】推送设置
     * @param group
     * 1 C推送
     * 2 C邮件
     * 3 B推送
     * 4 B邮件
     * status 0关闭 1打开
     */
    @GET("api/settings/switches")
    suspend fun getNotificationConfig(
        @Query("group") group: Int?,
    ): HttpResult<CommonListResult<SettingCheckItemBean>>


    /**
     * 【0.13】设置-修改通知设置
     */
    @FormUrlEncoded
    @POST("api/settings/updateSwitch")
    suspend fun notificationUpdateSwitch(
        @Field("type") type: Long?,
        @Field("status") status: Int?,
    ): HttpResult<Any>

    /**
     * 获取当前公司招聘者可见状态
     */
    @GET("api/jobseeker/settings/employersVisibility")
    suspend fun getEmployersVisibility(): HttpResult<EmployersVisibilityResult>
    /**
     * 更新当前公司招聘者可见状态
     */
    @FormUrlEncoded
    @POST("api/jobseeker/settings/employersVisibility/update")
    suspend fun updateEmployersVisibility(@Field("status") status: @ShowStatus Int): HttpResult<Any>

    @GET("api/common/getRegionLanguageList")
    suspend fun getRegionLanguageList(): HttpResult<CommonRegionConfigResult>

    @FormUrlEncoded
    @POST("api/settings/updateLanguage")
    suspend fun updateLanguage(@Field("lang") lang: String
    ): HttpResult<Any>
}