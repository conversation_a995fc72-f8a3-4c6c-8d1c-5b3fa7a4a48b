package com.twl.meeboss.setting.module.account.verify.activity

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.setting.export.SettingPageRouter
import com.twl.meeboss.setting.export.SettingRouterPath
import com.twl.meeboss.setting.export.model.PasswordResetType
import com.twl.meeboss.setting.module.account.verify.components.AccountVerifyCodePage
import com.twl.meeboss.setting.module.account.verify.viewmodel.AccountVerifyCodeUiIntent
import com.twl.meeboss.setting.module.account.verify.viewmodel.AccountVerifyCodeViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 用于修改密码前，发送和校验验证码，包括邮箱和手机号的验证码
 */
@RouterPage(path = [SettingRouterPath.ACCOUNT_VERIFY_CODE_PAGE])
@AndroidEntryPoint
class AccountVerifyCodeActivity() : BaseMviActivity<AccountVerifyCodeViewModel>() {

    override val viewModel: AccountVerifyCodeViewModel by viewModels()

    override fun preInit(intent: Intent) {
        val account = intent.getStringExtra(SettingPageRouter.BUNDLE_ACCOUNT) ?: ""
        val machineCheckToken = intent.getStringExtra(SettingPageRouter.BUNDLE_TOKEN) ?: ""
        val isPhone = intent.getBooleanExtra(SettingPageRouter.BUNDLE_IS_PHONE, false)
        viewModel.sendUiIntent(AccountVerifyCodeUiIntent.Init(account, isPhone, machineCheckToken))
    }

    override fun initData() {
        viewModel.sendUiIntent(AccountVerifyCodeUiIntent.GetCode)
        viewModel.checkResult.observe(this) { uniqStr ->
            // 验证码校验成功，跳转到重置密码页面
            if (uniqStr.isNotBlank()) {
                if (viewModel.isPhone()) {
                    SettingPageRouter.jumpToAccountResetPasswordActivity(
                        this,
                        PasswordResetType.FROM_PHONE,
                        uniqStr = uniqStr,
                    )
                } else {
                    SettingPageRouter.jumpToAccountResetPasswordActivity(
                        this,
                        PasswordResetType.FROM_EMAIL,
                        uniqStr = uniqStr,
                    )
                }
                finish()
            }
        }
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        AccountVerifyCodePage(uiState, onClickSendAgain = {
            viewModel.sendUiIntent(AccountVerifyCodeUiIntent.GetCode)
        },
            onClickButton = {
                viewModel.sendUiIntent(AccountVerifyCodeUiIntent.CheckCode)
            }) {
            viewModel.sendUiIntent(AccountVerifyCodeUiIntent.CodeChange(it))
        }
    }


}

