package com.twl.meeboss.setting.module.account.verify.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.ktx.toFormatPhoneNumber
import com.twl.meeboss.common.utils.Countdown
import com.twl.meeboss.setting.module.account.verify.strategy.EmailVerifyCodeStrategy
import com.twl.meeboss.setting.module.account.verify.strategy.PhoneVerifyCodeStrategy
import com.twl.meeboss.setting.module.account.verify.strategy.VerifyCodeStrategy
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class AccountVerifyCodeViewModel @Inject constructor(
    //private val repos: BossJobRepository
) : BaseMviViewModel<AccountVerifyCodeUiState, AccountVerifyCodeUiIntent>() {

    //人机校验token
    var machineCheckToken: String = ""

    var originAccount:String = ""

    val checkResult = MutableLiveData<String>()

    private val countDown: Countdown = Countdown {
        viewModelScope.launch(Dispatchers.Main) {
            sendUiState { copy(countDownValue = it) }
        }
    }

    private val strategy: VerifyCodeStrategy by lazy {
        if (isPhone()) {
            PhoneVerifyCodeStrategy(account(), viewModelScope)
        } else {
            EmailVerifyCodeStrategy(account(), viewModelScope)
        }
    }

    fun isPhone():Boolean = uiStateFlow.value.isPhone

    private fun account():String = originAccount

    override fun initUiState(): AccountVerifyCodeUiState = AccountVerifyCodeUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is AccountVerifyCodeUiIntent.Init -> {
                machineCheckToken = intent.machineCheckToken
                originAccount = intent.account
                sendUiState {
                    copy(
                        account = if(intent.isPhone) intent.account.toFormatPhoneNumber() else intent.account,
                        isPhone = intent.isPhone
                    )
                }
            }
            is AccountVerifyCodeUiIntent.CodeChange -> {
                sendUiState { copy(code = intent.code, isError = false) }
                if (intent.code.length == 4) {
                    checkVerificationCode()
                }
            }
            is AccountVerifyCodeUiIntent.CheckCode -> {
                checkVerificationCode()
            }
            is AccountVerifyCodeUiIntent.GetCode -> {
                getVerificationCode()
            }

            else -> {

            }

        }
    }


    private fun getVerificationCode() {
        showLoadingDialog()
        strategy.getVerificationCode(machineCheckToken) {
            dismissLoadingDialog()
            if (it) {
                countDown.start()
            }
        }
    }

    private fun checkVerificationCode() {
        showLoadingDialog()
        strategy.checkVerificationCode(uiStateFlow.value.code) {
            dismissLoadingDialog()
            checkResult.postValue(it)
        }
    }
}

data class AccountVerifyCodeUiState(
    val account:String = "",
    val isPhone: Boolean = true,
    val canSave: Boolean = false,
    val code: String = "",
    val isError: Boolean = false,
    val errorMessage: String = "",
    val countDownValue: Int = 0
) : IUiState

sealed class AccountVerifyCodeUiIntent : IUiIntent {
    data class Init(val account:String,val isPhone:Boolean,val machineCheckToken:String):AccountVerifyCodeUiIntent()
    data class CodeChange(val code: String) : AccountVerifyCodeUiIntent()
    data object CheckCode : AccountVerifyCodeUiIntent()
    data object GetCode : AccountVerifyCodeUiIntent()
}


