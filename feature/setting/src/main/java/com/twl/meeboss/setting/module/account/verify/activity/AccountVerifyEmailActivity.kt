package com.twl.meeboss.setting.module.account.verify.activity

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.setting.export.SettingPageRouter
import com.twl.meeboss.setting.export.SettingRouterPath
import com.twl.meeboss.setting.module.account.verify.components.AccountVerifyEmailPage
import com.twl.meeboss.setting.module.account.verify.viewmodel.AccountVerifyEmailViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 验证邮箱页面
 */

@RouterPage(path = [SettingRouterPath.ACCOUNT_VERIFY_EMAIL_PAGE])
@AndroidEntryPoint
class  AccountVerifyEmailActivity() : BaseMviActivity<AccountVerifyEmailViewModel>() {

    override val viewModel:  AccountVerifyEmailViewModel by viewModels()

    override fun preInit(intent: Intent) {
        viewModel.email = intent.getStringExtra(SettingPageRouter.BUNDLE_ACCOUNT) ?: ""
    }

    override fun initData() {
    }

    @Composable
    override fun ComposeContent() {
        AccountVerifyEmailPage()
    }

}
