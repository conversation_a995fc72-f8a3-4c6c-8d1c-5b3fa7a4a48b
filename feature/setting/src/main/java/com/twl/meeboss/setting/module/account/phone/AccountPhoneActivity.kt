package com.twl.meeboss.setting.module.account.phone

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.buildAreaCodeParam
import com.twl.meeboss.base.model.common.CommonAreaCodeBean
import com.twl.meeboss.base.ktx.showRobotCheckDialog
import com.twl.meeboss.components.bottomsheet.areacode.SelectAreaCodeBottomSheet
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.setting.export.SettingPageRouter
import com.twl.meeboss.setting.export.SettingRouterPath
import dagger.hilt.android.AndroidEntryPoint

/**
 * 设置手机号页面（设置或者修改）
 */
@RouterPage(path = [SettingRouterPath.ACCOUNT_PHONE_PAGE])
@AndroidEntryPoint
class AccountPhoneActivity() : BaseMviActivity<AccountPhoneViewModel>() {

    override val viewModel: AccountPhoneViewModel by viewModels()

    override fun preInit(intent: Intent) {
        viewModel.sendUiIntent(
            AccountPhoneUiIntent.Init(
                intent.getBooleanExtra(
                    SettingPageRouter.BUNDLE_IS_ADD_PHONE,
                    false
                )
            )
        )

    }

    override fun initData() {
        viewModel.checkAccountResult.observe(this) { success ->
            if (success) {
                viewModel.uiStateFlow.value.run {
                    SettingPageRouter.jumpToAccountVerifyPhoneCodeActivity(
                        this@AccountPhoneActivity,
                        phoneNumber.text,
                        viewModel.machineCheckToken, isAddPhone, areaCode = CommonAreaCodeBean(countryCode = areaCountry, smsPrefix = areaCode)
                    )
                }

            }
        }
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()

        AccountPhonePage(
            uiState, onClickAreaCode = {
                SelectAreaCodeBottomSheet.newInstance(){
                    viewModel.sendUiIntent(AccountPhoneUiIntent.AreaCodeChanged(it))
                }.showSafely(this)
            },
            onValueChanged = {
                viewModel.sendUiIntent(AccountPhoneUiIntent.InputChanged(it))
            },
            onInputStateChanged = {
                viewModel.sendUiIntent(AccountPhoneUiIntent.InputStateChanged(it))
            },
        ) {
            showRobotCheckDialog {
                viewModel.machineCheckToken = it
                viewModel.sendUiIntent(AccountPhoneUiIntent.Save)
            }
        }
    }

}
