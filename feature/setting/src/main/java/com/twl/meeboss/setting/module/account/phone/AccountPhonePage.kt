package com.twl.meeboss.setting.module.account.phone

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.text.isDigitsOnly
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.core.ui.component.InputState
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.component.textfield.XPhoneTextField
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.setting.R

/**
 * @author: 冯智健
 * @date: 2024年08月06日 21:20
 * @description:
 */
@Composable
fun AccountPhonePage(uiState: AccountPhoneUiState,
                     onClickAreaCode:()->Unit = {},
                     onValueChanged:(TextFieldValue)->Unit = {},
                     onInputStateChanged:(InputState)->Unit = {}, onNextClick: () -> Unit = {}) {

    XTheme {
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
        ) {
            Column {
                XTitleBar()
                Spacer(modifier = Modifier.height(12.dp))
                Text(modifier = Modifier.padding(16.dp, 0.dp, 0.dp, 0.dp),
                    text = stringResource(id = if(uiState.isAddPhone) R.string.common_phone_number else R.string.common_new_phone_number),
                    fontSize = 28.sp, fontWeight = FontWeight(590)
                )
                Spacer(modifier = Modifier.height(6.dp))
                Text(modifier = Modifier.padding(16.dp, 0.dp),
                    text = stringResource(id = R.string.setting_phone_number_subtitle),
                    fontSize = 14.sp, fontWeight = FontWeight.W400)
                Spacer(modifier = Modifier.height(10.dp))
                XPhoneTextField(
                    modifier = Modifier.padding(16.dp, 0.dp),
                    value = uiState.phoneNumber,
                    placeHolder = R.string.common_phone_number,
                    innerTitle = stringResource(id = R.string.common_phone_number),
                    state = uiState.inputState,
                    areaCode = "${uiState.areaCountry} ${uiState.areaCode}",
                    errorMessage = uiState.errorMessage,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                    onClickAreaCode = onClickAreaCode,
                    onValueChange = {
                        if (it.text.isBlank() || (it.text.isDigitsOnly())) {
                           onValueChanged(it)
                        }
                    },
                    onStateChanged = {
                        onInputStateChanged(it)
                    }
                )
                Spacer(modifier = Modifier.height(28.dp))
                XCommonButton(modifier = Modifier.padding(16.dp, 0.dp),
                    text = stringResource(id = R.string.common_next),
                    enabled = uiState.canSave,
                    onClick = onNextClick
                )
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    AccountPhonePage(uiState = AccountPhoneUiState(areaCode = "+1", areaCountry = "US"))
}