package com.twl.meeboss.setting.module.greeting.home

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.google.accompanist.pager.PagerState
import com.google.accompanist.pager.rememberPagerState
import com.twl.meeboss.base.components.pagestate.XStatePage
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.base.model.enumeration.GreetingSettingType
import com.twl.meeboss.chat.export.model.ChatTemplateBean
import com.twl.meeboss.chat.export.model.ChatTemplateResult
import com.twl.meeboss.core.ui.component.tabview.XTabView
import com.twl.meeboss.setting.R
import com.twl.meeboss.setting.module.greeting.home.components.GreetingSettingContent
import com.twl.meeboss.setting.module.greeting.home.model.GreetingSettingContentBean
import com.twl.meeboss.setting.module.greeting.home.preview.GreetingSettingParameterProvider

/**
 * @author: 冯智健
 * @date: 2024年08月15日 18:04
 * @description:
 */
@Composable
fun GreetingSettingPage(
    isGeek: Boolean,
    loadState: LoadState = LoadState.Success,
    pagerState: PagerState = rememberPagerState(),
    startChatGreetingTemplate: ChatTemplateResult? = null,
    interestInGreetingTemplate: ChatTemplateResult? = null,
    onGreetingStatusChange: ((Boolean, Long) -> Unit) = { _,_ ->},
    onGreetingTemplateChange: ((ChatTemplateBean) -> Unit) = {},
    retryOnClick: () -> Unit = {},
) {
    Column(modifier = Modifier.background(Color.White).fillMaxSize()) {
        XTitleBar(title = stringResource(id = R.string.setting_greeting_settings))
        if (isGeek) {
            XTabView(
                modifier = Modifier.fillMaxSize(),
                pagerState = pagerState,
                tabIndicatorPercent = 0.5F,
                options = listOf(
                    stringResource(id = R.string.setting_start_chat_greeting),
                    stringResource(id = R.string.setting_interest_in_greeting),
                )
            ) { page ->
                XStatePage(loadState = loadState, retryOnClick = retryOnClick) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(top = 20.dp, start = 16.dp, end = 16.dp)
                            .verticalScroll(rememberScrollState())
                    ) {
                        val contentBean = when(page) {
                            GreetingSettingType.START_CHAT ->
                                GreetingSettingContentBean(
                                    GreetingSettingType.START_CHAT,
                                    startChatGreetingTemplate,
                                    stringResource(id = R.string.setting_geek_greeting_status_tips)
                                )
                            GreetingSettingType.INTEREST_IN ->
                                GreetingSettingContentBean(
                                    GreetingSettingType.INTEREST_IN,
                                    interestInGreetingTemplate,
                                    stringResource(id = R.string.setting_auto_reply_tips)
                                )
                            else -> null
                        }
                        GreetingSettingContent(
                            contentBean = contentBean,
                            onGreetingStatusChange = {
                                onGreetingStatusChange(it, contentBean?.chatTemplate?.selectedTemplateId ?: -1)
                            },
                            onGreetingTemplateChange = onGreetingTemplateChange
                        )
                    }
                }
            }
        } else {
            XStatePage(loadState = loadState, retryOnClick = retryOnClick) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 20.dp, start = 16.dp, end = 16.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    val contentBean = GreetingSettingContentBean(
                        GreetingSettingType.START_CHAT,
                        startChatGreetingTemplate,
                        stringResource(id = R.string.setting_boss_greeting_status_tips)
                    )
                    GreetingSettingContent(
                        contentBean = contentBean,
                        onGreetingStatusChange = {
                            onGreetingStatusChange(it, contentBean.chatTemplate?.selectedTemplateId ?: -1)
                        },
                        onGreetingTemplateChange = onGreetingTemplateChange
                    )
                }
            }
        }
    }
}


@Preview(showBackground = true)
@Composable
private fun PreviewGreetingSettingPageGeek(
    @PreviewParameter(GreetingSettingParameterProvider::class)
    chatTemplate: ChatTemplateResult?,
) {
    GreetingSettingPage(true, startChatGreetingTemplate = chatTemplate)
}

@Preview(showBackground = true)
@Composable
private fun PreviewGreetingSettingPageBoss(
    @PreviewParameter(GreetingSettingParameterProvider::class)
    chatTemplate: ChatTemplateResult?,
) {
    GreetingSettingPage(false, startChatGreetingTemplate = chatTemplate)
}
