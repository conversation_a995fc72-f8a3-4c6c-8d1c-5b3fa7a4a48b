package com.twl.meeboss.setting.module.account.email

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.showRobotCheckDialog
import com.twl.meeboss.setting.export.SettingPageRouter
import com.twl.meeboss.setting.export.SettingRouterPath
import dagger.hilt.android.AndroidEntryPoint


@RouterPage(path = [SettingRouterPath.ACCOUNT_EMAIL_PAGE])
@AndroidEntryPoint
class AccountEmailActivity() : BaseMviActivity<AccountEmailViewModel>() {

    override val viewModel: AccountEmailViewModel by viewModels()

    override fun preInit(intent: Intent) {
        viewModel.isAddEmail = intent.getBooleanExtra(SettingPageRouter.BUNDLE_IS_ADD_EMAIL, true)

    }

    @Composable
    override fun ComposeContent() {
        AccountEmailPage {
            showRobotCheckDialog {
                viewModel.machineCheckToken = it
                viewModel.sendUiIntent(AccountEmailUiIntent.Save)
            }
        }
    }


    override fun initData() {
        viewModel.checkAccountResult.observe(this) { success ->
            if (success) {
                SettingPageRouter.jumpToAccountVerifyEmailCodeActivity(
                    this,
                    viewModel.uiStateFlow.value.email.text,
                    viewModel.machineCheckToken, viewModel.isAddEmail
                )
            }
        }
    }

}