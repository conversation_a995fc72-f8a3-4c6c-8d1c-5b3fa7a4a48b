<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>

        <activity
            android:name=".module.main.SettingsMainActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.account.main.AccountSettingActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.account.email.AccountEmailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.account.verify.activity.AccountVerifyEmailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.account.verify.activity.AccountVerifyEmailCodeActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".module.account.phone.AccountPhoneActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.account.verify.activity.AccountVerifyPhoneCodeActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".module.account.password.activity.AccountPasswordActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.account.password.activity.AccountOldPasswordActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.account.password.activity.AccountResetPasswordActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.account.verify.activity.AccountVerifyCodeActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.account.delete.AccountDeleteActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.feedback.FeedbackActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.greeting.home.GreetingSettingActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.greeting.custom.GreetingSettingCustomActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize"/>

        <activity
            android:name=".module.block.BlocklistActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.notification.activity.NotificationSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.notification.activity.AppPushNotificationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.notification.activity.EmailNotificationActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".module.visibility.VisibilitySettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.notification.activity.MainNotificationGuideActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            />
        <activity
            android:name=".module.language.LanguageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".module.legalprivacy.SettingLegalAndPrivacyActivity"
            android:screenOrientation="portrait" />

    </application>
</manifest>