package com.twl.meeboss.chat.export.constant

import androidx.annotation.IntDef

@Target(AnnotationTarget.TYPE, AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.SOURCE)
@IntDef(flag = true, value = [
    FriendStatus.NONE,
    FriendStatus.NOT_REPLY,
    FriendStatus.NEW_GREETING,
    FriendStatus.IN_COMMUNICATION
])
annotation class FriendStatus {
    companion object {
        const val NONE = 0
        const val NOT_REPLY = 1
        const val NEW_GREETING = 2
        const val IN_COMMUNICATION = 3

    }
}

fun Int.isFriendStatusCanExchange(): <PERSON><PERSON><PERSON> {
    return this and FriendStatus.IN_COMMUNICATION == FriendStatus.IN_COMMUNICATION
}

/**
 * 二进制中，第三位为1 ，则表示已经发送过交换请求
 * 4：100
 */
fun Int.isFriendStatusHasSendExchange(): <PERSON><PERSON><PERSON> {
    return this and 4 == 4
}