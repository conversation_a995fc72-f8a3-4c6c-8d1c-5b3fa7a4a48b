package com.twl.meeboss.chat.export.api

import com.twl.meeboss.base.foundation.IRepository
import com.twl.meeboss.base.model.PageList
import com.twl.meeboss.base.model.ResumeUrlBean
import com.twl.meeboss.chat.export.constant.BlackListStatus
import com.twl.meeboss.chat.export.model.ChatCheckEmailResult
import com.twl.meeboss.chat.export.model.ChatCheckPhoneResult
import com.twl.meeboss.chat.export.model.ChatTemplateResult
import com.twl.meeboss.chat.export.model.ContactFromNet
import com.twl.meeboss.chat.export.model.ConversationListResult
import com.twl.meeboss.chat.export.model.InteractInterestResult
import com.twl.meeboss.chat.export.model.ValidateBeforeChatResult

/**
 * @author: 冯智健
 * @date: 2024年08月15日 21:37
 * @description:
 */
interface IChatGeekRepository: IRepository {
    /**
     * 开聊前校验
     */
    suspend fun validateBeforeChat(securityId: String): Result<ValidateBeforeChatResult>
    /**
     * 添加好友
     */
    suspend fun addFriend(securityId: String, addFriendSource: Int = 0): Result<ContactFromNet>
    /**
     * 获取会话列表
     */
    suspend fun getConversations(lastVersion:String): Result<ConversationListResult>
    /**
     * 删除好友会话
     */
    suspend fun deleteConversation(friendId: String, friendIdentity: Int): Result<Any>
    /**
     * 获取问候语模版
     */
    suspend fun getGreetingTemplates(
        friendId: String? = null, 
        jobId: String? = null, 
        showSample: Boolean = false
    ): Result<ChatTemplateResult>
    /**
     * 发送问候语
     */
    suspend fun sendGreetingText(templateId: Long, text: String, friendId: String, jobId: String): Result<Any>
    /**
     * 保存问候语
     */
    suspend fun saveGreetingText(templateId: Long, text: String?, saveAsDefault: Boolean): Result<Any>
    /**
     * 获取感兴趣回复语模版
     */
    suspend fun getInterestTemplates(
        friendId: String? = null, 
        jobId: String? = null, 
        showSample: Boolean = false
    ): Result<ChatTemplateResult>
    /**
     * 保存感兴趣回复语
     */
    suspend fun saveInterestText(templateId: Long, text: String?, useAsDefault: Boolean): Result<Any>

    /**
     * 发送感兴趣语
     */
    suspend fun sendInterestText(templateId: Long, text: String, friendId: String, jobId: String): Result<Any>
    /**
     * 获取邮箱
     */
    suspend fun checkEmail(): Result<ChatCheckEmailResult>
    /**
     * 获取手机号
     */
    suspend fun checkPhone(): Result<ChatCheckPhoneResult>
    /**
     * 请求交换电话
     */
    suspend fun requestExchangePhone(securityId: String): Result<Any>
    /**
     * 请求交换邮箱
     */
    suspend fun requestExchangeEmail(securityId: String): Result<Any>
    /**
     * 交换电话
     */
    suspend fun exchangePhone(exchangedId: String, securityId:String, status:Int): Result<Any>
    /**
     * 交换邮箱
     */
    suspend fun exchangeEmail(exchangedId: String, securityId:String, status:Int): Result<Any>
    /**
     * 更新黑名单状态
     */
    suspend fun updateBlackListStatus(friendId: String, deleted: @BlackListStatus Int): Result<Any>
    /**
     * 获取黑名单列表
     */
    suspend fun getBlackList(lastId: String?, pageSize: Int): Result<PageList<ContactFromNet>>

    /**
     * 请求发送简历
     */
    suspend fun  geekRequestSendResume(resumeId: String,securityId: String):Result<Any>

    /**
     * 处理简历请求
     */
    suspend fun geekSendResume(resumeId: String, securityId: String, exchangeId: String,status: Int): Result<Any>

    /**
     * 获取简历预览地址
     */
    suspend fun getExchangeResumeUrl(exchangeId: String):Result<ResumeUrlBean>

    //感兴趣
    suspend fun geekInteractInterest(mid: Long): Result<InteractInterestResult>

    /**
     * @param scene
     * 11 请求邮箱
     * 12 请求电话
     * 13 请求简历
     * 14 请求发送简历
     */
    suspend fun validateBeforeExchange(securityId: String,scene:Int): Result<Any>
}