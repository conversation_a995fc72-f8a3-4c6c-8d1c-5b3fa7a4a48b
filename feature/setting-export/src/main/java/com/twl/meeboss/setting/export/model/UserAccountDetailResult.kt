package com.twl.meeboss.setting.export.model

import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.base.model.common.CommonAreaCodeBean

class UserAccountDetailResult(
    val email: String? = "",
    val canChangeEmail:Boolean = false,//是否可以换绑邮箱:true 可以换绑  false不可以
    val emailStatus: Int = 0, //0无邮箱、1有邮箱未认证、2有邮箱已认证
    val password: String? = "",
    val phoneNumber: String? = "",
    val countryCodeVO:CommonAreaCodeBean? = null,
    val appleEmail: String? = "", //苹果绑定的邮箱
    val googleEmail: String? = "" //谷歌绑定的邮箱
) : BaseEntity
