package com.twl.meeboss.setting.export.model

import androidx.annotation.IntDef

/**
 * @author: 冯智健
 * @date: 2024年08月06日 21:42
 * @description:
 */
@Target(AnnotationTarget.TYPE, AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.SOURCE)
@IntDef(flag = true, value = [
    PasswordResetType.FROM_OLD_PASSWORD,
    PasswordResetType.FROM_PHONE,
    PasswordResetType.FROM_EMAIL
])
annotation class PasswordResetType {
    companion object {
        //通过输入旧密码后重置
        const val FROM_OLD_PASSWORD = 0
        //通过验证手机号验证码后重置
        const val FROM_PHONE = 1
        //通过验证邮箱验证码后重置
        const val FROM_EMAIL = 2
    }
}
