package com.twl.meeboss.base.main.router

import android.content.Context
import com.twl.meeboss.core.network.HttpResult

/**
 * @author: 冯智健
 * @date: 2024年07月16日 18:31
 * @description:
 */
interface IBaseSettingService {
    fun jumpToMainNotificationGuideActivity(context: Context)

    suspend fun deleteAccount(): HttpResult<Any>

    fun jumpToFeedbackActivity(context: Context, autoFinish:Boolean = false)
}