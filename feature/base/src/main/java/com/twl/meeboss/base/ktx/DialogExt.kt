package com.twl.meeboss.base.ktx

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.compose.ui.geometry.Rect
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.BarUtils
import com.twl.meeboss.base.R
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.main.router.BasePageRouter
import com.twl.meeboss.base.manager.SwitchIdentityManager
import com.twl.meeboss.base.point.PointHelper
import com.twl.meeboss.common.ktx.toPx
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.TimeExtUtils
import com.twl.meeboss.core.ui.activity.FoundationActivity
import com.twl.meeboss.core.ui.dialog.OneColumnWheelDialog
import com.twl.meeboss.core.ui.dialog.TwoColumnWheelDialog
import com.twl.meeboss.core.ui.dialog.showConfirmDialog
import com.twl.meeboss.core.ui.utils.TimeUtils
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.core.ui.utils.toResourceString
import java.util.Calendar

/**
 * 显示开始时间选择弹框
 */
fun FragmentActivity.showSelectTimeDialog(
    title:String = "",
    defaultMonth: Int = 0,
    defaultYear: Int = 0,
    onConfirmClick: (month: Int, year: Int) -> Unit
) {
    val months = TimeUtils.getAllMonths(this)
    val years = TimeExtUtils.getYears()
    var defaultYearIndex = 0
    var defaultMonthIndex = 0
    if (defaultMonth == 0 || defaultYear == 0) {
        val calendar = Calendar.getInstance()
        if (defaultYear == 0) {
            defaultYearIndex = years.indexOf(calendar.get(Calendar.YEAR).toString())
        }
        if (defaultMonth == 0) {
            defaultMonthIndex = calendar.get(Calendar.MONTH)
        }
    }else{
        defaultYearIndex = years.indexOf(defaultYear.toString())
        defaultMonthIndex = defaultMonth - 1
    }

    TwoColumnWheelDialog.newInstance(
        title = title,
        list1 = months,
        defaultIndex1 = defaultMonthIndex,
        list2 = years,
        defaultIndex2 = defaultYearIndex,
        onConfirmClick = { index1, index2 ->
            onConfirmClick(index1 + 1, years[index2].toInt())
        }
    ).showSafely(this)
}


/**
 * 显示年份选择弹框
 */
fun FragmentActivity.showSelectYearDialog(
    title:String = "",
    defaultYear: Int = 0,
    onConfirmClick: (year: Int) -> Unit
) {
    val years = TimeExtUtils.getYears()
    var defaultYearIndex = 0
    if ( defaultYear == 0) {
        val calendar = Calendar.getInstance()
        defaultYearIndex = years.indexOf(calendar.get(Calendar.YEAR).toString())

    }else{
        defaultYearIndex = years.indexOf(defaultYear.toString())
    }

    OneColumnWheelDialog.newInstance(
        title = title,
        list = years,
        defaultIndex = defaultYearIndex,
        onConfirmClick = { index ->
            onConfirmClick(years[index].toInt())
        }
    ).showSafely(this)
}


fun FragmentActivity.showDeleteDialog(title:String,content:String,confirmCallback:()->Unit){
    this.showConfirmDialog(title = title,
        content = content,
        confirmText = R.string.common_button_delete.toResourceString(),
        cancelText = R.string.common_no_thanks.toResourceString(),
        onConfirm = confirmCallback)
}

fun FragmentActivity.showRegisterRightMoreDialog(
    rect: Rect,
    offsetX:Int = 10.toPx,
    offsetY:Int = 0,
){
    var popupWindow:PopupWindow? = null
    val contentView = layoutInflater.inflate(R.layout.base_register_more_dialog,null)
    contentView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
    contentView.findViewById<View>(R.id.clSwitchRole).setOnClickListener {
        popupWindow?.dismiss()
        if (this is FoundationActivity) {
            SwitchIdentityManager(this).switchIdentify()
        }
    }
    contentView.findViewById<View>(R.id.clLogOut).setOnClickListener {
        popupWindow?.dismiss()
        showConfirmDialog(
            title = R.string.common_log_out.toResourceString(),
            content = R.string.common_register_logout_dialog_content.toResourceString(),
            confirmText = R.string.common_log_out.toResourceString(),
            cancelText = R.string.common_button_cancel.toResourceString(),
            onConfirm = {
                AccountManager.logoutWithRequest()
            },
        )
    }
    contentView.findViewById<View>(R.id.clFeedback).setOnClickListener {
        popupWindow?.dismiss()
        if (this is FoundationActivity) {
            BasePageRouter.jumpToFeedbackActivity(this, true)
        }
    }

    // 计算contentView的高宽
    val contentWidth = contentView.measuredWidth
    val locationX = rect.right.toInt() - contentWidth + offsetX
    val locationY = rect.bottom.toInt() + BarUtils.getStatusBarHeight() + offsetY
    popupWindow = PopupWindow(
        contentView,
        ViewGroup.LayoutParams.WRAP_CONTENT,
        ViewGroup.LayoutParams.WRAP_CONTENT,
        true
    ).apply {
        isTouchable = true
        setBackgroundDrawable(ColorDrawable())
        showAtLocation(window.decorView, Gravity.TOP or Gravity.START, locationX, locationY)
    }
}

fun FragmentActivity.showRightMoreDialog(
    rect: Rect,
    layoutResId: Int,
    offsetX:Int = 8.toPx,
    offsetY:Int = 0,
    callback: (View, PopupWindow) -> Unit = {_,_->},
){
    val contentView = layoutInflater.inflate(layoutResId,null)
    contentView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
    // 计算contentView的高宽
    val contentWidth = contentView.measuredWidth
    val locationX = rect.right.toInt() - contentWidth + offsetX
    val locationY = rect.bottom.toInt() + BarUtils.getStatusBarHeight() + offsetY

    var popupWindow = PopupWindow(
        contentView,
        ViewGroup.LayoutParams.WRAP_CONTENT,
        ViewGroup.LayoutParams.WRAP_CONTENT,
        true
    )

    callback(contentView, popupWindow)

    popupWindow.apply {
        isTouchable = true
        setBackgroundDrawable(ColorDrawable())
        showAtLocation(window.decorView, Gravity.TOP or Gravity.START, locationX, locationY)
    }
}


/**
 * 显示时间选择弹框，当前年份开始，一定年份范围内，例如：今年开始，前后50年
 * @param yearRange 年份范围，默认50
 */
fun FragmentActivity.showSelectTimeDialogBeforeAndAfter(
    yearRange:Int = 50,//今年开始，前后多少年，默认50
    title:String = "",
    defaultMonth: Int = 0,
    defaultYear: Int = 0,
    onConfirmClick: (month: Int, year: Int) -> Unit
) {
    val months = TimeUtils.getAllMonths(context = this)
    val years = TimeExtUtils.getYearsByRangeBeforeAndAfter()
    var defaultYearIndex = 0
    var defaultMonthIndex = 0
    if (defaultMonth == 0 || defaultYear == 0) {
        val calendar = Calendar.getInstance()
        if (defaultYear == 0) {
            defaultYearIndex = years.indexOf(calendar.get(Calendar.YEAR).toString())
        }
        if (defaultMonth == 0) {
            defaultMonthIndex = calendar.get(Calendar.MONTH)
        }
    }else{
        defaultYearIndex = years.indexOf(defaultYear.toString())
        defaultMonthIndex = defaultMonth - 1
    }

    TwoColumnWheelDialog.newInstance(
        title = title,
        list1 = months,
        defaultIndex1 = defaultMonthIndex,
        list2 = years,
        defaultIndex2 = defaultYearIndex,
        onConfirmClick = { index1, index2 ->
            onConfirmClick(index1 + 1, years[index2].toInt())
        }
    ).showSafely(this)
}

/**
 * 显示内容改变丢失的警告弹框
 */
fun Context.alertContentChangesDialog(
    onCancel: () -> Unit = {},
    onConfirm: () -> Unit
) {
    showConfirmDialog(
        title = R.string.common_discard_changes.toResourceString(),
        content = R.string.common_discard_changes_dialog_content.toResourceString(),
        cancelText = R.string.coomon_continue_edit_left_button.toResourceString(),
        onCancel = {
            onCancel()
            PointHelper.reportPoint("f3tab-profile-discard") {
                addP("1")
            }
        },
        confirmText = R.string.common_discard.toResourceString(),
        onConfirm = {
            onConfirm()
            PointHelper.reportPoint("f3tab-profile-discard") {
                addP("2")
            }
        }
    )
}
