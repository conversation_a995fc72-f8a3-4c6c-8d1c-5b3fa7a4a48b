package com.twl.meeboss.base.components.industry

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewmodel.compose.viewModel
import com.twl.meeboss.base.R
import com.twl.meeboss.base.api.BaseApi
import com.twl.meeboss.base.components.SuggestListItem
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.foundation.viewmodel.BaseViewModel
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.addSearchKeyToLast
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.core.ui.component.XTextField
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.dismissSafely

/**
 * 选择行业底部弹层
 */

class CommonSearchIndustryBottomSheet : CommonBottomDialogFragment() {
    private var onSelectCallback: ((HighlightBean) -> Unit)? = null
    companion object {
        fun newInstance(onSelectCallback: (HighlightBean) -> Unit) =
            CommonSearchIndustryBottomSheet().apply {
                this.onSelectCallback = onSelectCallback
            }
    }
    @Composable
    override fun DialogContent() {
        CommonSearchIndustryContent(onClickClose = {
            dismissSafely()
        }, callback = {
            onSelectCallback?.invoke(it)
            dismissSafely()
        })
    }

}

@Composable
internal fun CommonSearchIndustryContent(viewModel: CommonSearchIndustryViewModel = viewModel(),
                                         maxInputCount: Int = DefaultValueConstants.MAX_INPUT_COUNT_GEEK_REGISTER_INDUSTRY,
                                         onClickClose: () -> Unit = {},
                                         callback: (item: HighlightBean) -> Unit = {}) {
    LaunchedEffect(Unit) {
        viewModel.getSuggestSearchIndustry()
    }
    val titleId = if(UserProvider.isBoss()) R.string.common_your_company_s_industry else R.string.common_enter_industry
    XTheme {
        Column(modifier = Modifier
            .fillMaxSize()
            .background(Color.White, shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))) {
            Image(painter = painterResource(id = com.twl.meeboss.core.ui.R.drawable.ui_dailog_close), contentDescription = "Close dialog",
                modifier = Modifier
                    .padding(16.dp, 20.dp, 0.dp, 0.dp)
                    .clickable {
                        onClickClose()
                    }
                    .size(24.dp))
            Text(
                modifier = Modifier.padding(16.dp, 18.dp),
                text = stringResource(id = R.string.common_add_industry),
                style = TextStyle(
                    color = Black222222,
                    fontSize = 28.sp,
                    fontWeight = FontWeight.SemiBold
                ))
            Spacer(modifier = Modifier.height(2.dp))
            val input by viewModel.etInput.observeAsState(TextFieldValue("", selection = TextRange.Zero))
            XTextField(modifier = Modifier.padding(16.dp, 0.dp),
                value = input,
                innerTitle = titleId,
                placeHolder = titleId,
                textStyle = TextStyle(fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Black222222
                ),
                showKeyboard = true,
                onValueChange = {
                    if (it.text.length <= maxInputCount) {
                        viewModel.etInput.value = it
                        viewModel.getSuggestSearchIndustry()
                    }else{
                        T.ss(R.string.geek_industry_text_exceed_toast)
                    }
                })
            val list by viewModel.list.observeAsState(listOf())
            val count = list?.size ?: 0
            LazyColumn {
                itemsIndexed(list!!) { index, item ->
                    SuggestListItem(AnnotatedString(item.name), index != count - 1, isAdd = item.isLocalAdd) {
                        callback(item)
                    }
                }
            }

        }
    }
}

@Preview
@Composable
internal fun GeekSearchMajorContentPreview() {
    val mViewModel: CommonSearchIndustryViewModel = viewModel<CommonSearchIndustryViewModel>().also {
        it.list.value = listOf(
            HighlightBean(code = 1, name = "Bloomington, IN, US"),
            HighlightBean(code = 1, name = "Bloomington, CA, US"),
            HighlightBean(code = 1, name = "Bloomington, AL, US"),
            HighlightBean(code = 1, name = "Bloomington, IN, US"),
            HighlightBean(code = 1, name = "Bloomington, IN, US"))
    }
    CommonSearchIndustryContent(mViewModel) {

    }
}

internal class CommonSearchIndustryViewModel : BaseViewModel() {

    val etInput: MutableLiveData<TextFieldValue> = MutableLiveData()

    var list: MutableLiveData<List<HighlightBean>> = MutableLiveData()

    fun getSuggestSearchIndustry() {
        async {
            val api = getService(BaseApi::class.java)
            val input = etInput.value?.text ?: ""
            val result = api.commonSuggestIndustry(input)
            if (result.isSuccess) {
                list.postValue(result.getOrNull()?.list?.addSearchKeyToLast(input) ?: listOf())
            } else {
                list.postValue(listOf())
            }
        }
    }

}
