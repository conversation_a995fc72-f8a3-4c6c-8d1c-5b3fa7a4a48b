package com.twl.meeboss.base.model.boss

import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.base.model.common.CommonAreaCodeBean

/**
 * B的基本个人信息
 */
data class BossUserInfo(
    val userId: String? = null,
    val avatar: String? = "",
    val firstName: String? = "",
    val lastName: String? = "",
    val email: String? = "",
    val emailAuthStatus: Int? = null,
    val phoneNumber: String? = "",
    val countryCodeVO: CommonAreaCodeBean? = null
) : BaseEntity