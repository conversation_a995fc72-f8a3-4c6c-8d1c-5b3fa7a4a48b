package com.twl.meeboss.base.components.company

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.os.bundleOf
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewmodel.compose.viewModel
import com.twl.meeboss.base.api.BaseApi
import com.twl.meeboss.base.foundation.viewmodel.BaseViewModel
import com.twl.meeboss.base.ktx.subString
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.base.model.common.CommonBrandBean
import com.twl.meeboss.base.model.common.addSearchKeyToLast
import com.twl.meeboss.base.R
import com.twl.meeboss.common.ktx.hasEmoji
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.core.ui.component.XTextField
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.theme.alpha
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable

class CommonSearchCompanyBottomSheet(
) : CommonBottomDialogFragment() {

    private var onSelectCallback: ((inputText: String, selectItem: CommonBrandBean) -> Unit)? = null
    private var defaultInputValue: TextFieldValue? = null

    private val hasCustomItem by lazy {
        arguments?.getBoolean("hasCustomItem") ?: false
    }

    companion object {
        fun newInstance(
            hasCustomItem: Boolean = false,
            defaultInputValue: TextFieldValue? = null,
            onSelectCallback: (inputText: String, selectItem: CommonBrandBean) -> Unit
        ) = CommonSearchCompanyBottomSheet().apply {
            this.defaultInputValue = defaultInputValue
            this.onSelectCallback = onSelectCallback
            arguments = bundleOf(
                "hasCustomItem" to hasCustomItem
            )
        }
    }
    @Composable
    override fun DialogContent() {
        CommonSearchCompanyContent(
            hasCustomItem = hasCustomItem,
            defaultInputValue = defaultInputValue,
            onClickClose = {
                dismissSafely()
            },
            callback = { inputText, item ->
                onSelectCallback?.invoke(inputText, item)
                dismissSafely()
            },
        )
    }

}

@Composable
fun CommonSearchCompanyContent(
    hasCustomItem: Boolean = false,
    defaultInputValue: TextFieldValue? = null,
    viewModel: CommonSearchCompanyContentViewModel = viewModel(),
    onClickClose: () -> Unit = {},
    callback: (inputText: String, item: CommonBrandBean) -> Unit
) {
    XTheme {
        LaunchedEffect(key1 = Unit) {
            viewModel.getSuggestJobTitle(hasCustomItem)
            viewModel.setDefaultInput(defaultInputValue)
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Color.White,
                    shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                )
        ) {
            Row(modifier = Modifier.fillMaxWidth()) {
                Image(
                    painter = painterResource(id = R.drawable.ui_dailog_close),
                    contentDescription = "Close dialog",
                    modifier = Modifier
                        .padding(16.dp, 20.dp, 0.dp, 0.dp)
                        .clickable {
                            onClickClose()
                        }
                        .size(24.dp))
                Spacer(modifier = Modifier.weight(1f))

                Text(
                    text = stringResource(id = R.string.common_button_save),
                    fontSize = 16.sp,
                    modifier = Modifier
                        .padding(16.dp, 20.dp, 20.dp, 0.dp)
                        .noRippleClickable {
                            val inputText = viewModel.etInput.value?.text ?: ""
                            if (inputText.isEmpty()) {
                                return@noRippleClickable
                            }
                            callback(
                                inputText,
                                CommonBrandBean(
                                    companyId = "",
                                    companyName = inputText,
                                    isLocalAdd = true
                                )
                            )
                        },
                    textAlign = TextAlign.Center,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    color = if (viewModel.etInput.value?.text.isNullOrEmpty()) {
                        Secondary.alpha(0.5f)
                    } else {
                        Secondary
                    },
                    fontWeight = FontWeight.Medium
                )
            }
            Text(
                modifier = Modifier.padding(16.dp, 18.dp),
                text = stringResource(id = R.string.common_company_name),
                style = TextStyle(
                    color = Black222222,
                    fontSize = 28.sp,
                    fontWeight = FontWeight.SemiBold
                )
            )
            Spacer(modifier = Modifier.height(2.dp))
            val input by viewModel.etInput.observeAsState(
                TextFieldValue(
                    "",
                    selection = TextRange.Zero
                )
            )
            XTextField(
                modifier = Modifier.padding(16.dp, 0.dp),
                value = input,
                innerTitle = R.string.common_enter_company_name,
                placeHolder = R.string.common_enter_company_name,
                textStyle = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Black222222
                ),
                showKeyboard = true,
                onValueChange = { newValue ->
                    if (newValue.text.hasEmoji()) {
                        return@XTextField
                    }
                    newValue.subString(viewModel.maxLength, true).let {
                        viewModel.etInput.value = it
                        viewModel.getSuggestJobTitle(hasCustomItem)
                    }

                })
            val list by viewModel.list.observeAsState(listOf())
            val count = list?.size ?: 0
            LazyColumn {
                itemsIndexed(list!!) { index, item ->
                    if (item.isLocalAdd) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .align(Alignment.Start)
                                .padding(16.dp, 20.dp)
                                .noRippleClickable {
                                    callback(input.text, item)
                                },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Image(
                                modifier = Modifier.padding(end = 6.dp),
                                painter = painterResource(id = R.drawable.base_icon_black_add),
                                contentDescription = ""
                            )
                            Text(
                                modifier = Modifier
                                    .fillMaxWidth(),
                                overflow = TextOverflow.Ellipsis,
                                text = input.text, fontWeight = FontWeight.Medium,
                                fontSize = 16.sp, color = Black222222
                            )
                        }
                    } else {
                        CommonSearchCompanyListItem(
                            item.companyName,
                            item.industry.name,
                            index != count - 1
                        ) {
                            callback(input.text, item)
                        }
                    }
                }
            }

        }
    }
}

@Composable
fun CommonSearchCompanyListItem(
    title: String,
    content: String,
    showDivider: Boolean, onClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .padding(horizontal = 16.dp, vertical = 0.dp)
            .clickable {
                onClick()
            }) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 20.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Text(
                    text = title, fontWeight = FontWeight.Medium,
                    overflow = TextOverflow.Ellipsis,
                    lineHeight = 22.sp,
                    fontSize = 16.sp, color = Black222222
                )
                Spacer(modifier = Modifier.height(2.dp))

                Text(
                    overflow = TextOverflow.Ellipsis,
                    text = content, fontWeight = FontWeight.Normal,
                    lineHeight = 20.sp,
                    fontSize = 14.sp, color = Black484848
                )
            }
        }
        if (showDivider) {
            HorizontalDivider(thickness = 0.5.dp, color = BlackEBEBEB)
        }
    }

}


@Preview
@Composable
fun GeekSearchCompanyContentPreview() {
    val mViewModel: CommonSearchCompanyContentViewModel =
        viewModel<CommonSearchCompanyContentViewModel>().also {
            it.list.value = listOf(
                CommonBrandBean("1", "Google", "https://www.google.com", OptionBean(1L, "IT")),
                CommonBrandBean("1", "Google", "https://www.google.com", OptionBean(1L, "IT")),
                CommonBrandBean("1", "Google", "https://www.google.com", OptionBean(1L, "IT"))
            )
        }
    CommonSearchCompanyContent(viewModel = mViewModel) { inputText, item ->

    }
}

class CommonSearchCompanyContentViewModel : BaseViewModel() {

    val maxLength = 100

    val etInput: MutableLiveData<TextFieldValue> = MutableLiveData()

    var list: MutableLiveData<List<CommonBrandBean>> = MutableLiveData()

    fun getSuggestJobTitle(hasCustomItem: Boolean = false) {
        async {
            val api = getService(BaseApi::class.java)
            val result = api.commonSuggestCompany(etInput.value?.text ?: "")
            if (result.isSuccess) {
                val tempList = (result.getOrNull()?.list?.toMutableList() ?: mutableListOf())
                val resultList = if (hasCustomItem && !etInput.value?.text.isNullOrBlank()) {
                    tempList.addSearchKeyToLast(etInput.value?.text ?: "")
                } else {
                    tempList
                }
                list.postValue(resultList)
            } else {
                list.postValue(listOf())
            }
        }
    }

    fun setDefaultInput(textFieldValue: TextFieldValue?) {
        textFieldValue?.apply {
            etInput.value = this
        }
    }

}
