package com.twl.meeboss.base.initializer.impl

import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.apm.ApmAction
import com.twl.meeboss.base.apm.ApmManager.apmReport
import com.twl.meeboss.base.initializer.IInitializer
import com.twl.meeboss.base.mudule.ModuleManager
import com.twl.meeboss.common.base.AppConfig
import com.twl.meeboss.common.sentry.SentryHelper


class AccountInitializer: IInitializer {
    override fun init(appConfig: AppConfig) {
        AccountManager.registerAccountLifecycle(ModuleManager)
        AccountManager.initialize()
        SentryHelper.setGlobalUserInfo()
    }
}