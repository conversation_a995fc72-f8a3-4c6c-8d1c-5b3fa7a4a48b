package com.twl.meeboss.base.main.functions

import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.twl.meeboss.base.constants.BossCompanyAuthStatus
import com.twl.meeboss.base.function.AbsFunction
import com.twl.meeboss.base.ktx.hasNotificationPermission
import com.twl.meeboss.base.main.router.BasePageRouter
import com.twl.meeboss.base.model.boss.BossInfo
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.preference.SpKey
import com.twl.meeboss.common.preference.SpManager
import com.twl.meeboss.common.provider.UserProvider
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 首页权限相关功能
 * 如果需要在首页声明周期内处理权限相关的功能，请统一在这里添加，除非是点击后申请的
 */
class MainPermissionFunction(private val activity: FragmentActivity) : AbsFunction() {
    private val TAG = this::class.java.simpleName
    private var observer:Observer<BossInfo?>? = null

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        val hasNotificationPermission = hasNotificationPermission()
        val hasShowGuide = SpManager.getUserBoolean(SpKey.KEY_MAIN_NOTIFICATION_GUIDE, false)
        var showNotificationGuide = !hasNotificationPermission && !hasShowGuide
        observer = Observer {
            it?.bossInfoBossVO?.certStatus?.let { certStatus->
                if (certStatus == BossCompanyAuthStatus.AUTH_PASS.value) {
                    XLog.info(TAG, "jumpToMainNotificationGuideActivity1")
                    activity.lifecycleScope.launch {
                        delay(500)
                        BasePageRouter.jumpToMainNotificationGuideActivity(activity)
                    }
                }
                if (observer != null) {
                    BasePageRouter.getBossUserInfo().removeObserver(observer!!)
                }
            }
        }

        XLog.info(TAG, "role:${UserProvider.getIdentify()},hasShowGuide:${hasShowGuide},hasPer:${hasNotificationPermission}")
        if (UserProvider.isBoss() && !hasShowGuide && !hasNotificationPermission) {
            if (observer != null) {
                BasePageRouter.getBossUserInfo().observe(owner, observer!!)
            }
            showNotificationGuide = false
        }
        XLog.info(TAG, "showNotificationGuide:${showNotificationGuide}")
        if (showNotificationGuide) {
            XLog.info(TAG, "jumpToMainNotificationGuideActivity2")
            owner.lifecycleScope.launch {
                delay(500)
                BasePageRouter.jumpToMainNotificationGuideActivity(activity)
            }
        }
    }
}