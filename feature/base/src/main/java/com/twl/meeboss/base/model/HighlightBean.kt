package com.twl.meeboss.base.model

import com.squareup.moshi.Json


data class HighlightBean(
    val code: Long = 0L,
    val name: String = "",
    val parentCode: Long = 0L,
    @<PERSON><PERSON>(ignore = true)
    val highlights: List<Highlight>? = listOf(),
    @<PERSON><PERSON>(ignore = true)
    var localEnable: Boolean = true,
    @Json(ignore = true)
    val isLocalAdd: Boolean = false,
) : BaseEntity{
    fun uniqueKey(): String {
        return "$code$name"
    }
}

/**
 * 把搜索词添加到最后

 */
fun List<HighlightBean>?.addSearchKeyToLast(key: String): List<HighlightBean> {
    if (key.isBlank()) {
        return this ?: listOf()
    } else {
        val keys = this?.map { it.name } ?: listOf()
        if (keys.contains(key)) {
            return this ?: listOf()
        } else {
            val list = this?.toMutableList() ?: mutableListOf()
            list.add(HighlightBean(code = 0, name = key, isLocalAdd = true))
            return list
        }
    }

}
