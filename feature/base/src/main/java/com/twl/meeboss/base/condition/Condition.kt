package com.twl.meeboss.base.condition

/**
 * @author: musa on 2025/05/23
 * @e-mail: <EMAIL>
 * @desc: 条件的抽象基类，定义了条件评估的基本行为。
 *        每个条件持有一个可变类型的变量 `variable`，并能够根据该变量评估出一个布尔结果。
 *
 * @param T 条件所操作的变量类型。
 * @property variable 条件依赖的变量，其值可以通过 `handleEvaluation` 方法更新。
 */
abstract class Condition<T> (var variable: T) {
    private var _result: Boolean? = null
    /**
     * 条件的最新评估结果。
     * 初始为 `null`，在 `handleEvaluation` 被调用后或直接调用 `evaluate` (如果子类覆盖了缓存逻辑) 后更新。
     * `ConditionEvaluator` 在 `calculate(byCache = true)` 时会使用此缓存结果。
     */
    val result: Boolean? = _result

    /**
     * 更新条件内部的变量，并重新执行评估逻辑，缓存评估结果。
     *
     * @param newValue 用于评估的新变量值。
     * @return 返回最新的评估结果 (true 或 false)。
     */
    fun handleEvaluation(newValue: T): Boolean{
        variable = newValue
        val result = evaluate()
        _result = result
        return result
    }

    /**
     * 核心评估方法，子类必须实现此方法以定义具体的条件判断逻辑。
     * 该方法会基于当前的 `variable` 值返回一个布尔结果。
     *
     * @return 条件的评估结果 (true 或 false)。
     */
    abstract fun evaluate(): Boolean

}

/**
 * 一个简单的布尔条件，评估其内部 `variable` (布尔类型) 是否为 `true`。
 *
 * @param variable 初始的布尔值。
 */
class BoolCondition(variable: Boolean): Condition<Boolean>(variable) {
    /**
     * 评估结果直接为其布尔类型的 `variable` 值。
     */
    override fun evaluate() = variable
}

/**
 * 检查字符串是否不为空白 (即非null且至少包含一个非空白字符) 的条件。
 *
 * @param text 初始的字符串值。
 */
class NotBlankCondition(text: String): Condition<String>(text) {
    /**
     * 评估结果为其字符串类型 `variable` 调用 `isNotBlank()` 的结果。
     */
    override fun evaluate() = variable.isNotBlank()
}

/**
 * 检查当前变量值是否与其原始值不同的条件。
 * 这可以用于检测字段是否已被修改。
 *
 * @param T 变量的类型。
 * @param original 原始值，用于与当前的 `variable`进行比较。
 */
class NotOriginalCondition<T>(private val original: T) : Condition<T>(original) {
    /**
     * 评估结果为当前 `variable` 是否不等于 `original` 值。
     */
    override fun evaluate() = variable != original
}