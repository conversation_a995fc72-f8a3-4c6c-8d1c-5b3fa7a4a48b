package com.twl.meeboss.base.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.R
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.BlackEBEBEB


@Composable
fun SuggestListItem(title: AnnotatedString, showDivider: <PERSON><PERSON><PERSON>, isAdd: <PERSON>olean = false, onClick: () -> Unit) {
    Column(modifier = Modifier
        .padding(16.dp, 0.dp)
        .height(70.dp)
        .clickable {
            onClick()
        }) {
        Box(modifier = Modifier
            .fillMaxWidth()
            .weight(1F)) {

            Row(modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.CenterStart), verticalAlignment = Alignment.CenterVertically) {
                if (isAdd) {
                    Image(modifier = Modifier.padding(end = 6.dp),painter = painterResource(id = R.drawable.base_icon_black_add), contentDescription = "")
                }
                Text(
                    modifier = Modifier
                        .fillMaxWidth(),
                    overflow = TextOverflow.Ellipsis,
                    text = title, fontWeight = FontWeight.Medium,
                    fontSize = 16.sp, color = Black222222)
            }

        }
        if (showDivider) {
            HorizontalDivider(thickness = 0.5.dp, color = BlackEBEBEB)
        }
    }

}
