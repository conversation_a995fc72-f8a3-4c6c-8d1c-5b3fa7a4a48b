package com.twl.meeboss.base.ktx

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.PermissionUtils
import com.permissionx.guolindev.PermissionX

/**
 * 请求电话权限
 */
fun FragmentActivity.requestPhonePermission(success: () -> Unit, fail: () -> Unit = {}) {
    PermissionX.init(this)
        .permissions(Manifest.permission.CALL_PHONE)
        .request { allGranted, grantedList, deniedList ->
            if (allGranted) {
                success()
            } else {
                fail()
            }
        }
}

fun FragmentActivity.requestNotificationPermission(success: () -> Unit, fail: () -> Unit = {}) {
    PermissionX.init(this)
        .permissions(PermissionX.permission.POST_NOTIFICATIONS)
        .request { allGranted, grantedList, deniedList ->
            if (allGranted) {
                success()
            } else {
                fail()
            }
        }
}

/**
 * 跳转系统通知权限设置页
 */
fun Context.startSystemNotificationPermissionSetting() {
    try {
        val intent = Intent()
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS)
        intent.putExtra(Settings.EXTRA_APP_PACKAGE, packageName)
        intent.putExtra(Settings.EXTRA_CHANNEL_ID, applicationInfo.uid)
        startActivity(intent)
    } catch (e: Exception) {
        e.printStackTrace()

        val intent = Intent()
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        val uri = Uri.fromParts("package", packageName, null)
        intent.setData(uri)
        startActivity(intent)
    }
}

fun hasNotificationPermission():Boolean {
    return isPermissionGranted(PermissionX.permission.POST_NOTIFICATIONS)
}

fun isPermissionGranted(permission:String):Boolean {
    return PermissionUtils.isGranted(permission)
}
fun isPermissionGranted(permissionList:List<String>):Boolean {
    return PermissionUtils.isGranted(*permissionList.toTypedArray())
}

fun startPermissionActivity(activity: Activity, permission:String) {
    startPermissionActivity(activity, listOf(permission))
}

fun startPermissionActivity(context: Context, permissionList: List<String>) {
    //跳转应用消息，间接打开应用权限设置-效率高
    val intent = Intent()
    intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
    val uri = Uri.fromParts("package", context.packageName, null)
    intent.setData(uri)
    context.startActivity(intent)
}