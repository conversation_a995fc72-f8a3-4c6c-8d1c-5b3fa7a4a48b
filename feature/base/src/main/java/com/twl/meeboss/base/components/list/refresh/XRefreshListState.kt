package com.twl.meeboss.base.components.list.refresh

import android.content.Context
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import com.king.ultraswiperefresh.UltraSwipeRefreshState
import com.twl.meeboss.base.R
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.common.provider.ContextProvider
import com.twl.meeboss.core.ui.utils.toResourceString

data class XRefreshListState<T>(
    val list: List<T> = listOf(),
    val loadState: LoadState = LoadState.Loading,
    val refreshState: UltraSwipeRefreshState = UltraSwipeRefreshState(isRefreshing = false, isLoading = false),
    val lazyListState: LazyListState = LazyListState(),
    val refreshEnable: Boolean = true,   // 是否允许刷新
    val refreshText: String = "",
    val loadMoreEnable: Boolean = true,  // 是否允许加载更多
    var isLoadingMore:Boolean = false,
    val hasMore: Boolean = true,
    val loadFinishText: String = "",
) {
    fun isError() = loadState is LoadState.Fail

    fun isLoading() = loadState is LoadState.Loading

    fun isEmpty() = loadState is LoadState.Empty || list.isEmpty()

    fun isSuccess() = loadState is LoadState.Success

    fun refreshSuccess(list: List<T>,hasMore: Boolean): XRefreshListState<T> {
        if(list.isEmpty()){
           return refreshEmpty()
        }
        refreshState.isRefreshing = false
        return copy(list = list, loadState = LoadState.Success,refreshState = refreshState,hasMore = hasMore)
    }

    /**
     * 本地刷新数据
     * eg:更新item的开聊状态
     */
    fun refreshData(list: List<T>): XRefreshListState<T> {
        return copy(list = list)
    }

    fun refreshFail(content:String? = R.string.common_network_error_tips.toResourceString(ContextProvider.getAppContext()),
                    buttonText:String? = com.twl.meeboss.core.ui.R.string.common_error_try_again.toResourceString(ContextProvider.getAppContext())): XRefreshListState<T> {
        refreshState.isRefreshing = false
        return copy(loadState = LoadState.Fail(content = content, buttonText = buttonText))
    }

    private fun refreshEmpty(): XRefreshListState<T> {
        refreshState.isRefreshing = false
        return copy(loadState = LoadState.Empty())
    }

    fun loading(): XRefreshListState<T> {
        refreshState.isRefreshing = false
        return copy(loadState = LoadState.Loading)
    }

    fun loadMoreSuccess(list: List<T>,hasMore:Boolean): XRefreshListState<T> {
        return copy(list = this.list + list,hasMore = hasMore, isLoadingMore = false)
    }

    fun loadMoreFail(): XRefreshListState<T> {
        return copy(list = this.list, isLoadingMore = false)
    }

    companion object {
        fun <T : Any> getDefault(list: List<T> = emptyList(),context: Context = ContextProvider.getContext()): XRefreshListState<T> {
            return XRefreshListState(list = list,
                loadFinishText = R.string.common_no_more_results.toResourceString(context),
                refreshText = R.string.common_data_updated.toResourceString(context),
                loadState = LoadState.Loading)
        }

        @Composable
        @ReadOnlyComposable
        fun <T : Any> getPreviewDefault(list: List<T> = emptyList()): XRefreshListState<T> {
            return XRefreshListState(list = list,
                loadState = LoadState.Success)
        }

    }
}