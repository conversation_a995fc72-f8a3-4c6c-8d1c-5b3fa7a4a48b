package com.twl.meeboss.base.point

import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase

class FirebaseAnalyticsFactory {
    private var firebaseAnalytics: FirebaseAnalytics = Firebase.analytics

    companion object {
        fun create(): FirebaseAnalyticsFactory {
            return FirebaseAnalyticsFactory()
        }
    }

    fun report(action: String, block: ((HashMap<String, String>) -> Unit)? = null) {
        val param: HashMap<String, String> = hashMapOf()
        block?.invoke(param)
        firebaseAnalytics.logEvent(action, Bundle().apply {
            param.forEach { (key, value) ->
                putString(key, value)
            }
        })
    }
}

fun reportFirebaseAnalytics(action: String, block: ((HashMap<String, String>) -> Unit)? = null) {
    /*
    reportFirebaseAnalytics("first_action") {
        it["key1"] = "value1"
        it["key2"] = "value2"
    }
    */
    FirebaseAnalyticsFactory.create().report(action, block)
}