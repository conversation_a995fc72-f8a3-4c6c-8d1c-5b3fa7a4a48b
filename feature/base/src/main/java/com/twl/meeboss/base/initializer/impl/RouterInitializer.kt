package com.twl.meeboss.base.initializer.impl

import android.text.TextUtils
import com.blankj.utilcode.util.Utils
import com.sankuai.waimai.router.Router
import com.sankuai.waimai.router.common.DefaultRootUriHandler
import com.sankuai.waimai.router.components.DefaultLogger
import com.sankuai.waimai.router.components.RouterComponents
import com.sankuai.waimai.router.core.Debugger
import com.sankuai.waimai.router.core.OnCompleteListener
import com.sankuai.waimai.router.core.UriRequest
import com.sankuai.waimai.router.core.UriResult
import com.twl.meeboss.base.apm.ApmAction
import com.twl.meeboss.base.apm.ApmManager
import com.twl.meeboss.base.initializer.IInitializer
import com.twl.meeboss.common.BuildConfig
import com.twl.meeboss.common.base.AppConfig
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.router.handle.ActivityForResultLauncher
import com.twl.meeboss.common.utils.T

/**
 * @author: 冯智健
 * @date: 2024年06月27日 18:05
 * @description:
 */
class RouterInitializer : IInitializer {
    override fun init(appConfig: AppConfig) {
        if (appConfig.isDebug) {
            // 设置Logger
            Debugger.setLogger(object : DefaultLogger() {
                override fun handleError(t: Throwable) {
                    super.handleError(t)
                    throw t
                }
            })
            // Log开关，建议测试环境下开启，方便排查问题。
            Debugger.setEnableLog(BuildConfig.DEBUG)
            // 调试开关，建议测试环境下开启。调试模式下，严重问题直接抛异常，及时暴漏出来。
            Debugger.setEnableDebug(BuildConfig.DEBUG)
        }
        // 适配5.x、6.x 及高版本中startActivity的bug
        RouterComponents.setActivityLauncher(ActivityForResultLauncher())
        // 初始化
        Router.init(DefaultRootUriHandler(Utils.getApp()).apply {
            // 设置全局跳转完成监听器，可用于跳转失败时统一弹Toast提示，做埋点统计等
            globalOnCompleteListener = MeeBossRouterCompleteListener.INSTANCE
        })
    }
}

class MeeBossRouterCompleteListener : OnCompleteListener {
    override fun onSuccess(request: UriRequest) {

    }

    override fun onError(request: UriRequest, resultCode: Int) {
        var text = request.getStringField(UriRequest.FIELD_ERROR_MSG, null)
        if (TextUtils.isEmpty(text)) {
            text = when (resultCode) {
                UriResult.CODE_NOT_FOUND -> "Unsupported jump link"
                UriResult.CODE_FORBIDDEN -> "No permission"
                else -> "Jump failed"
            }
        }
        text += "($resultCode)"
        if (Debugger.isEnableDebug()) {
            text += "\n${request.uri}"
        }
        XLog.error(TAG, "onError: $text")
        if(BuildConfig.DEBUG){
            T.ss(text)
        }
        ApmManager.apmReport(ApmAction.ROUTER_EXCEPTION,""){
            put("error","url:${request.uri}, error:$resultCode,detail:$text")
        }
    }

    companion object {
        private val TAG = MeeBossRouterCompleteListener::class.java.simpleName
        val INSTANCE: MeeBossRouterCompleteListener = MeeBossRouterCompleteListener()
    }
}
