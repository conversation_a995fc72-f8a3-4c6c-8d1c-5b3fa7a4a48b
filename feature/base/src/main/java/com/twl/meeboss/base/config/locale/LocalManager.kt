package com.twl.meeboss.base.config.locale

import android.app.Activity
import android.content.Context
import android.content.SharedPreferences
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.ActivityUtils
import com.twl.meeboss.base.R
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.config.NetEnvironment
import com.twl.meeboss.base.config.language.LanguageUtil
import com.twl.meeboss.base.main.router.BasePageRouter
import com.twl.meeboss.base.main.router.BaseServiceRouter
import com.twl.meeboss.base.model.common.CommonRegion
import com.twl.meeboss.common.base.AppConfig
import com.twl.meeboss.common.preference.SpKey
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.common.utils.DeviceExtUtils
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.BuildConfig
import com.twl.meeboss.core.network.config.EnvironmentType
import com.twl.meeboss.core.network.config.INetEnvironment
import com.twl.meeboss.core.ui.utils.handleSafely
import com.twl.meeboss.core.ui.utils.toResourceString
import java.util.Locale

/**
 * 本地配置管理(语言、国家、服务器环境)
 */
object LocalManager {
    val localizationEnable:Boolean = true
    private const val SP_NAME = "local_config"
    private var mAppConfig:AppConfig  = AppConfig()
    private var mLocaleConfig: LocaleConfig =
        LocaleConfig("", "", "", "", 0, environment =  NetEnvironment.American.OnLine())

    private val listeners: MutableList<ILocaleChangedListener> = mutableListOf()

    fun getConfig(): LocaleConfig {
        return mLocaleConfig
    }

    fun init(context: Context, appConfig: AppConfig) {
        mAppConfig = appConfig
        val sp = getSp(context)
        //获取打包的目标环境
        val defaultEnvironmentType = appConfig.environment
        val currentEnvironmentType = if (BuildConfig.DEBUG) {
            //从sp中读取设置的环境(QA环境可能会设置)
            sp.getInt(SpKey.KEY_APP_ENVIRONMENT, defaultEnvironmentType)
        } else {
            defaultEnvironmentType
        }

        //从本地获取手机的国家和语言
        val currentCountry = sp.getString(SpKey.KEY_APP_COUNTRY, getSystemCountry())
            ?: getSystemCountry()
        val currentLanguage =
            sp.getString(SpKey.KEY_APP_LANGUAGE, getSystemLanguage())
                ?: getSystemLanguage()
        //从sp中读取设置的国家和语言
        val config = LocaleConfig(
            appCountry = currentCountry,
            phoneCountry = LanguageUtil.systemLocal.country,
            appLanguage = currentLanguage,
            phoneLanguage = LanguageUtil.systemLocal.language,
            environmentType = currentEnvironmentType,
            environment = initNewEnvironment(sp, currentCountry, currentEnvironmentType)
        )
        applyConfig(context, config,true)
    }



    private fun innerUpdateConfig(
        config: LocaleConfig, context: Context, region: CommonRegion? = null
    ) {

        val changedCountry = config.appCountry != mLocaleConfig.appCountry
        val changedLanguage = config.appLanguage != mLocaleConfig.appLanguage
        if (UserProvider.isLogin()) {//登录状态下
            if (changedCountry) {//如果国家改变了，需要重新登录
                AccountManager.logout {
                    applyConfig(context, config)
                    saveConfig(context, config.appCountry, config.appLanguage, region)
                    LanguageUtil.switchLanguage(config.appLanguage, config.appCountry, context as Activity) {
                        BaseServiceRouter.jumpToLoginActivity(ActivityUtils.getTopActivity())
                    }
                }
            } else {//如果国家没变
                if (changedLanguage) {//修改了语言
                    applyConfig(context, config)
                    saveConfig(context, config.appCountry, config.appLanguage, region)
                    LanguageUtil.switchLanguage(config.appLanguage, config.appCountry, context as Activity) {
                        //回到首页
                        BasePageRouter.jumpToMainActivity(context, 0, true)
                    }
                }
            }
        } else {
            applyConfig(context, config)
            saveConfig(context, country = config.appCountry, language = config.appLanguage, region = region)
            if (context is FragmentActivity) {
                context.handleSafely {
                    LanguageUtil.setLanguage(context, config.appLanguage, config.appCountry)
                }
            }
        }


    }

    fun justUpdateLanguage(language: String, country: String ,context: Context){
        if(language.isNotBlank() && language != mLocaleConfig.appLanguage && isLocalSupportLanguage(language)){
            applyConfig(context, mLocaleConfig.copy(appLanguage = language))
            getSp(context).edit().run {
                putString(SpKey.KEY_APP_LANGUAGE, language)
                apply()
            }
            LanguageUtil.setLanguage(context as Activity,language,country)
        }
    }

    /**
     * 外部更新配置
     */
    fun updateConfig(context: Activity, country: String, language: String, region: CommonRegion?) {
        //地区和语言没有改变
        if (mLocaleConfig.appLanguage == language && mLocaleConfig.appCountry == country) {
            return
        }
        if (mLocaleConfig.appCountry == country) {//国家相同，必然语言不同，换个语言就行了
            //切换语言
            if (isLocalSupportLanguage(language)) {
                innerUpdateConfig(mLocaleConfig.copy(appLanguage = language), context)
            } else {
                T.ss("Not support language")
            }

        } else {//国家不同

            val newLanguage = if (mLocaleConfig.appLanguage != language) {//语言也不同
                if (isLocalSupportLanguage(language)) language else mLocaleConfig.appLanguage
            } else {
                mLocaleConfig.appLanguage
            }
            innerUpdateConfig(
                mLocaleConfig.copy(appLanguage = newLanguage, appCountry = country),
                context,
                if (isLocaleSupportCountry(country)) null else region
            )

        }
    }


    private fun applyConfig(context: Context, config: LocaleConfig,isInit:Boolean = false) {
        val changedCountry = mLocaleConfig.appCountry != config.appCountry
        mLocaleConfig = config.copy(environment = NetEnvironment.switchEnvironment(config.appCountry, config.environmentType))
        listeners.forEach {
            it.onLocaleChanged(mLocaleConfig,isInit,changedCountry)
        }
    }


    /**
     * 把配置保存到sp
     */
    private fun saveConfig(
        context: Context,
        country: String,
        language: String,
        region: CommonRegion?
    ) {
        val sp = getSp(context)
        sp.edit().run {
            putString(SpKey.KEY_APP_COUNTRY, country)
            putString(SpKey.KEY_APP_LANGUAGE, language)
            if (region != null) {
                putString(SpKey.KEY_SERVER_CONFIG_HOST_BEAN, GsonUtils.toJson(region))
            } else {
                putString(SpKey.KEY_SERVER_CONFIG_HOST_BEAN, "")
            }
            apply()
        }


    }

    fun addListeners(listener: ILocaleChangedListener) {
        if (listeners.contains(listener)) {
            return
        }
        listeners.add(listener)
    }

    fun removeListener(listener: ILocaleChangedListener) {
        listeners.remove(listener)
    }

    /**
     * 初始化服务器环境变量
     */
    private fun initNewEnvironment(
        sp: SharedPreferences,
        country: String,
        @EnvironmentType environmentType: Int
    ): INetEnvironment {
        val serverConfigData = sp.getString(SpKey.KEY_SERVER_CONFIG_HOST_BEAN, "")
        if (isLocaleSupportCountry(country) || serverConfigData.isNullOrBlank()) {
            return NetEnvironment.switchEnvironment(country, environmentType)
        } else {
            val region = GsonUtils.fromJson(serverConfigData, CommonRegion::class.java)
                ?: return NetEnvironment.switchEnvironment(country, environmentType)
            return NetEnvironment.CustomEnvironment(
                region.region,
                environmentType,
                region.apiDomain,
                region.h5Domain,
                region.mqttHost,
                region.mqttPort
            )
        }
    }

    private fun getSp(context: Context): SharedPreferences {
        return context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE)
    }

    /**
     * 把语言字段更新到DeviceExtUtils，提供给公参使用
     */
    private fun updateLanguageString() {
        DeviceExtUtils.updateLanguageString(mLocaleConfig.appLanguage)
    }
    private fun updateCountryString() {
        DeviceExtUtils.updateCountryString(mLocaleConfig.appCountry)
    }

    /**
     * 是否已经同步过国家
     */
    fun hasSyncedCountry(context: Context): Boolean {
        val sp = getSp(context)
        return sp.getBoolean(SpKey.KEY_HAS_SYNCED_COUNTRY, false).also {
            sp.edit().putBoolean(SpKey.KEY_HAS_SYNCED_COUNTRY, true).apply()
        }
    }


    /**
     * 获取手机系统国家，如果是欧洲国家，则返回德国，否则返回美国
     */
    private fun getSystemCountry(): String {
        if(localizationEnable){
            val sysCountry = LanguageUtil.systemLocal.country
            return if (sysCountry.isEuropeCountry()) {
                Locale.GERMANY.country
            } else {
                Locale.US.country
            }
        }else{
            return Locale.US.country
        }

    }

    /*
     * 获取手机系统语言，如果是欧洲国家，且语言是德语，则返回德语，否则返回英语
     */
    private fun getSystemLanguage(): String {
        if(localizationEnable){
            val sysCountry = LanguageUtil.systemLocal.country
            val sysLanguage = LanguageUtil.systemLocal.language
            return if (sysCountry.isEuropeCountry()) {
                if (sysLanguage == Locale.GERMANY.language) {
                    Locale.GERMANY.language
                } else {
                    Locale.US.language
                }
            } else {
                Locale.US.language
            }
        }else{
            return Locale.US.language
        }
    }


    private fun String.isEuropeCountry(): Boolean {
        return when (this) {
            "AL", "AD", "AT", "BY", "BE", "BA", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IS", "IE", "IT", "LV", "LI", "LT", "LU", "MT", "MD", "MC", "ME", "NL", "MK", "NO", "PL", "PT", "RO", "RU", "SM", "RS", "SK", "SI", "ES", "SE", "CH", "UA", "UK" -> true
            else -> false
        }
    }

    fun setLanguage(context: Context, language: String) {
        getSp(context).edit().putString(SpKey.KEY_APP_LANGUAGE, language).apply()
        mLocaleConfig = mLocaleConfig.copy(appLanguage = language)
        updateLanguageString()
        updateCountryString()
    }


     fun isLocaleSupportCountry(country: String): Boolean {
        return listOf(Locale.US.country, Locale.GERMANY.country).contains(country)
    }

    fun isLocalSupportLanguage(language: String): Boolean {
        return listOf(Locale.US.language, Locale.GERMANY.language).contains(language)
    }


    fun getMoneyUnitLabel():String{
        return when(mLocaleConfig.appCountry){
            Locale.GERMANY.country -> "€"
            else -> "$"
        }
    }
    fun getAppLocale():Locale{
        return Locale(mLocaleConfig.appLanguage, mLocaleConfig.appCountry)
    }

    fun getSalaryUnitParamValue():Int{
        return when(mLocaleConfig.appCountry){
            Locale.GERMANY.country -> 2
            else -> 1
        }
    }

    fun getSalaryUnitNameValue():String {
        return R.string.common_salary_unit.toResourceString()
    }

    fun saveCustomEnvironment(context: Context,type: Int,country: String,language: String){
        getSp(context).edit().putInt(SpKey.KEY_APP_ENVIRONMENT,type)
            .putString(SpKey.KEY_APP_COUNTRY,country)
            .putString(SpKey.KEY_APP_LANGUAGE,language).commit()
    }

    fun resetEnvironment(context: Context,country: String,language: String){
        getSp(context).edit().putInt(SpKey.KEY_APP_ENVIRONMENT, mAppConfig.environment)
            .putString(SpKey.KEY_APP_COUNTRY,country)
            .putString(SpKey.KEY_APP_LANGUAGE,language).commit()
    }

    fun isGermany():Boolean{
        return Locale.GERMANY.country == mLocaleConfig.appCountry
    }
}