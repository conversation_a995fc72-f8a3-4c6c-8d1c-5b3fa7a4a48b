package com.twl.meeboss.base.ktx

import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.base.main.router.BaseServiceRouter
import com.twl.meeboss.common.provider.UserProvider

fun FragmentActivity.checkLoginAndCompleteStatus(bundle: Bundle? = null): Bo<PERSON>an {
    var needHandle = false
    if (!UserProvider.isLogin()) {
        TLog.info("MainActivity", "User not login")
        BaseServiceRouter.jumpToLoginActivity(this, bundle)
        finish()
        needHandle = true
    } else if (AccountManager.getFirstCompleteStatus() != UserConstants.COMPLETE_STATUS_ALREADY_COMPLETE) {
        TLog.info("MainActivity", "user not complete")
        BaseServiceRouter.afterLogin(this)
        needHandle = true
    }
    return needHandle
}

fun FragmentActivity.showRobotCheckDialog(oldToken: String? = "", callback: ((String) -> Unit)) {
    BaseServiceRouter.showRobotCheckDialog(this, oldToken, callback)
}