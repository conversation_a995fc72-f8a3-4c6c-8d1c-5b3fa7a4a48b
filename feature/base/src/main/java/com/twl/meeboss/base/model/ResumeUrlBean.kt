package com.twl.meeboss.base.model

import com.blankj.utilcode.util.PathUtils
import java.io.File

/**
 * 拿简历id请求接口得到的真正预览的bean
 */
data class ResumeUrlBean(
    val originUrl: String = "",
    val previewUrl: String = "",
    val resumeId: String = "",
    val fileName: String = "",
) : BaseEntity {
    fun isDownloaded():Boolean {
        return File(getLocalFilePath()).exists()
    }

    fun getLocalDownloadPath():String {
        return PathUtils.getExternalAppDownloadPath() + File.separator + "attachment" + File.separator + resumeId
    }

    fun getLocalFilePath():String {
        return getLocalDownloadPath() + File.separator + fileName
    }
}