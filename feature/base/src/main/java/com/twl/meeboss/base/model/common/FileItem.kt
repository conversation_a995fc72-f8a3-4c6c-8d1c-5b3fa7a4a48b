package com.twl.meeboss.base.model.common

import android.net.Uri
import android.os.Parcelable
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.ImageUtils
import com.blankj.utilcode.util.Utils
import com.twl.meeboss.base.R
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.media.imagepicker.util.FileUriUtils
import com.twl.meeboss.base.model.enumeration.FileType
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.utils.toResourceString
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
data class FileItem(
    val uri: Uri? = null,
    val url: String? = null,
    val fileName: String? = null,
    val mimeType: String? = null,
    val fileSize: Long = 0,
    val token:String = "",
    val date: Long = -1,
    val imageWidth: Int = 0, //图片宽度,只有拍照和选择图片场景下有
    val imageHeight: Int = 0 //图片高度,只有拍照和选择图片的场景下有
) : Parcelable {

    @IgnoredOnParcel
    val isImage: Boolean = getFileType() == FileType.IMAGE

    @IgnoredOnParcel
    val isVideo: Boolean = getFileType() == FileType.VIDEO

    @IgnoredOnParcel
    val isFromNet: Boolean = uri?.toString()?.startsWith("http", true) == true

    fun getFileType() = if (isFromNet) {
        when {
            uri.toString().isHttpImage() -> FileType.IMAGE
            else -> FileType.NONE
        }
    } else {
        when {
            mimeType?.startsWith("image", true) == true -> FileType.IMAGE
            mimeType?.startsWith("video", true) == true -> FileType.VIDEO
            mimeType?.startsWith("audio", true) == true -> FileType.AUDIO
            mimeType?.startsWith("application", true) == true -> FileType.DOCUMENT
            else -> FileType.NONE
        }
    }

    private fun String?.isHttpImage(): Boolean {
        ImageUtils.ImageType.entries.map {
            return this?.endsWith(it.value, true) == true
        }
        return this?.endsWith("jpeg", true) == true
    }
}

/**
 * 不超过{@link DefaultValueConstants#IMAGE_MAX_SIZE_BYTE}的FileItem列表
 */
fun List<FileItem>.getLegalImageItem(needToast:Boolean = false):List<FileItem> {
    val mediaItems = mutableListOf<FileItem>()
    var hasExceedItem = false
    this.forEach { e ->
        e.uri?.let {
            if (FileUtils.getFileLength(FileUriUtils.getRealPath(Utils.getApp(), it)) > DefaultValueConstants.IMAGE_MAX_SIZE_BYTE) {
                hasExceedItem = true
            } else {
                mediaItems.add(e)
            }
        }
    }
    if (hasExceedItem && needToast) {
        T.ss(
            R.string.common_upload_image_size_exceed_limit_tips.toResourceString(DefaultValueConstants.IMAGE_MAX_SIZE_M.toString())
        )
    }
    return mediaItems
}