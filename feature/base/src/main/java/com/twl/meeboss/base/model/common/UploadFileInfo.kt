package com.twl.meeboss.base.model.common

import com.twl.meeboss.base.model.BaseEntity

/**
 * @author: 冯智健
 * @date: 2024年08月01日 17:59
 * @description:
 */
data class UploadFileInfo(
    val fileName: String,
    val contentType: String,
    val contentLength: Long,
    val contentMD5: String,
    val filePath: String,
) : BaseEntity

sealed class UploadFileResult {
    data class DocumentUploadFileResult(
        val originFileItem: FileItem,
        val keyName: String
    ): UploadFileResult()

    data class ImageUploadFileResult(
        val originFileItem: FileItem,
        val keyName: String?,
        val originUrl: String?,
        val thumbUrl: String?
    ): UploadFileResult()
}
