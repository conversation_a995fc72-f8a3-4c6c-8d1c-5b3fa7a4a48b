package com.twl.meeboss.base.foundation.repo

import com.twl.meeboss.base.foundation.IRepository
import com.twl.meeboss.common.exp.ApiException
import com.twl.meeboss.common.exp.EmptyDataException
import com.twl.meeboss.common.exp.NetworkException
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.HttpResult

open class BaseRepository : IRepository {

    val TAG: String = this::class.java.simpleName

    suspend fun <T : Any?> safeCallApi(
        call: suspend () -> HttpResult<T?>
    ): Result<T> {
        val httpResult = call()
        return httpResult.toResult()
    }
}

fun <T : Any?> HttpResult<T?>.toResult(): Result<T> {
    try {
        val result = when (this) {
            is HttpResult.Success -> {
                this.value?.let {
                    Result.success(it)
                } ?: run {
                    Result.failure(EmptyDataException())
                }
            }

            is HttpResult.ApiError -> {
                Result.failure(ApiException(null, this.message, this.code, this.errorType))
            }

            is HttpResult.NetworkError -> {
                Result.failure(
                    NetworkException(
                        this.message,
                        this.error,
                        this.errorType
                    )
                )
            }

            is HttpResult.UnknownError -> {
                Result.failure(Throwable(this.throwable))
            }

            is HttpResult.Error -> {
                Result.failure(Throwable(this.message))
            }
        }
        return result
    } catch (t: Throwable) {
        return Result.failure(t)
    }
}

fun Result<*>.toastErrorIfPresent() = exceptionOrNull()?.message?.let { T.ss(it) }
