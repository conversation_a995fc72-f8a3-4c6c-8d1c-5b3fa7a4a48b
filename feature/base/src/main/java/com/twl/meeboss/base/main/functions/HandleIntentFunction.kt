package com.twl.meeboss.base.main.functions

import android.content.Intent
import android.net.Uri
import com.twl.meeboss.base.function.AbsFunction
import com.twl.meeboss.base.main.MainActivity
import com.twl.meeboss.base.manager.ProtocolHandleManager
import com.twl.meeboss.common.log.XLog

/**
 * 外部通过 app link 协议打开app需要解析协议内容
 */
class HandleIntentFunction(val activity: MainActivity) : AbsFunction() {
    private val TAG = "HandleIntentFunction"

    override fun onCustom() {
        super.onCustom()
        handleIntent()
    }

    private fun handleIntent() {
        //region 处理 app links.
        val appLinkIntent: Intent = activity.intent
        val appLinkAction: String? = appLinkIntent.action
        val appLinkData: Uri? = appLinkIntent.data
        //XLog.info(TAG,"appLink action:${appLinkAction},appLinkData:${appLinkData}")
        ProtocolHandleManager.handleProtocol(activity, appLinkData)
        //endregion
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent()
    }
}