package com.twl.meeboss.base.foundation.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.twl.meeboss.base.model.LoadingBean

open class FoundationViewModel : ViewModel() {


    private val _loading = MutableLiveData<LoadingBean>()

    val loading: MutableLiveData<LoadingBean>
        get() = _loading

    /**
     * 显示loading dialog
     * 可以在子线程调用
     */
    fun showLoadingDialog(cancelable: Boolean = true) {
        _loading.postValue(LoadingBean.show(cancelable))
    }

    /**
     * 隐藏loading dialog
     * 可以在子线程调用
     */
    fun dismissLoadingDialog() {
        _loading.postValue(LoadingBean.dismiss())
    }

}