package com.twl.meeboss.base.components.dialog

import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import com.twl.meeboss.base.components.dialog.strategy.DialogStrategyFactory
import com.twl.meeboss.base.components.dialog.strategy.IDialogStrategy
import com.twl.meeboss.base.model.OptionBean

class DialogHelper(val activity: FragmentActivity) {

    private val singleChoiceCallback = mutableMapOf<CommonDataType, IDialogStrategy?>()
    private val multiChoiceCallback = mutableMapOf<CommonDataType, IDialogStrategy?>()

    private val mViewModel: CommonDataViewModel by lazy {
        ViewModelProvider(activity)[CommonDataViewModel::class.java]
    }

    init {
        mViewModel.commonData.observe(activity) {
            it?.let {
                val singleCallback = singleChoiceCallback[it.type]
                if (singleCallback == null) {
                    multiChoiceCallback[it.type]?.showDialog(it.data)
                } else {
                    singleCallback.showDialog(it.data)
                }
                singleChoiceCallback[it.type] = null
                multiChoiceCallback[it.type] = null
            }
        }
    }

    /**
     * 显示单选对话框
     */
    fun showSingleChoiceDialog(type: CommonDataType,
                               defaultValue: String,
                               callback: (OptionBean) -> Unit) {
        singleChoiceCallback[type] = DialogStrategyFactory.createSingleChoiceDialogStrategy(type, activity, defaultValue, callback)
        mViewModel.getCommonDataByType(type)
    }

    /**
     * 显示多选对话框
     * @param exclusiveItem 互斥的item的字符串，优先使用
     * @param exclusiveItemCode 互斥的item的code，如果exclusiveItem为空，且这个code不为0，使用这个
     */
    fun showMultiChoiceDialog(type: CommonDataType,
                              defaultValue: List<String>,
                              exclusiveItem: String? = "",
                              exclusiveItemCode: Long = 0,
                              hasEnable: Boolean = true,
                              callback: (List<OptionBean>) -> Unit) {
        multiChoiceCallback[type] = DialogStrategyFactory.createMultiChoiceDialogStrategy(type, activity, defaultValue, exclusiveItem, exclusiveItemCode, hasEnable, callback)
        mViewModel.getCommonDataByType(type)
    }
}
