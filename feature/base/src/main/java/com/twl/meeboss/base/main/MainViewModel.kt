package com.twl.meeboss.base.main

import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class MainViewModel @Inject constructor(
    //private val repos: BossJobRepository
) : BaseMviViewModel<MainViewUiState, MainViewUiIntent>() {
    var currentTabIndex = 0

    override fun initUiState(): MainViewUiState = MainViewUiState()

    override fun handleIntent(intent: IUiIntent) {

    }

}

data class MainViewUiState(val canSave: Boolean = false,
) : IUiState

sealed class MainViewUiIntent : IUiIntent {
    data object Save : MainViewUiIntent()
}
