package com.twl.meeboss.base.main.router

import androidx.compose.runtime.Composable
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import com.twl.meeboss.base.model.boss.BossInfo
import com.twl.meeboss.base.model.job.JobSimpleDetailResult
import com.twl.meeboss.base.mudule.BaseModule
import com.twl.meeboss.core.network.HttpResult

/**
 * @author: 冯智健
 * @date: 2024年07月15日 16:01
 * @description:
 */
interface IBaseBossService {
    fun getBossModule(): BaseModule
    fun getMainFragment(tabIndex: Int): Fragment

    fun getBossUserInfo(): LiveData<BossInfo?>

    suspend fun getJobSimpleDetail(jobId: String): HttpResult<JobSimpleDetailResult>

}