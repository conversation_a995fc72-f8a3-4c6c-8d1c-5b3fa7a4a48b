package com.twl.meeboss.base.apm;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.techwolf.lib.tlog.TLog;
import com.twl.meeboss.common.utils.ProcessHelper;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * Created by monch on 2017/7/18.
 */

public class ActivityThreadHook {

    private static final String TAG = "ActivityThreadHook";
    private static Handler mH = new Handler(Looper.getMainLooper());

    public static void hookActivityThread() {
        mH.postAtFrontOfQueue(new MonitorRunnable());
    }

    private static class MonitorRunnable implements Runnable {
        @Override
        public void run() {
            try {
                Log.i(TAG, "In loop.");
                Looper.loop();
                Log.i(TAG, "Loop error.");
            } catch (Throwable t) {
                TLog.error(TAG, t, "Receiver error.");
                if (isIgnoreThrowable(t)) {
                    Log.i(TAG, "Retry loop.");
                    hookActivityThread();
                } else {
                    Log.i(TAG, "throw error.", t);
                    report(t);
                    throw t;
                }
            }
        }
    }

    private static final List<String> MMS_IGNORES = Arrays.asList("notification", "MMSReceiver", "onGetTokenComplete");
    private static final List<String> MAIN_IGNORES = Arrays.asList(
            "android.widget.Toast$TN",
            "io.rong.imlib.ConnectChangeReceiver",
            "android.hardware.SystemSensorManager$SensorEventQueue.dispatchSensorEvent_",
            "com.tencent.wcdb.database.SQLiteConnection.nativePrepareStatement",
            "android.widget.Editor$ActionPinnedPopupWindow.computeLocalPosition",
            "android.view.ViewGroup.resetCancelNextUpFlag",
            "android.widget.TextView.canPasteAsPlainText",
            "android.app.RemoteServiceException$CannotDeliverBroadcastException",
            "can't deliver broadcast",
            "Activity client record must not be null to execute transaction item"
    );


    private static boolean isIgnoreThrowable(Throwable t) {
        boolean ignore = false;
        try {
            final String string = Log.getStackTraceString(t);
            if (string != null) {
                List<String> ignoreList = null;
                if (ProcessHelper.isMainProcess()) {
                    ignoreList = MAIN_IGNORES;
                }
                ignore = isIgnore(string, ignoreList);
                if (ignore) { //忽略的异常上报到bugly 的主动异常
                    TLog.info(TAG, "%s", string);
                    report(t);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ignore;
    }

    private static void report(Throwable t){
        if (t == null) {
            return;
        }
        final String string = Log.getStackTraceString(t);
        ApmManager.INSTANCE.apmReport(ApmAction.APP_CRASH, "", new Function1<JSONObject, Unit>() {
            @Override
            public Unit invoke(JSONObject jsonObject) {
                try {
                    jsonObject.put("stack", string);
                    jsonObject.put("exception",t.getMessage());
                    jsonObject.put("type", t.getCause());
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                }
                return null;
            }
        });
    }

    private static boolean isIgnore(String stacks, List<String> ignoreList){
        if (ignoreList != null) {
            for (String s : ignoreList) {
                if (stacks.contains(s)) {
                    return true;
                }
            }
        }
        return false;
    }
}
