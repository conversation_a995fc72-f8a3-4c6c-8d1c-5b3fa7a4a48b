package com.twl.meeboss.base.model.boss

import com.twl.meeboss.base.model.BaseEntity

data class BossAccountInfo(
    val completeStatus: Int = 0, //完善状态
    val firstCompleteStatus: Int = 0, //是否首善   0 没首善，1 已经首善
    val certStatus: Int = 0,//'公司认证状态 0未认证、1认证通过、2认证失败',3审核中
    val certTitle: String = "",//公司认证状态异常时展示的标题
    val certDesc: String = "",//公司认证状态异常时展示的描述
    val companyName: String? = null,
    val position: String? = null,
    val workEmail: String? = null,
    val activeJobCount: Int? = null,
    val appliedCandidateCount: Int? = null,
    val myFavoriteJobSeekerCount: Int? = null
) : BaseEntity