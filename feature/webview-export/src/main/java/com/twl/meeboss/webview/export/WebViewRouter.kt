package com.twl.meeboss.webview.export

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.sankuai.waimai.router.Router

object WebViewRouter : IWebViewInterface {
    @Composable
    override fun DefaultWebViewComponent(modifier: Modifier?, url: String) {
        Router.getService(IWebViewInterface::class.java, WebViewRouterPath.WEBVIEW_SERVICE)
            ?.DefaultWebViewComponent(modifier = modifier, url = url)
    }
}