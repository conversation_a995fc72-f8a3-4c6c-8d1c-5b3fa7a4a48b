package com.twl.meeboss.webview.export

import com.twl.meeboss.base.config.UrlConfig
import com.twl.meeboss.core.network.config.HttpConfigManager
import java.net.URLEncoder

class WebUrls {

    companion object{

        fun String.toFullUrl(): String {
            return "${HttpConfigManager.getNetEnvironment().baseWebUrl}$this"
        }

        fun getRobotCheckUrl(): String {
            return "turnstile-verify-v2".toFullUrl()
        }

        fun getProtocolH5Url(@ProtocolId id:String): String {
            return  "${UrlConfig.H5_PROTOCOL}/${id}".toFullUrl()
        }

        fun getDeleteAccountUrl(): String {
            return UrlConfig.H5_DELETE_COUNT.toFullUrl()
        }

        fun String?.getPreviewResumeUrl(fileName:String? = null,language:String? = "en"):String {
            if (isNullOrBlank()) {
                return ""
            }

            var resultUrl = "${UrlConfig.H5_PREVIEW_URL}?previewUrl=" + URLEncoder.encode(this, "UTF-8")
            if (!fileName.isNullOrBlank()) {
                resultUrl += "&fileName=${URLEncoder.encode(fileName, "UTF-8")}"
            }
            if (!language.isNullOrBlank()) {
                resultUrl += "&lang=$language"
            }
            return resultUrl.toFullUrl()
        }
        fun getImprintUrl():String{
            return UrlConfig.H5_IMPRINT.toFullUrl()
        }

    }
}