package com.twl.meeboss.webview.export

import androidx.annotation.StringDef

@Target(AnnotationTarget.TYPE, AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.SOURCE)
@StringDef( value = [
    ProtocolId.TERMS_OF_SERVICE,
    ProtocolId.PRIVACY_POLICY
    ]
)
annotation class ProtocolId {
    companion object {
        const val TERMS_OF_SERVICE = "terms"
        const val PRIVACY_POLICY = "privacy"
        const val JOB_SEEKER_GUIDE = "job_seeker_guide"
        const val EMPLOYER_GUIDE = "employer_guide"

    }
}