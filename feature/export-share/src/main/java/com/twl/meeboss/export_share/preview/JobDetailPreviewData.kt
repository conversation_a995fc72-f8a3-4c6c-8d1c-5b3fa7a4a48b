package com.twl.meeboss.export_share.preview

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.twl.meeboss.base.model.IndexBean
import com.twl.meeboss.base.model.common.CommonTypeBean
import com.twl.meeboss.export_share.model.JobDetailBizInfo
import com.twl.meeboss.export_share.model.JobDetailBossInfo
import com.twl.meeboss.export_share.model.JobDetailCompanyInfo
import com.twl.meeboss.export_share.model.JobDetailJobInfo
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.export_share.topmatches.TopHighlyMatchedDetailModel
import java.util.UUID
import kotlin.random.Random

/**
 * @author: 冯智健
 * @date: 2024年07月05日 19:47
 * @description:
 */
object JobPreviewPreviewData {
    val jobDetail = JobDetailResult(
        jobDetailBizInfo = JobDetailBizInfo(
            favoriteJob = 1
        ),
        jobDetailJobInfo = JobDetailJobInfo(
            jobId = UUID.randomUUID().toString(),
            jobTitle = "Product Manager Product Manager Product Manager Product Manager Product Manager Product Manager Product Manager Product Manager Product Manager",
            jobCode = UUID.randomUUID().toString(),
            jobType = List(10) {
                CommonTypeBean(Random.nextLong(), "Part-time")
            },
            locationType = CommonTypeBean(Random.nextLong(), "On-site"),
            salaryType = CommonTypeBean(Random.nextLong(), "per year"),
            minSalary = 1200000000000000000,
            maxSalary = 2000000000000000000,
            salaryDesc = "$9000000000-$9000000000 per year",
            salaryShortDesc = "$9000000000-$9000000000 per year",
            eduLevel = CommonTypeBean(Random.nextLong(), "High school or equivalent"),
            expLevel = List(3) {
                CommonTypeBean(Random.nextLong(), "Expert and leadership (9+years) ")
            },
            skills = List(10) {
                CommonTypeBean(Random.nextLong(), "Java")
            },
            jobDesc = "The Role OfferToday is looking for an outstanding User Experience Designer to " +
                    "design the future of customer experience. The UX Designer will be responsible " +
                    "for researching, designing, innovating, and prototyping new user experiences " +
                    "that pave the way for the future of energy & vehicles in a way that has never " +
                    "been done before.The Digital Experience Team ",
            address = List(10) {
                CommonTypeBean(Random.nextLong(), "Causeway Bay")
            },
            jobStatus = 10,
            languages = listOf(IndexBean(1, "English"), IndexBean(2, "Chinese")),
            visaSponsored = 1,
        ),
        jobDetailBossInfo = JobDetailBossInfo(
            avatar = "https://img2.baidu.com/it/u=1459566101,1482467068&fm=253&fmt=auto&app=138&f=JPEG?w=400&h=400",
            name = "Shirley Wong",
            bossPosition = "Product lead",
            bossIntroduce = "Tencent · Product lead"
        ),
        jobDetailCompanyInfo = JobDetailCompanyInfo(
            comId = UUID.randomUUID().toString(),
            name = "Motor Vehicle Manufacturing Motor Company Manufacturing Motor Company",
            shortName = "Tesla Tesla Tesla Tesla Tesla Tesla Tesla Tesla Tesla Tesla Tesla Tesla Tesla",
            logo = "https://img2.baidu.com/it/u=1459566101,1482467068&fm=253&fmt=auto&app=138&f=JPEG?w=400&h=400",
            description = "Our mission is to accelerate the world’s transition to sustainable energy. " +
                    "With global temperatures rising, the faster we free Our mission is to accelerate " +
                    "the world’s transition to sustainable energy. With global temperatures rising",
            industryName = "game",
            sizeType = CommonTypeBean(code = 10000, name = "20-200人")
        )
    )
}

class JobDetailPreviewParameterProvider : PreviewParameterProvider<JobDetailResult> {
    override val values: Sequence<JobDetailResult> = sequenceOf(
        JobPreviewPreviewData.jobDetail
    )
}

class TopMatchesPreviewParameterProvider : PreviewParameterProvider<Pair<TopHighlyMatchedDetailModel, JobDetailResult>>{
    override val values: Sequence<Pair<TopHighlyMatchedDetailModel, JobDetailResult>> = sequenceOf(
        TopHighlyMatchedDetailModel("", "1.5+ years of product management experience in mobile app development, aligned with the job’s core focus.  2. Led cross-functional teams and launched features with >1M users—matches the scale and impact expected.")
        to JobPreviewPreviewData.jobDetail
    )
}