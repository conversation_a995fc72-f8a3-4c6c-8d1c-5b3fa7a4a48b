package com.twl.meeboss.export_share.topmatches

/**
 * @author: musa on 2025/05/08
 * @e-mail: yangpeng<PERSON>@kanzhun.com
 * @desc: 高度匹配展示信息domain层实体
 */
data class TopHighlyMatchedModel(
    val showTopMatchs: <PERSON><PERSON><PERSON>,
    val showRedDot: Boolean,
    val icons: List<String>,
    val title: String,
    val subTitle: String,
)

data class TopHighlyMatchedDetailModel(
    val securityId: String,
    val reason: String,
)