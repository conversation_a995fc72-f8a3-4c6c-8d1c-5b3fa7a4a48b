<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/base_bg_popup_window"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clCopyLink"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:paddingHorizontal="10dp"
        android:paddingTop="2dp"
        android:paddingBottom="2dp"
        >
        <ImageView
            android:id="@+id/ivCopyLink"
            android:layout_width="20dp"
            android:layout_height="20dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@drawable/base_icon_more_copy_link"
            />

        <TextView
            android:id="@+id/tvCopyLink"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@id/ivCopyLink"
            app:layout_constraintTop_toTopOf="@id/ivCopyLink"
            app:layout_constraintBottom_toBottomOf="@id/ivCopyLink"
            android:layout_marginStart="8dp"
            android:text="@string/common_share_job_copy_link"
            android:textSize="14sp"
            android:textColor="#222222"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>