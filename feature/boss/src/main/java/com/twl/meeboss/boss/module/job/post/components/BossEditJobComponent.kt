package com.twl.meeboss.boss.module.job.post.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.twl.meeboss.boss.module.job.model.JobEditItem
import com.twl.meeboss.core.ui.utils.noRippleClickable
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

@Composable
fun BossEditJobComponent(modifier: Modifier = Modifier,
                         header: @Composable () -> Unit = {},
                         buttons: @Composable () -> Unit = {},
                         onScroll:(Int)->Unit = {},
                         list: List<JobEditItem>,
                         onClick: (JobEditItem) -> Unit = {},
                         onDeleteClick: (JobEditItem) -> Unit = {}) {
    val scrollState = rememberScrollState()
    LaunchedEffect(key1 = scrollState) {
        snapshotFlow{scrollState.value}.collectLatest {
            onScroll(it)
        }
    }
    Column(modifier = modifier) {
        Column(modifier = Modifier
            .weight(1F)
            .fillMaxWidth()
            .verticalScroll(scrollState)) {
            header()
            val scope = rememberCoroutineScope()
            var preventSplitClick by remember { mutableStateOf(false) }
            list.forEach { item ->
                BossEditJobItem(
                    modifier = Modifier.noRippleClickable {
                        if (item.canEdit) {
                            if (!preventSplitClick) {
                                preventSplitClick = true
                                onClick(item)
                                scope.launch {
                                    delay(1000L)
                                    preventSplitClick = false
                                }
                            }
                        }
                    }, item.title, item.content ?: "", item.optional, item.placeHolder, item.canEdit, item.canDelete,
                    onDeleteClick = {
                        onDeleteClick(item)
                    })
            }

        }
        Spacer(modifier = Modifier.padding(top = 16.dp))
        buttons()
    }

}

@Preview
@Composable
private fun PreviewBossEditJobComponent(@PreviewParameter(EditJobPreviewParameterProvider::class) data: BossEditJobPreviewInfo) {
    Column(modifier = Modifier
        .fillMaxSize()
        .background(Color.White)) {
        BossEditJobComponent(modifier = Modifier.padding(16.dp, 0.dp), list = data.list)
    }
}


class EditJobPreviewParameterProvider : PreviewParameterProvider<BossEditJobPreviewInfo> {
    val list = mutableListOf(JobEditItem(title = "JobTitle", placeHolder = "Edit job title"),
        JobEditItem(title = "Visa sponsorship (Optional)", placeHolder = "Do you provide visa sponsorship", content = "No"),
        JobEditItem(title = "Employment type", placeHolder = "Select employment type"),
        JobEditItem(title = "Workplace type", placeHolder = "Select workplace type"),
        JobEditItem(title = "Job location", placeHolder = "Enter job location"),
        JobEditItem(title = "Pay", placeHolder = "Enter pay"),
        JobEditItem(title = "Job description", placeHolder = "Enter job description"),
        JobEditItem(title = "Minimum education level (Optional)", placeHolder = "Select minimum education level"),
        JobEditItem(title = "Experience level (Optional)", placeHolder = "Select experience level"),
        JobEditItem(title = "Job skills (Optional)", placeHolder = "Select job skills"),
        JobEditItem(title = "Language (Optional)", placeHolder = "Select language")
    )
    override val values = sequenceOf(
        BossEditJobPreviewInfo(list = list)

    )
}

data class BossEditJobPreviewInfo(val list: List<JobEditItem>)

