package com.twl.meeboss.boss.module.candidates.detail.dialog

import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.foundation.viewmodel.BaseViewModel
import com.twl.meeboss.base.ktx.getDefaultPage
import com.twl.meeboss.base.ktx.getDefaultPageSize
import com.twl.meeboss.boss.module.job.manager.model.JobListItemResult
import com.twl.meeboss.boss.repos.BossJobRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2025/3/4
 * description:
 */
@HiltViewModel
class BossCandidateResumeJobSelectViewModel @Inject constructor(
    private val jobRepo: BossJobRepository
) : BaseViewModel() {
    var currentPage = getDefaultPage()
    var friendId = ""

    val jobs: MutableLiveData<XRefreshListState<JobListItemResult>> = MutableLiveData(
        XRefreshListState.getDefault<JobListItemResult>().copy(
            loadFinishText = ""
        )
    )

    fun requestJobList(isRefresh: Boolean = true) {
        val page = if (isRefresh) {
            getDefaultPage()
        } else {
            currentPage + 1
        }
        async {
            val result = jobRepo.getTalentJobList(
                friendId = friendId,
                page = page,
                pageSize = getDefaultPageSize()
            )
            val old = jobs.value ?: XRefreshListState.getDefault()
            if (result.isSuccess) {
                result.getOrNull()?.run {
                    if (isRefresh) {
                        jobs.postValue(
                            old.refreshSuccess(
                                list = content ?: emptyList(),
                                hasMore = hasMore ?: false
                            )
                        )
                    } else {
                        jobs.postValue(
                            old.loadMoreSuccess(
                                list = content ?: emptyList(),
                                hasMore = hasMore ?: false
                            )
                        )
                    }
                }
                currentPage = page
            } else {
                if (isRefresh) {
                    jobs.postValue(
                        old.refreshFail("")
                    )
                } else {
                    jobs.postValue(
                        old.loadMoreFail()
                    )
                }
            }
        }
    }
}
