package com.twl.meeboss.boss.module.candidates.detail.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.constants.DefaultValueConstants.Companion.PRESENT
import com.twl.meeboss.base.ktx.getJoinString
import com.twl.meeboss.boss.R
import com.twl.meeboss.export_share.model.CandidateResumeEduExp
import com.twl.meeboss.boss.module.candidates.detail.preview.CandidateResumePreviewData
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.utils.withPlaceHolder

/**
 * @author: 冯智健
 * @date: 2024年07月22日 09:39
 * @description:
 */
@Composable
fun CandidateResumeEduExpCard(candidateResumeEduExpList: List<CandidateResumeEduExp>) {
    Text(
        modifier = Modifier.padding(top = 28.dp, bottom = 20.dp),
        text = stringResource(id = R.string.common_education),
        color = Black222222,
        fontSize = 22.sp,
        fontWeight = FontWeight.Medium
    )
    Column {
        candidateResumeEduExpList.forEach { item ->
            Column(modifier = Modifier.padding(bottom = 28.dp)) {
                Text(
                    text = item.schoolName.withPlaceHolder(id = R.string.geek_no_institute_info),
                    color = Black222222,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.size(7.dp))
                Text(
                    text = getJoinString(listOf(item.eduLevelDesc, item.majorName), " · ") ?: "",
                    color = Black484848,
                    fontSize = 13.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                getEduExpTimeString(item).takeIf { it.isNotBlank() }?.let {
                    Text(
                        modifier = Modifier.padding(top = 12.dp),
                        text = it,
                        color = Black888888,
                        fontSize = 12.sp
                    )
                }

                item.description?.takeIf { it.isNotBlank() }?.let {
                    Text(
                        modifier = Modifier.padding(top = 12.dp),
                        text = it,
                        color = Black484848,
                        fontSize = 14.sp
                    )
                }
            }
        }
    }
}

@Composable
@ReadOnlyComposable
private fun getEduExpTimeString(candidateResumeEduExp: CandidateResumeEduExp): String {
    if (candidateResumeEduExp.startYear == 0 && candidateResumeEduExp.endYear == 0) {
        return ""
    }
    val start = "${candidateResumeEduExp.startYear}"
    val end = if (candidateResumeEduExp.endYear == PRESENT) {
        val resources = LocalContext.current.resources
        resources.getString(R.string.common_present)
    } else {
        "${candidateResumeEduExp.endYear}"
    }
    return "$start - $end"
}

@Preview
@Composable
private fun PreviewCandidateResumeEduExpCard() {
    Column(
        modifier = Modifier
            .background(Color.White)
            .padding(16.dp)
    ) {
        CandidateResumePreviewData.candidateResume.eduExpList?.mapNotNull { it }?.let {
            CandidateResumeEduExpCard(it)
        }
    }
}