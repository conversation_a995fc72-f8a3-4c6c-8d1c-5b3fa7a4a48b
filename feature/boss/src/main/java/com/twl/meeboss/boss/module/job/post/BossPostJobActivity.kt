package com.twl.meeboss.boss.module.job.post

import android.content.Intent
import androidx.activity.result.ActivityResult
import androidx.activity.viewModels
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.constants.BUNDLE_OBJECT
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.manager.ActivityResultManager
import com.twl.meeboss.base.manager.IActivityResult
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.constant.BossConstants.BOSS_POST_JOB
import com.twl.meeboss.boss.export.BossEventBusKey
import com.twl.meeboss.boss.export.BossRouterPath
import com.twl.meeboss.boss.model.JobParseResult
import com.twl.meeboss.boss.module.complete.ai.BossCompleteChooseType
import com.twl.meeboss.boss.module.complete.job.activity.BossJobGenerateActivity
import com.twl.meeboss.boss.module.complete.job.activity.BossJobTemplateGenerateActivity
import com.twl.meeboss.boss.module.job.model.EditJobLocalData
import com.twl.meeboss.boss.module.job.model.JobEditItem
import com.twl.meeboss.boss.module.job.post.components.BossEditJobComponent
import com.twl.meeboss.boss.module.job.post.components.BossEditJobPreviewInfo
import com.twl.meeboss.boss.module.job.post.components.EditJobPreviewParameterProvider
import com.twl.meeboss.boss.module.job.post.dialog.JobQuickPostBottomSheet
import com.twl.meeboss.boss.module.job.post.utils.BossEditJobInteractHelper
import com.twl.meeboss.boss.module.job.post.utils.BossEditJobSuccessHelper
import com.twl.meeboss.boss.module.job.post.viewmodel.BossPostJobViewModel
import com.twl.meeboss.boss.module.job.post.viewmodel.BossPostUiIntent
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.dialog.showConfirmDialog
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.theme.COLOR_DDDDDD
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.AndroidEntryPoint

@RouterPage(path = [BossRouterPath.BOSS_POST_JOB_PAGE])
@AndroidEntryPoint
class BossPostJobActivity() : BaseMviActivity<BossPostJobViewModel>() {

    override val viewModel: BossPostJobViewModel by viewModels()

    private val REQ_GENERATE_JOB = 1001
    private val activityResultManager by lazy {
        ActivityResultManager(this)
    }

    override fun preInit(intent: Intent) {
        activityResultManager.register(REQ_GENERATE_JOB)
    }

    private val editInteract by lazy {
        BossEditJobInteractHelper(this, BOSS_POST_JOB, {
            viewModel.uiStateFlow.value.list
        }, {
            viewModel.uiStateFlow.value.localStorage
        }) { list, localStorage ->
            viewModel.sendUiIntent(BossPostUiIntent.UpdateList(list, localStorage))
        }
    }


    override fun initData() {
        viewModel.sendUiIntent(BossPostUiIntent.GetDraft)
        viewModel.showDraftDialog.observe(this) {
            it?.run {
                showApplyDraftDialog(it)
            }
        }
        liveEventBusObserve(BossEventBusKey.POST_JOB_SUCCESS) { _: String ->
            finish()
        }
    }

    @Composable
    override fun ComposeContent() {
        val viewModel: BossPostJobViewModel = viewModel()
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        if (uiState.isPostSuccess) {
            finish()
            BossEditJobSuccessHelper.handJobPostResult(this, uiState.jobStatus, uiState.jobId)
        }
        if (uiState.showLoading) {
            showLoadingDialog()
        } else {
            dismissLoadingDialog()
        }
        BossPostJobContent(
            uiState.list,
            uiState.canSave,
            onClick = this::onClickItem,
            onDeleteClick = this::onDeleteClick,
            onClickQuickPost = { onClickQuickPost(uiState.showTemplate) },
            onClickSave = this::onClickSave
        )
    }

    private fun onClickQuickPost(showTemplate:Boolean) {
        BossPointReporter.quickPostJobClick()
        val hasFillItem = viewModel.uiStateFlow.value.list.indexOfFirst { !it.content.isNullOrEmpty() } != -1
        JobQuickPostBottomSheet.newInstance(showTemplate){
            BossPointReporter.postJobQuickPostPath(
                if (it != BossCompleteChooseType.TYPE_TEMPLATE) {
                    it + 1
                } else {
                    it
                }
            )
            if(it == BossCompleteChooseType.TYPE_TEMPLATE){
                BossJobTemplateGenerateActivity.intent(context = this,hasFillItem = hasFillItem)
            }else{
                BossPointReporter.postJobPathNext(it)
                activityResultManager.startActivityForResult(REQ_GENERATE_JOB,object :IActivityResult {
                    override fun getIntent(): Intent {
                        return BossJobGenerateActivity.getIntent(this@BossPostJobActivity, it, hasFillItem)
                    }

                    override fun sendCallback(result: ActivityResult) {
                        if (result.resultCode == RESULT_OK && result.data != null) {
                            val tempResult = result.data?.getSerializableExtra(BUNDLE_OBJECT)
                            if (tempResult is JobParseResult) {
                                viewModel.sendUiIntent(BossPostUiIntent.OnGetJobParseResult(tempResult))
                            }
                        }
                    }

                })
            }
        }.showSafely(this)
    }

    private fun onClickItem(item: JobEditItem) {
        editInteract.onItemClick(item)
    }

    private fun onDeleteClick(item: JobEditItem) {
        editInteract.onDeleteItem(item)
    }

    private fun onClickSave() {
        viewModel.sendUiIntent(BossPostUiIntent.Save)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        editInteract.onActivityResult(requestCode, resultCode, data)
    }

    override fun onBackPressed() {
        if (viewModel.needSaveDraft()) {
            viewModel.sendUiIntent(BossPostUiIntent.SaveDraft)
        }
        super.onBackPressed()

    }

    private fun showApplyDraftDialog(draft: EditJobLocalData) {
        showConfirmDialog(title = R.string.boss_use_previous_unsaved_content.toResourceString(),
            onConfirm = {
                viewModel.sendUiIntent(BossPostUiIntent.ApplyDraft(draft))
            }, onCancel = {
                //取消不清草稿
                //viewModel.sendUiIntent(BossPostUiIntent.DeleteDraft)
            })
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        intent.extras?.get(BUNDLE_OBJECT)?.let {
            if(it is EditJobLocalData){
                viewModel.sendUiIntent(BossPostUiIntent.OnGetEditJobLocalData(it))
            }
        }
    }
}

@Composable
fun BossPostJobContent(
    list: List<JobEditItem>,
    canSave: Boolean,
    onClick: (JobEditItem) -> Unit = {},
    onDeleteClick: (JobEditItem) -> Unit = {},
    onClickQuickPost: () -> Unit = {},
    onClickSave: () -> Unit = {}
) {
    XTheme {
        Column(modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
        ) {
            XTitleBar()
            Box {
                BossEditJobComponent(modifier = Modifier.padding(horizontal = 16.dp), list = list,
                    onClick = onClick,
                    onDeleteClick = onDeleteClick,
                    header = {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                        ) {
                            Text(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(0.dp, 0.dp),
                                text = stringResource(id = R.string.job_post_a_job),
                                fontSize = 28.sp,
                                color = Black222222,
                                fontWeight = FontWeight.SemiBold
                            )

                            Spacer(modifier = Modifier.height(12.dp))

                            Row(
                                modifier = Modifier
                                    .border(
                                        color = COLOR_DDDDDD,
                                        width = 1.dp,
                                        shape = RoundedCornerShape(42.dp),
                                    )
                                    .padding(16.dp, 10.dp)
                                    .noRippleClickable(onClick = onClickQuickPost)
                            ) {
                                Image(
                                    painter = painterResource(id = R.mipmap.boss_icon_quick_post_job),
                                    contentDescription = "",
                                    modifier = Modifier
                                        .size(20.dp)
                                )

                                Text(
                                    modifier = Modifier
                                        .padding(start = 10.dp)
                                        .align(Alignment.CenterVertically),
                                    text = stringResource(id = R.string.boss_post_job_ai_quick_post),
                                    style = TextStyle(
                                        fontSize = 14.sp,
                                        lineHeight = 24.sp,
                                        fontWeight = FontWeight.W500,
                                        color = COLOR_484848,
                                    ),
                                )
                            }
                        }
                    }, buttons = {
                        XCommonButton(
                            modifier = Modifier.padding(bottom = 16.dp),
                            onClick = onClickSave,
                            text = stringResource(id = R.string.boss_register_post_first_job_button),
                            enabled = canSave,
                            canClickWhenUnable = true
                        )
                    })

            }

        }
    }
}


@Preview(locale="en")
@Composable
fun PreviewEditJobContent1(@PreviewParameter(EditJobPreviewParameterProvider::class) data: BossEditJobPreviewInfo) {
    BossPostJobContent(list = data.list,
        canSave = true)
}