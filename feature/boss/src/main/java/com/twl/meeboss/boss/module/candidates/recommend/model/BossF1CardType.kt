package com.twl.meeboss.boss.module.candidates.recommend.model

import androidx.annotation.IntDef

@Target(AnnotationTarget.TYPE, AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.SOURCE)
@IntDef(flag = true, value = [
    BossF1CardType.CANDIDATE,
    BossF1CardType.BEGINNER_GUIDE_CARD,
    BossF1CardType.RECOMMEND,
    BossF1CardType.MARK,
    BossF1CardType.NOTIFICATION,

])
annotation class BossF1CardType {
    companion object {
        // 职位卡片
        const val CANDIDATE = 0
        // 新手指引卡片
        const val BEGINNER_GUIDE_CARD = 1
        // recommend
        const val RECOMMEND = 2
        // mark
        const val MARK = 3
        // notification
        const val NOTIFICATION = 4
    }
}