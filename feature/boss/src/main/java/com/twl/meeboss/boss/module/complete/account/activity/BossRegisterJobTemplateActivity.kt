package com.twl.meeboss.boss.module.complete.account.activity

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.constants.BUNDLE_STRING
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.main.router.BaseServiceRouter
import com.twl.meeboss.base.model.job.JobTemplateBean
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.boss.export.BossRouterPath
import com.twl.meeboss.boss.module.complete.account.components.BossRegisterJobTemplatePage
import com.twl.meeboss.boss.module.complete.account.preview.JobTemplateListPreviewParameterProvider
import com.twl.meeboss.boss.module.complete.account.viewmodel.BossRegisterJobTemplateEvent
import com.twl.meeboss.boss.module.complete.account.viewmodel.BossRegisterJobTemplateUiIntent
import com.twl.meeboss.boss.module.complete.account.viewmodel.BossRegisterJobTemplateUiState
import com.twl.meeboss.boss.module.complete.account.viewmodel.BossRegisterJobTemplateViewModel
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.common.utils.T
import dagger.hilt.android.AndroidEntryPoint


@RouterPage(path = [BossRouterPath.BOSS_REGISTER_JOB_TEMPLATE_PAGE])
@AndroidEntryPoint
class BossRegisterJobTemplateActivity() : BaseMviActivity<BossRegisterJobTemplateViewModel>() {
    override val viewModel: BossRegisterJobTemplateViewModel by viewModels()
    val companyId by lazy {
        intent.getStringExtra(BUNDLE_STRING)
    }

    override fun preInit(intent: Intent) {
    }

    override fun initData() {
        viewModel.sendUiIntent(BossRegisterJobTemplateUiIntent.GetTempLateList(companyId))
    }

    override fun onBackPressed() {
        super.onBackPressed()
        BaseServiceRouter.afterLogin(this)
    }

    @Composable
    override fun ComposeContent() {
        LaunchedEffect(Unit) {
            viewModel.eventFlow.collect{
                when (it) {
                    is BossRegisterJobTemplateEvent.FinishPost -> {
                        BaseServiceRouter.afterLogin(this@BossRegisterJobTemplateActivity)
                        T.ss(if (it.postResultBeans.size > 1) R.string.employer_post_job_successfully_toast else R.string.employer_post_one_job_successfully_toast)
                    }
                }
            }
        }

        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        BossRegisterJobTemplatePage(
            uiState = uiState,
            onPatchPost = {
                viewModel.sendUiIntent(BossRegisterJobTemplateUiIntent.BatchPostIntent)
                BossPointReporter.jobTemplatePostJobClickOnboarding(scene = "1", templateIds = uiState.selectedTemplateIds)
            },
            onSelectItem = {
                viewModel.sendUiIntent(BossRegisterJobTemplateUiIntent.SelectTheItemIntent(it))
            },
            onItemClick = { templateId ->
                BossPointReporter.registerTemplateClick("1", templateId)
                BossPageRouter.jumpToBossRegisterTemplateDetailActivity(this, templateId)
            },
            onClickSkip = {
                BossPointReporter.registerTemplateClick("0",null)
                BaseServiceRouter.afterLogin(this)
                BossPointReporter.jobTemplatePostJobClickOnboarding(scene = "0", templateIds = uiState.selectedTemplateIds)
            }
        )
    }

}

@Preview
@Composable
private fun PreviewBossRegisterJobTemplateContent(@PreviewParameter(
    JobTemplateListPreviewParameterProvider::class) list: List<JobTemplateBean>) {
    BossRegisterJobTemplatePage(
        uiState = BossRegisterJobTemplateUiState(templateList = list)
    )
}
