package com.twl.meeboss.boss.module.me.candidate.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.ktx.getJoinString
import com.twl.meeboss.boss.export.model.CandidateItemResult
import com.twl.meeboss.boss.module.candidates.recommend.preview.CandidatesListPreviewData
import com.twl.meeboss.boss.R
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.GRAY_AAAAAA

@Composable
fun BossMyCandidateCardSummaryArea(item: CandidateItemResult) {
    val textHeightState = remember {
        mutableIntStateOf(0)
    }
    item.workExpList?.mapNotNull{ it }?.let { list ->
        getJoinString(
            listOf(list[0].jobTitle, list[0].companyName),
            "  ·  "
        )?.let {
            Row(
                modifier = Modifier.padding(top = 16.dp)
            ) {
                Icon(
                    modifier = Modifier
                        .padding(end = 8.dp)
                        .widthIn(min = 18.dp)
                        .drawBehind {
                            if (list.size >= 2) {
                                drawLine(
                                    color = GRAY_AAAAAA,
                                    start = Offset(
                                        size.width / 2,
                                        size.height / 2
                                    ),
                                    end = Offset(
                                        size.width / 2,
                                        textHeightState.intValue.toFloat() + 12.dp.toPx()
                                    ),
                                    strokeWidth = 1.dp.toPx(),
                                    pathEffect = PathEffect.dashPathEffect(
                                        floatArrayOf(3.dp.toPx(), 2.dp.toPx()),
                                        0f
                                    )
                                )
                            }
                        },
                    imageVector = ImageVector.vectorResource(id = R.drawable.ui_bag_icon),
                    contentDescription = null,
                    tint = Color.Unspecified
                )
                Text(
                    modifier = Modifier.weight(1F),
                    text = it,
                    color = Black484848,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    onTextLayout = {
                        textHeightState.intValue = it.size.height
                    }
                )
                list[0].timeDesc?.takeIf { it.isNotBlank() }?.let {
                    Text(
                        modifier = Modifier.padding(start = 16.dp),
                        text = it,
                        color = Black888888,
                        fontSize = 12.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
        if (list.size >= 2) {
            getJoinString(
                listOf(list[1].jobTitle, list[1].companyName),
                "  ·  "
            )?.let {
                Row(
                    modifier = Modifier.padding(top = 8.dp),
                ) {
                    Icon(
                        modifier = Modifier
                            .padding(end = 8.dp)
                            .widthIn(min = 18.dp)
                            .drawBehind {
                                if (list.size >= 2) {
                                    drawLine(
                                        color = GRAY_AAAAAA,
                                        start = Offset(
                                            size.width / 2,
                                            size.height / 2
                                        ),
                                        end = Offset(
                                            size.width / 2,
                                            0.dp.toPx()
                                        ),
                                        strokeWidth = 1.dp.toPx()
                                    )
                                }
                            },
                        imageVector = ImageVector.vectorResource(id = R.drawable.ui_concentric_circles_icon),
                        contentDescription = null,
                        tint = Color.Unspecified
                    )
                    Text(
                        modifier = Modifier.weight(1F),
                        text = it,
                        color = Black484848,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                        onTextLayout = {
                            textHeightState.intValue = it.size.height
                        }
                    )
                    list[1].timeDesc?.takeIf { it.isNotBlank() }?.let {
                        Text(
                            modifier = Modifier.padding(start = 16.dp),
                            text = it,
                            color = Black888888,
                            fontSize = 12.sp,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
        }

        
    }
}

@Preview
@Composable
private fun PreviewBossMyCandidateCardSummaryArea() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier.padding(vertical = 20.dp, horizontal = 16.dp)
        ) {
            BossMyCandidateCardSummaryArea(CandidatesListPreviewData.candidateItemResult)
        }
    }
}