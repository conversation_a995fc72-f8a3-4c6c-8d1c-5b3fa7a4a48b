package com.twl.meeboss.boss.module.job.home.dialog

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.boss.R
import com.twl.meeboss.core.ui.component.XDivider
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable

class CloseProductManagerBottomSheet @JvmOverloads constructor(private val onCopyClick: (() -> Unit)? = null) :
    CommonBottomDialogFragment() {

    @Composable
    override fun DialogContent() {
        CloseProductManagerContent(
            onCloseClick = { dismissSafely() },
            onCopyClick = {
                dismissSafely()
                onCopyClick?.invoke()
            }
        )
    }
}

@Preview
@Composable
fun CloseProductManagerContent(
    onCloseClick: () -> Unit = {},
    onCopyClick: () -> Unit = {}
) {
    XTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    Color.White,
                    shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                )
        ) {

            Box(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Icon(
                    painter = painterResource(id = com.twl.meeboss.base.R.drawable.ui_dailog_close),
                    modifier = Modifier
                        .padding(horizontal = 16.dp, vertical = 20.dp)
                        .noRippleClickable { onCloseClick() },
                    contentDescription = null,
                )
                Text(
                    text = "Product Manager",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .padding(vertical = 20.dp)
                        .widthIn(200.dp, 280.dp)
                        .align(Alignment.Center),
                    color = Black222222,
                    textAlign = TextAlign.Center,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.W500
                )
            }

            XDivider()
            CloseItem(
                id = R.drawable.boss_icon_copy,
                content = stringResource(R.string.employer_jobs_active_menu_option2),
                onItemClick = onCopyClick
            )
            Spacer(modifier = Modifier.height(23.dp))
        }
    }
}

@Composable
fun CloseItem(@DrawableRes id: Int, content: String, onItemClick: () -> Unit) {
    Column(
        modifier = Modifier
            .padding(
                horizontal = 16.dp
            )
            .noRippleClickable { onItemClick() }
    ) {
        Row(
            modifier = Modifier.padding(
                vertical = 24.dp
            ),
            verticalAlignment = Alignment.CenterVertically
        ) {

            Image(
                painter = painterResource(id),
                contentDescription = null
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = content,
                style = TextStyle(
                    fontSize = 16.sp,
                    lineHeight = 22.sp,
                    fontWeight = FontWeight(510),
                    color = Color(0xFF222222),
                )
            )
        }
        XDivider()
    }
}