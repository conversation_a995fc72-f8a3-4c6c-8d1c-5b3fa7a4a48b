package com.twl.meeboss.boss.module.candidates.detail.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.constants.DefaultValueConstants.Companion.PRESENT
import com.twl.meeboss.boss.R
import com.twl.meeboss.export_share.model.CandidateResumeProjectExp
import com.twl.meeboss.boss.module.candidates.detail.preview.CandidateResumePreviewData
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import java.text.DecimalFormat

/**
 * @author: 冯智健
 * @date: 2024年07月22日 10:23
 * @description:
 */
@Composable
fun CandidateResumeProjectExpCard(candidateResumeProjectExpList: List<CandidateResumeProjectExp>) {
    Text(
        modifier = Modifier.padding(top = 28.dp),
        text = stringResource(id = R.string.common_project_experience),
        color = Black222222,
        fontSize = 22.sp,
        fontWeight = FontWeight.Medium
    )
    Column(modifier = Modifier.padding(bottom = 12.dp)) {
        candidateResumeProjectExpList.forEach { item ->
            Column {
                Text(
                    modifier = Modifier.padding(top = 20.dp),
                    text = item.projectName ?: "",
                    color = Black222222,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                item.jobTitle?.takeIf { it.isNotBlank() }?.let {
                    Text(
                        modifier = Modifier.padding(top = 4.dp),
                        text = it,
                        color = Black484848,
                        fontSize = 13.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
                Text(
                    modifier = Modifier.padding(top = 12.dp),
                    text = getProjectExpTimeString(item),
                    color = Black888888,
                    fontSize = 12.sp
                )
                item.description?.takeIf { it.isNotBlank() }?.let {
                    Text(
                        modifier = Modifier.padding(top = 12.dp),
                        text = it,
                        color = Black484848,
                        fontSize = 14.sp
                    )
                }
                item.performance?.takeIf { it.isNotBlank() }?.let {
                    Text(
                        modifier = Modifier.padding(top = 12.dp),
                        text = " · " + stringResource(id = R.string.common_performance),
                        color = Black222222,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        modifier = Modifier.padding(top = 12.dp),
                        text = it,
                        color = Black484848,
                        fontSize = 14.sp
                    )
                }
                Box(modifier = Modifier.height(16.dp))
            }
        }
    }

    HorizontalDivider(thickness = 1.dp, color = BlackEBEBEB)
}

@Composable
@ReadOnlyComposable
private fun getProjectExpTimeString(candidateResumeProjectExp: CandidateResumeProjectExp): String {
    val formatter = DecimalFormat("00")
    val start = "${formatter.format(candidateResumeProjectExp.startMonth)}/${candidateResumeProjectExp.startYear}"
    val end = if (candidateResumeProjectExp.endYear == PRESENT) {
        val resources = LocalContext.current.resources
        resources.getString(R.string.common_present)
    } else {
        "${formatter.format(candidateResumeProjectExp.endMonth)}/${candidateResumeProjectExp.endYear}"
    }
    return "$start - $end"
}


@Preview
@Composable
private fun PreviewCandidateResumeProjectExpCard() {
    Column(
        modifier = Modifier
            .background(Color.White)
            .padding(16.dp)
    ) {
        CandidateResumePreviewData.candidateResume.projectExpList?.mapNotNull { it }?.let {
            CandidateResumeProjectExpCard(it)
        }
    }
}