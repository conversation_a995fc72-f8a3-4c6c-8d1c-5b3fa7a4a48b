package com.twl.meeboss.boss.module.candidates.recommend.model

import androidx.annotation.Keep
import com.twl.meeboss.base.model.BaseEntity

/**
 * @author: 冯智健
 * @date: 2024年07月16日 13:34
 * @description:
 */
@Keep
data class JobTitleResult(
    val list: List<JobTitleInfo?>? = null,
//    val waitOpen:List<JobDetailJobInfo>? = null, v1.06.8废弃, 使用guideJobs替代
    val guideJobs:List<GuideJobItem>? = null,
): BaseEntity

@Keep
data class JobTitleInfo(
    val jobId: String? = null,
    val jobTitle: String? = null,
    val salaryDesc: String? = null,
): BaseEntity