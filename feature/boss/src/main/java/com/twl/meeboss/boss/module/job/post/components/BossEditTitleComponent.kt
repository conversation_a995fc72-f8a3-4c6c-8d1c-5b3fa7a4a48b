package com.twl.meeboss.boss.module.job.post.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewmodel.compose.viewModel
import com.twl.meeboss.base.components.SuggestListItem
import com.twl.meeboss.base.foundation.viewmodel.BaseViewModel
import com.twl.meeboss.base.ktx.getHighlightString
import com.twl.meeboss.base.ktx.subString
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.addSearchKeyToLast
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.api.JobApi
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.core.ui.component.XTextField
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.XTheme


@Composable
fun SuggestTitleContent(viewModel: BossEditJobTitleViewModel = viewModel(), defaultValue: HighlightBean = HighlightBean(), onClickClose: () -> Unit = {}, callback: (item: HighlightBean) -> Unit = {}) {
    XTheme {
        LaunchedEffect(key1 = Unit) {
            viewModel.getSuggestJobTitle()
        }
        Column(modifier = Modifier
            .fillMaxSize()
            .background(Color.White, shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))) {
            Image(painter = painterResource(id = R.drawable.ui_dailog_close), contentDescription = "Close dialog",
                modifier = Modifier
                    .padding(16.dp, 20.dp, 0.dp, 0.dp)
                    .clickable {
                        onClickClose()
                    }
                    .size(24.dp))
            Text(
                modifier = Modifier.padding(16.dp, 18.dp),
                text = stringResource(id = R.string.common_job_title),
                style = TextStyle(
                    color = Black222222,
                    fontSize = 28.sp,
                    fontWeight = FontWeight.SemiBold
                ))
            Spacer(modifier = Modifier.height(2.dp))
            val input by viewModel.etInput.observeAsState(TextFieldValue(defaultValue.name, selection = TextRange(defaultValue.name.length)))
            XTextField(modifier = Modifier.padding(16.dp, 0.dp),
                value = input,
                innerTitle = R.string.common_enter_job_title,
                placeHolder = R.string.common_enter_job_title,
                textStyle = TextStyle(fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Black222222
                ),
                showKeyboard = true,
                onValueChange = { v ->
                    v.subString(150, true).let {
                        viewModel.etInput.value = it
                        viewModel.getSuggestJobTitle()
                    }
                })
            val list by viewModel.list.observeAsState(listOf())
            val count = list?.size ?: 0
            LazyColumn {
                itemsIndexed(list!!) { index, item ->
                    SuggestListItem(item.name.getHighlightString(item.highlights), index != count - 1, isAdd = item.isLocalAdd) {
                        viewModel.getSuggestJobTitle()
                        callback(item)
                    }
                }
            }

        }
    }
}

@Preview
@Composable
fun SuggestTitlePagePreview() {
    val mViewModel: BossEditJobTitleViewModel = viewModel<BossEditJobTitleViewModel>().also {
        it.list.value = listOf(HighlightBean(1, "title1"), HighlightBean(2, "title2"))
    }
    SuggestTitleContent(mViewModel) {

    }
}

class BossEditJobTitleViewModel : BaseViewModel() {

    val etInput: MutableLiveData<TextFieldValue> = MutableLiveData()

    var list: MutableLiveData<List<HighlightBean>> = MutableLiveData()

    fun getSuggestJobTitle() {
        async {
            val api = getService(JobApi::class.java)
            val result = api.suggestJobTitle(etInput.value?.text ?: "")
            if (result.isSuccess) {
                list.postValue(result.getOrNull()?.list ?.addSearchKeyToLast(etInput.value?.text.notNull()) ?: listOf())
            } else {
                list.postValue(listOf())
            }
        }
    }

}
