package com.twl.meeboss.boss.module.talentpool

import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.ktx.getDefaultPage
import com.twl.meeboss.base.ktx.getDefaultPageSize
import com.twl.meeboss.boss.export.model.CandidateItemResult
import com.twl.meeboss.boss.repos.BossCandidatesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


/**
 * <AUTHOR>
 * @date 2025/3/3
 * description:
 */
@HiltViewModel
class BossTalentPoolViewModel @Inject constructor(
    private val repo: BossCandidatesRepository
) : BaseMviViewModel<BossTalentPoolUiState, BossTalentPoolUiIntent>() {
    private var currentPage = getDefaultPage()

    init {
        requestListData()
    }

    override fun initUiState() = BossTalentPoolUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossTalentPoolUiIntent.Refresh -> {
                requestListData()
            }

            is BossTalentPoolUiIntent.LoadMore -> {
                requestListData(isRefresh = false)
            }
        }
    }

    private fun requestListData(isRefresh: Boolean = true) {
        val page = if (isRefresh) {
            getDefaultPage()
        } else {
            currentPage + 1
        }
        requestData(enableLoadState = false,
            request = { repo.talentJobSeekerList(page = page, pageSize = getDefaultPageSize()) },
            success = { result ->
                result?.run {
                    if (isRefresh) {
                        sendUiState {
                            copy(
                                pagingData = pagingData.refreshSuccess(
                                    result.content ?: emptyList(), result.hasMore ?: false
                                )
                            )
                        }
                    } else {
                        sendUiState {
                            copy(
                                pagingData = pagingData.loadMoreSuccess(
                                    result.content ?: emptyList(), result.hasMore ?: false
                                )
                            )
                        }
                    }
                    currentPage = page
                }

            },
            fail = {
                if (isRefresh) {
                    sendUiState { copy(pagingData = pagingData.refreshFail(it.message)) }
                } else {
                    sendUiState { copy(pagingData = pagingData.loadMoreFail()) }
                }
            })
    }

}


data class BossTalentPoolUiState(val pagingData: XRefreshListState<CandidateItemResult> = XRefreshListState.getDefault()) :
    IUiState


sealed class BossTalentPoolUiIntent : IUiIntent {
    data object Refresh : BossTalentPoolUiIntent()
    data object LoadMore : BossTalentPoolUiIntent()
}