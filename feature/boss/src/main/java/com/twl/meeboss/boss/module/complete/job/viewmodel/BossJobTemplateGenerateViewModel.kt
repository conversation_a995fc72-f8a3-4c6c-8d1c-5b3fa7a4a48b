package com.twl.meeboss.boss.module.complete.job.viewmodel

import com.twl.meeboss.base.R
import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.foundation.IUiEvent
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.base.foundation.repo.toastErrorIfPresent
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.ktx.getDefaultPage
import com.twl.meeboss.base.ktx.getDefaultPageSize
import com.twl.meeboss.boss.api.response.toPostResultBean
import com.twl.meeboss.boss.export.model.PostResultBean
import com.twl.meeboss.boss.module.complete.account.model.CompanyJobTemplateBean
import com.twl.meeboss.boss.repos.BossRepository
import com.twl.meeboss.common.ktx.toggle
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class BossJobTemplateGenerateViewModel @Inject constructor(
    private val repository: BossRepository
) : BaseMviViewModel<BossJobTemplateGenerateUiState, BossJobTemplateGenerateUiIntent>() {

    private var currentPage = getDefaultPage()

    override fun initUiState() = BossJobTemplateGenerateUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossJobTemplateGenerateUiIntent.GetTemplateList -> {
                requestTemplateList()
            }

            is BossJobTemplateGenerateUiIntent.LoadMore -> {
                requestTemplateList(isRefresh = false)
            }

            is BossJobTemplateGenerateUiIntent.Next ->{
                patchPostJobs()
            }

            is BossJobTemplateGenerateUiIntent.SelectTheItem -> {
                sendUiState {
                    copy(
                        selectedTemplateIds = selectedTemplateIds.toggle(intent.templateId)
                    )
                }
            }
        }
    }

    private fun patchPostJobs() {
        launcherOnIO {
            val result = requestForResult(true){
                val selectedTemplateIds = uiStateFlow.value.selectedTemplateIds
                repository.batchAddByTemplate(selectedTemplateIds.joinToString(separator = ","))
            }
            result.toastErrorIfPresent()
            result.getOrNull()?.run {
                sendEvent(BossJobTemplateGenerateEvent.NavigateToPostResultEvent(batchResult?.map { it.toPostResultBean() } ?: emptyList()))
            }
        }
    }

    private fun requestTemplateList(isRefresh: Boolean = true) {
        val page = if (isRefresh) {
            getDefaultPage()
        } else {
            currentPage + 1
        }

        requestData(
            request = {
                repository.getCompanyTemplateList(
                    page = page,
                    pageSize = getDefaultPageSize()
                )
            },
            success = { result ->
                result?.run {
                    if (isRefresh) {
                        sendUiState {
                            copy(
                                listState = listState.refreshSuccess(
                                    result.content ?: emptyList(),
                                    result.hasMore ?: false
                                )
                            )
                        }
                    } else {
                        sendUiState {
                            copy(
                                listState = listState.loadMoreSuccess(
                                    result.content ?: emptyList(),
                                    result.hasMore ?: false
                                )
                            )
                        }
                    }
                    currentPage = page
                }
            },
            fail = {
                if (isRefresh) {
                    sendUiState { copy(listState = listState.refreshFail(it.message)) }
                } else {
                    sendUiState { copy(listState = listState.loadMoreFail()) }
                }
            }
        )
    }
}

data class BossJobTemplateGenerateUiState(
    val isLoading: Boolean = false,
    val listState: XRefreshListState<CompanyJobTemplateBean> = XRefreshListState(
        emptyList(),
        refreshText = R.string.common_data_updated.toResourceString(),
        loadState = LoadState.Loading
    ),
    val selectedTemplateIds: Set<String> = emptySet(),
) : IUiState

sealed class BossJobTemplateGenerateUiIntent : IUiIntent {
    data object GetTemplateList : BossJobTemplateGenerateUiIntent()
    data object LoadMore : BossJobTemplateGenerateUiIntent()
    data object Next : BossJobTemplateGenerateUiIntent()
    data class SelectTheItem(val templateId: String) : BossJobTemplateGenerateUiIntent()
}

interface BossJobTemplateGenerateEvent : IUiEvent {
    class NavigateToPostResultEvent(val postResultBeans: List<PostResultBean>) : BossJobTemplateGenerateEvent
}
