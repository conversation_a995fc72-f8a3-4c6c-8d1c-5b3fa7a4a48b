package com.twl.meeboss.boss.module.talentpool

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.twl.meeboss.base.components.list.refresh.XRefreshList
import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.main.router.BasePageRouter
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.boss.export.model.CandidatePageFrom
import com.twl.meeboss.boss.module.candidates.recommend.components.CandidateListItemCard
import com.twl.meeboss.boss.module.candidates.recommend.preview.CandidatesListPreviewData
import com.twl.meeboss.core.ui.component.state.XEmptyContent
import com.twl.meeboss.core.ui.component.state.XErrorContent
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable

/**
 * <AUTHOR>
 * @date 2025/3/3
 * description:
 */
@Composable
fun BossTalentPoolPage(
    uiState: BossTalentPoolUiState,
    onTitleInfo: () -> Unit,
    onRefresh: () -> Unit,
    onLoadMore: () -> Unit,
) {
    val context = LocalContext.current
    XTheme {
        Column(
            modifier = Modifier
                .background(COLOR_F5F5F5)
                .fillMaxSize()
        ) {
            XTitleBar(
                modifier = Modifier
                    .background(Color.White),
                title = stringResource(R.string.boss_talent_pool_list),
                rightContent = {
                    Image(
                        modifier = Modifier
                            .padding(end = 16.dp)
                            .noRippleClickable {
                                onTitleInfo()
                            },
                        painter = painterResource(id = R.drawable.boss_icon_telent_pool_info),
                        contentDescription = null,
                    )
                }
            )
            XRefreshList(
                modifier = Modifier.padding(all = 12.dp),
                listState = uiState.pagingData,
                getUniqueKey = { it.securityId ?: "" },
                onRefresh = onRefresh,
                onLoadMore = onLoadMore,
                emptyContent = {
                    XEmptyContent(
                        text = stringResource(R.string.boss_talent_pool_empty),
                        emptyButtonText = stringResource(R.string.common_view_candidates),
                        onButtonClick = {
                            BasePageRouter.jumpToMainActivity(context, 0, false)
                        })
                }, errorContent = { _, _ ->
                    XErrorContent(retryOnClick = onRefresh)
                }) { bean, _ ->
                Column {
                    CandidateListItemCard(
                        item = bean,
                        showTalent = false
                    ) {
                        bean.securityId?.let { securityId ->
                            BossPageRouter.jumpToBossCandidateResumeActivity(
                                context,
                                securityId,
                                CandidatePageFrom.POOL
                            )
                        }
                    }
                    HorizontalDivider(color = Color.Transparent, thickness = 12.dp)

                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewGeekTalentPoolContent() {
    BossTalentPoolPage(uiState = BossTalentPoolUiState(
        pagingData = XRefreshListState.getPreviewDefault(
            list = CandidatesListPreviewData.candidateList
        )
    ),
        onTitleInfo = {},
        onRefresh = {},
        onLoadMore = {})
}

