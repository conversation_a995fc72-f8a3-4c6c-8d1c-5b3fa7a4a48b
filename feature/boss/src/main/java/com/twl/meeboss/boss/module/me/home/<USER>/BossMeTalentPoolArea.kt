package com.twl.meeboss.boss.module.me.home.component

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.FragmentActivity
import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.components.pagestate.XStatePage
import com.twl.meeboss.base.flow.GlobalFlowManager
import com.twl.meeboss.base.flow.RECOMMENDED_SUB_TAB
import com.twl.meeboss.base.main.router.BasePageRouter
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.boss.export.model.CandidateItemResult
import com.twl.meeboss.boss.export.model.CandidatePageFrom
import com.twl.meeboss.boss.module.candidates.recommend.components.CandidateListItemCard
import com.twl.meeboss.boss.module.candidates.recommend.preview.CandidatesListPreviewData
import com.twl.meeboss.boss.module.talentpool.dialog.BossTalentPoolInstructionsBottomSheet
import com.twl.meeboss.core.ui.component.button.XCommonOutlineButton
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.showSafely

/**
 * @author: jinda
 * @date: 2025年05月09日
 * @description:
 */
@Composable
fun BossMeTalentPoolArea(
    talentPoolPagingData: XRefreshListState<CandidateItemResult>,
    retryOnClick: () -> Unit = {}
) {
    val context = LocalContext.current
    Column(
        modifier = Modifier
            .background(Color.White)
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 19.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = stringResource(id = R.string.company_talent_pool_heading),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Black222222
                )
                Image(
                    modifier = Modifier
                        .padding(start = 6.dp)
                        .noRippleClickable {
                            BossTalentPoolInstructionsBottomSheet().showSafely(context as FragmentActivity)
                        },
                    painter = painterResource(id = R.drawable.boss_tip_icon),
                    contentDescription = null,
                )
            }
            if (talentPoolPagingData.list.size > 2) {
                Text(
                    text = stringResource(id = R.string.common_all),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Black484848,
                    modifier = Modifier
                        .noRippleClickable {
                            BossPageRouter.jumpToBossTalentPoolActivity(context)
                        }
                )
            }
        }
        XStatePage(
            loadState = talentPoolPagingData.loadState,
            retryOnClick = retryOnClick,
            emptyContent = {
                TalentPoolAreaEmptyJobContent()
            },
            normalContent = {
                val talentListToShow = talentPoolPagingData.list.take(2)
                for ((index, candidate: CandidateItemResult) in talentListToShow.withIndex()) {
                    CandidateListItemCard(
                        item = candidate,
                        showTalent = false,
                        border = BorderStroke(1.dp, color = BlackEBEBEB)
                    ) {
                        candidate.securityId?.let { securityId: String ->
                            BossPageRouter.jumpToBossCandidateResumeActivity(
                                context,
                                securityId,
                                CandidatePageFrom.POOL
                            )
                        }
                    }
                    if (index < talentListToShow.size - 1) {
                        HorizontalDivider(
                            color = Color.Transparent,
                            thickness = 12.dp
                        )
                    }
                }
            }
        )
    }
}


@Composable
private fun TalentPoolAreaEmptyJobContent() {
    val context = LocalContext.current
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 28.dp, start = 30.dp, end = 30.dp),
            text = stringResource(id = R.string.company_talent_pool_empty_heading),
            fontSize = 14.sp,
            textAlign = TextAlign.Center,
            lineHeight = 22.sp,
            color = Black484848
        )
        Row(
            modifier = Modifier.padding(top = 20.dp, bottom = 40.dp),
            horizontalArrangement = Arrangement.Center
        ) {
            XCommonOutlineButton(
                enableCustomModifier = true,
                text = stringResource(id = R.string.company_talent_pool_empty_cta_if_job),
                onClick = {
                    // 切换到“推荐”子tab
                    GlobalFlowManager.sendCandidatesTabState { copy(subTabIndex = RECOMMENDED_SUB_TAB) }
                    BasePageRouter.jumpToMainActivity(context, 0, false)
                }
            )
        }
    }
}

@Preview
@Composable
private fun PreviewBossMeTalentPoolArea() {
    BossMeTalentPoolArea(talentPoolPagingData = XRefreshListState.getPreviewDefault(
        list = CandidatesListPreviewData.candidateList
    ))
}