package com.twl.meeboss.boss.module.me.home.preview

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.twl.meeboss.base.model.boss.BossAccountInfo
import com.twl.meeboss.base.model.boss.BossInfo
import com.twl.meeboss.base.model.boss.BossUserInfo
import java.util.UUID

/**
 * @author: 冯智健
 * @date: 2024年07月18日 15:12
 * @description:
 */
object BossMePreviewData {
    val bossBaseInfo = BossInfo(
        bossInfoUserVO = BossUserInfo(
            userId = UUID.randomUUID().toString(),
            avatar = "https://img2.baidu.com/it/u=**********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=400&h=400",
            firstName = "William",
            lastName = "Zhang",
            email = "<EMAIL>",
            phoneNumber = "*********"
        ),
        bossInfoBossVO = BossAccountInfo(
            firstCompleteStatus = 1,
            completeStatus = 1,
            companyName = "Zhipin company",
            position = "Product manager",
            workEmail = "<EMAIL>",
            activeJobCount = 10,
            appliedCandidateCount = 2,
            myFavoriteJobSeekerCount = 16
        ),
    )
}

class BossMePreviewParameterProvider : PreviewParameterProvider<BossInfo?> {
    override val values: Sequence<BossInfo?> = sequenceOf(
        BossMePreviewData.bossBaseInfo, null
    )
}

