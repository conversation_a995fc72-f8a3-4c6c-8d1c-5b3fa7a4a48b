package com.twl.meeboss.boss.module.me.candidate

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.google.accompanist.pager.PagerState
import com.google.accompanist.pager.rememberPagerState
import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.components.tip.XClosableTip
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.model.CandidateItemResult
import com.twl.meeboss.boss.module.candidates.recommend.preview.CandidatesListPreviewParameterProvider
import com.twl.meeboss.boss.module.me.candidate.component.BossMyCandidateListContent
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.core.ui.component.tabview.XTabView
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.theme.XTheme
import kotlinx.coroutines.launch


@Composable
fun BossMyCandidatePage(
    uiState:BossMyCandidateUiState = BossMyCandidateUiState(),
    pageState: PagerState = rememberPagerState(initialPage = uiState.tabIndex),
    onMoreClick: (pageIndex: Int, candidateItem: CandidateItemResult) -> Unit = { _, _ -> },
    onChatClick: (pageIndex: Int, candidateItem: CandidateItemResult) -> Unit = { _, _ -> },
    onArchiveRemoveClick: (pageIndex: Int, candidateItem: CandidateItemResult) -> Unit = { _, _ -> },
    onCloseTipClick: (pageIndex: Int) -> Unit = {},
    onRefresh: (pageIndex:Int) -> Unit = {},
    onLoadMore: (pageIndex:Int) -> Unit = {},
) {

    val coroutineScope = rememberCoroutineScope()
    LaunchedEffect(Unit) {
        coroutineScope.launch {
            pageState.animateScrollToPage(uiState.tabIndex)
        }
    }

    LaunchedEffect(key1 = pageState) {
        snapshotFlow { pageState.currentPage }.collect{
            BossPointReporter.myCandidateSubTabClick(it+1)
        }
    }

    XTheme {
        Column(
            modifier = Modifier
                .background(Color.White)
                .fillMaxSize()
        ) {
            XTitleBar(
                title = stringResource(id = R.string.common_my_candidates),
            )
            XTabView(
                modifier = Modifier.weight(1f),
                pagerState = pageState,
                tabIndicatorPercent = 0.5F,
                options = uiState.candidateTitleList
            ) { pageIndex ->

                val candidateUiState = uiState.bossMyCandidateUiStateList[pageIndex]
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                ) {
                    val visibility = remember {
                        mutableStateOf(true)
                    }

                    if (visibility.value && candidateUiState.tipText.isNotBlank()) {
                        XClosableTip(
                            tipText = candidateUiState.tipText,
                            onCloseTipClick = {
                                visibility.value = false
                                onCloseTipClick(pageIndex)
                            }
                        )
                    }

                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .background(color = COLOR_F5F5F5)
                            .padding(top = 12.dp, start = 12.dp, end = 12.dp)
                    ) {
                        BossMyCandidateListContent(
                            listState = candidateUiState.listState,
                            pageIndex = pageIndex,
                            onMoreClick = { jobItem ->
                                onMoreClick(pageIndex, jobItem)
                            },
                            onChatClick = { jobItem ->
                                onChatClick(pageIndex, jobItem)
                            },
                            onArchiveRemoveClick = { jobItem ->
                                onArchiveRemoveClick(pageIndex, jobItem)
                            },
                            onRefresh = {
                                onRefresh(pageIndex)
                            },
                            onLoadMore = {
                                onLoadMore(pageIndex)
                            }
                        )
                    }
                }

            }
        }
    }
}


@Preview
@Composable
fun PreviewBossMyCandidatePage(
    @PreviewParameter(CandidatesListPreviewParameterProvider::class)
    listState: XRefreshListState<CandidateItemResult>
) {
    val jobTitle = listOf(
        stringResource(id = R.string.messages_employer_second_nav_value4),
        stringResource(id = R.string.common_in_process),
        stringResource(id = R.string.common_archived),
    )
    val bossMyCandidateUiStateList = jobTitle.map {
        BossMyCandidateListUiState(
            listState = listState
        )
    }
    val uiState = BossMyCandidateUiState().copy(
        tabIndex = 0,
        candidateTitleList = jobTitle,
        bossMyCandidateUiStateList = bossMyCandidateUiStateList,
    )

    BossMyCandidatePage(
        uiState = uiState
    )
}