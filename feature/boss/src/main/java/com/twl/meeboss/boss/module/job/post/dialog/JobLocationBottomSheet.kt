package com.twl.meeboss.boss.module.job.post.dialog

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.twl.meeboss.base.model.CityBean
import com.twl.meeboss.boss.module.job.post.components.BossEditJobLocation
import com.twl.meeboss.boss.module.job.post.components.BossEditJobLocationUiIntent
import com.twl.meeboss.boss.module.job.post.components.BossEditJobLocationViewModel
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.utils.dismissSafely

/**
 * B编辑职位地点底部弹窗
 */
class JobLocationBottomSheet : CommonBottomDialogFragment() {
    private val defaultList: MutableList<CityBean> by lazy {
        arguments?.getSerializable("defaultList") as? MutableList<CityBean> ?: mutableListOf()
    }
    private val isEmployeeLocation: Boolean by lazy {
        arguments?.getBoolean("isEmployeeLocation") ?: false
    }
    private var callback: ((MutableList<CityBean>) -> Unit)? = null

     companion object {
         fun newInstance(
             defaultList: MutableList<CityBean>,
             isEmployeeLocation: Boolean = false,
             callback: (MutableList<CityBean>) -> Unit
         ) = JobLocationBottomSheet().apply {
             this.callback = callback
                arguments = bundleOf(
                    "defaultList" to defaultList,
                    "isEmployeeLocation" to isEmployeeLocation
                )
         }
     }

    @Composable
    override fun DialogContent() {
        val viewModel: BossEditJobLocationViewModel = viewModel()
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        LaunchedEffect(key1 = viewModel) {
            viewModel.sendUiIntent(BossEditJobLocationUiIntent.Init(defaultList))
        }
        BossEditJobLocation(uiState,
            isEmployeeLocation = isEmployeeLocation,
            callback = this::onSaveCallback,
            onClickClose = this::onClosedCallback,
            onValueChanged = {
                viewModel.sendUiIntent(BossEditJobLocationUiIntent.UpdateInput(it))
            },
            onDelete = {
                viewModel.sendUiIntent(BossEditJobLocationUiIntent.DeleteSelectedLocation(it))
            },
            onSelected = {
                viewModel.sendUiIntent(BossEditJobLocationUiIntent.AddSelectedLocation(it))
            }
        )
    }

    private fun onSaveCallback(list: MutableList<CityBean>) {
        callback?.invoke(list)
        dismissSafely()
    }

    private fun onClosedCallback() {
        dismissSafely()
    }
}