package com.twl.meeboss.boss.module.job.post.dialog

import androidx.compose.runtime.Composable
import androidx.core.os.bundleOf
import com.twl.meeboss.boss.module.job.post.components.SuggestTitleContent
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.base.model.HighlightBean

class JobTitleBottomSheet : CommonBottomDialogFragment() {

    private val defaultValue by lazy {
        arguments?.getSerializable("defaultValue") as? HighlightBean ?: HighlightBean()
    }
    private var onSelectCallback: ((HighlightBean) -> Unit)? = null

    companion object {
        fun newInstance(defaultValue: HighlightBean, onSelectCallback: (HighlightBean) -> Unit) = JobTitleBottomSheet().apply {
            this.onSelectCallback = onSelectCallback
            arguments = bundleOf(
                "defaultValue" to defaultValue
            )
        }
    }

    @Composable
    override fun DialogContent() {
        SuggestTitleContent(defaultValue = defaultValue, onClickClose = {
            dismissSafely()
        }, callback = {
            onSelectCallback?.invoke(it)
            dismissSafely()
        })
    }

}