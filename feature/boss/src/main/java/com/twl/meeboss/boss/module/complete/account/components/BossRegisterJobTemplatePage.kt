package com.twl.meeboss.boss.module.complete.account.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.base.model.job.JobTemplateBean
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.module.complete.account.preview.JobTemplateItemPreviewParameterProvider
import com.twl.meeboss.boss.module.complete.account.preview.JobTemplateListPreviewParameterProvider
import com.twl.meeboss.boss.module.complete.account.viewmodel.BossRegisterJobTemplateUiState
import com.twl.meeboss.common.ktx.formatMoney
import com.twl.meeboss.core.ui.component.XDivider
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.resource.employer_post_job_from_templates_button
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_666666
import com.twl.meeboss.core.ui.theme.COLOR_888888
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable

@Composable
fun BossRegisterJobTemplatePage(
    uiState: BossRegisterJobTemplateUiState,
    modifier: Modifier = Modifier.background(Color.White),
    onPatchPost: () -> Unit = {},
    onSelectItem: (String) -> Unit = {},
    onItemClick: (String) -> Unit = {},
    onClickSkip: () -> Unit = {},
) {
    XTheme {
        Column(
            modifier = modifier,
        ) {
            XTitleBar(
                onBackClick = {
                    onClickSkip()
                }
            )

            Column(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            ) {

                Text(
                    text = stringResource(id = R.string.employer_post_job_from_templates_title),
                    modifier = Modifier.padding(top = 12.dp),
                    fontSize = 28.sp,
                    fontWeight = FontWeight.SemiBold
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = stringResource(id = R.string.boss_invitation_pick_first_job_subtitle),
                    style = TextStyle(
                        fontSize = 12.sp,
                        lineHeight = 18.sp,
                        fontWeight = FontWeight.W400,
                        color = COLOR_666666,
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                LazyColumn {
                    itemsIndexed(uiState.templateList) { index, item ->
                        BossRegisterJobTemplateItem(
                            item, selected = uiState.selectedTemplateIds.contains(item.templateId),
                            onItemClick = onItemClick,
                            onSelectedItem = onSelectItem
                        )
                        XDivider()
                    }
                }
            }

            XCommonButton(
                text = if (uiState.selectedTemplateIds.size < 2) stringResource(R.string.boss_register_post_first_job_button)
                    else employer_post_job_from_templates_button(uiState.selectedTemplateIds.size.toString()),
                modifier = Modifier.padding(16.dp),
                enabled = uiState.selectedTemplateIds.isNotEmpty(),
                onClick = {
                    onPatchPost()
                },
            )

            Spacer(modifier = Modifier.height(6.dp))
            Text(
                text = stringResource(id = R.string.geek_register_skip),
                style = TextStyle(
                    fontSize = 16.sp,
                    lineHeight = 24.sp,
                    fontWeight = FontWeight(590),
                    color = Color(0xFF222222),
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier
                    .padding(bottom = 20.dp)
                    .noRippleClickable(onClick = onClickSkip)
                    .align(Alignment.CenterHorizontally)
            )
        }
    }
}

@Composable
fun BossRegisterJobTemplateItem(
    templateBean: JobTemplateBean,
    selected: Boolean = false,
    onItemClick: (String) -> Unit = {},
    onSelectedItem: (String) -> Unit = {}
) {
    Row(
        modifier = Modifier
            .height(IntrinsicSize.Min)
            .fillMaxWidth()
            .padding(vertical = 24.dp)
            .noRippleClickable{onItemClick(templateBean.templateId)},
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Row(Modifier
            .noRippleClickable { onSelectedItem(templateBean.templateId) }
            .padding(end = 12.dp)) {
            Image(
                modifier = Modifier.fillMaxHeight(),
                painter = painterResource(id = if (selected) R.drawable.ui_icon_multi_ratio_selected else R.drawable.ui_icon_multi_ratio_unselected),
                contentDescription = null
            )
        }
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = templateBean.jobTitle,

                style = TextStyle(
                    fontSize = 16.sp,
                    lineHeight = 24.sp,
                    fontWeight = FontWeight.W500,
                    color = COLOR_222222,
                ),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
            )

            Spacer(modifier = Modifier.height(4.dp))

            val minSalaryStr =
                templateBean.minSalary?.formatMoney(LocalManager.getAppLocale()) ?: ""
            val maxSalaryStr =
                templateBean.maxSalary?.formatMoney(LocalManager.getAppLocale()) ?: ""
            val jobSalaryStr = when {
                templateBean.minSalary != null && templateBean.maxSalary != null -> {
                    "${templateBean.salaryUnit?.name ?: ""}${minSalaryStr}-${templateBean.salaryUnit?.name ?: ""}${maxSalaryStr}${templateBean.salaryShortType?.name ?: ""}"
                }

                templateBean.minSalary != null -> {
                    "${templateBean.salaryUnit?.name ?: ""}${minSalaryStr}${templateBean.salaryShortType?.name ?: ""}"
                }

                templateBean.maxSalary != null -> {
                    "${templateBean.salaryUnit?.name ?: ""}${maxSalaryStr}${templateBean.salaryShortType?.name ?: ""}"
                }

                else -> {
                    ""
                }
            }

            val jobDescStrList = mutableListOf<String>().apply {
                addAll(templateBean.jobType.map { it.name ?: "" })
                add(templateBean.locationType?.name ?: "")
                add(jobSalaryStr)
            }.filter {
                it.isNotBlank()
            }

            Text(
                text = jobDescStrList.joinToString(" · "),
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 21.sp,
                    fontWeight = FontWeight.W400,
                    color = COLOR_888888,
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        }

        Image(
            modifier = Modifier.size(20.dp),
            painter = painterResource(id = R.drawable.ui_icon_right_arrow),
            contentDescription = null
        )

    }
}

@Preview
@Composable
private fun PreviewBossRegisterJobTemplateItem(@PreviewParameter(JobTemplateItemPreviewParameterProvider::class) templateBean:JobTemplateBean) {
    Column(
        modifier = Modifier.background(Color.White)
    ) {
        BossRegisterJobTemplateItem(
            templateBean,
        )
    }
}

@Preview
@Composable
private fun PreviewBossRegisterJobTemplatePage(@PreviewParameter(
    JobTemplateListPreviewParameterProvider::class) list: List<JobTemplateBean>) {
    BossRegisterJobTemplatePage(
        uiState = BossRegisterJobTemplateUiState(templateList = list)
    )
}
