package com.twl.meeboss.boss.module.me.personal

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.model.boss.BossInfo
import com.twl.meeboss.base.model.common.FileItem
import com.twl.meeboss.base.model.common.getLegalImageItem
import com.twl.meeboss.base.model.enumeration.UploadFileBizType
import com.twl.meeboss.base.mudule.ModuleManager
import com.twl.meeboss.base.repos.BaseBusinessRepository
import com.twl.meeboss.base.usecase.ImageUploadUseCase
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.repos.BossRepository
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.T
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * @author: 冯智健
 * @date: 2024年08月08日 14:07
 * @description:
 */
@HiltViewModel
class BossPersonalInfoViewModel @Inject constructor(
    private val bossRepo: BossRepository,
    private val baseBusinessApi: BaseBusinessRepository,
    private val imageUploadUseCase: ImageUploadUseCase,
) : BaseMviViewModel<BossPersonalInfoUIState, BossPersonalInfoUiIntent>() {

    var bossBaseInfoResult: BossInfo? = null
    val showLoading: MutableLiveData<Boolean> = MutableLiveData()
    override fun initUiState() = BossPersonalInfoUIState()

    override fun handleIntent(intent: IUiIntent) {
        when(intent) {
            is BossPersonalInfoUiIntent.SaveInfo -> saveInfo()
            is BossPersonalInfoUiIntent.UploadAvatar -> uploadAvatar(intent.mediaItems)
            is BossPersonalInfoUiIntent.UpdatePosition -> sendUiState { copy(position = intent.position).checkCanSave() }
            is BossPersonalInfoUiIntent.OnFirstNameChange -> sendUiState { copy(firstName = intent.firstName).checkCanSave() }
            is BossPersonalInfoUiIntent.OnLastNameChange -> sendUiState { copy(lastName = intent.lastName).checkCanSave() }
        }
    }

    fun initInfo(){
        bossBaseInfoResult?.apply {
            val avatar = bossBaseInfoResult?.bossInfoUserVO?.avatar
            val position = bossBaseInfoResult?.bossInfoBossVO?.position
            val firstName = bossBaseInfoResult?.bossInfoUserVO?.firstName
            val lastName = bossBaseInfoResult?.bossInfoUserVO?.lastName
            val workEmail = bossBaseInfoResult?.bossInfoBossVO?.workEmail
            val companyName = bossBaseInfoResult?.bossInfoBossVO?.companyName
            sendUiState { copy(
                avatar = avatar ?: "",
                position = position ?: "",
                firstName = firstName ?: "",
                lastName = lastName ?: "",
                workEmail = workEmail ?: "",
                companyName = companyName ?: "",
                ) }
        } ?: apply {
            XLog.error(TAG, "bossBaseInfoResult null")
        }
    }


    private fun uploadAvatar(selectMediaItems: List<FileItem>) {
        val mediaItems = selectMediaItems.getLegalImageItem(true)
        if (mediaItems.isEmpty()) {
            return
        }

        showLoading.value = true
        imageUploadUseCase(
            viewModelScope = viewModelScope,
            bizType = UploadFileBizType.COMPANY_LOGO,
            fileItemList = mediaItems,
            success = {
                val list = it.mapNotNull { fileUploadResult ->
                    fileUploadResult.originUrl
                }
                if (list.isNotEmpty()) {
                    changeAvatar(list[0])
                } else {
                    launcherOnMain {
                        showLoading.value = false
                    }
                }
            },
            fail = {
                it?.let {
                    sendLoadUiState(LoadState.Fail(it.message))
                    XLog.error(TAG, "uploadImageFilesUseCase fail: ${it.message}")
                } ?: run {
                    sendLoadUiState(LoadState.Empty())
                    XLog.error(TAG, "uploadImageFilesUseCase is empty data")
                }
                T.ss(R.string.upload_avatar_failed)
                launcherOnMain {
                    showLoading.value = false
                }
            }
        )
    }


    private fun changeAvatar(avatar: String){
        requestData(
            request = {
                baseBusinessApi.changeAvatar(avatar)
            },
            success = {
                BossPointReporter.photoUpload()
                sendUiState { copy(avatar = avatar) }
                showLoading.value = false
            },
            fail = {
                XLog.error(TAG, "saveInfo error: ${it.message}")
                T.ss(it.message)
                showLoading.value = false
            }
        )
    }

    private fun saveInfo() {
        requestData(
            request = {
                bossRepo.updatePersonInfo(uiStateFlow.value.firstName, uiStateFlow.value.lastName, uiStateFlow.value.position)
            },
            success = {
                ModuleManager.updateUserInfo()
                T.ss(R.string.common_saved_successfully)
                sendUiState {
                    copy(finishPage = true)
                }
            },
            fail = {
                T.ss(it.message)
                XLog.error(TAG, "saveInfo error: ${it.message}")
            }
        )
    }
}

data class BossPersonalInfoUIState(
    val finishPage: Boolean = false,
    val avatar: String = "",
    val position: String = "",
    val firstName: String = "",
    val lastName: String = "",
    val workEmail: String = "",
    val saveEnable: Boolean = false,
    val companyName: String = "",
) : IUiState {
    fun checkCanSave():BossPersonalInfoUIState {
        return copy(
            saveEnable = firstName.isNotEmpty() && lastName.isNotEmpty() && position.isNotEmpty()
        )
    }
}

sealed class BossPersonalInfoUiIntent: IUiIntent {
    data object SaveInfo: BossPersonalInfoUiIntent()
    data class UploadAvatar(val mediaItems: List<FileItem>): BossPersonalInfoUiIntent()
    data class UpdatePosition(val position: String): BossPersonalInfoUiIntent()
    data class OnFirstNameChange(val firstName: String): BossPersonalInfoUiIntent()
    data class OnLastNameChange(val lastName: String): BossPersonalInfoUiIntent()
}