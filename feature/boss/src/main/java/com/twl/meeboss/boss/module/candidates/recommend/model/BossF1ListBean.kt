package com.twl.meeboss.boss.module.candidates.recommend.model

import androidx.compose.runtime.Stable
import com.twl.meeboss.base.model.BaseEntity
import java.util.UUID
import com.twl.meeboss.boss.export.model.CandidateItemResult

@Stable
data class BossF1ListBean(
    @BossF1CardType val cardType: Int,
    val candidate: CandidateItemResult? = null,
    val extend: BossExtendBean? = null,
) :BaseEntity{
    fun getUniqueKey(): String {
        return when (cardType) {
            BossF1CardType.CANDIDATE -> candidate?.securityId ?: ""
            BossF1CardType.BEGINNER_GUIDE_CARD -> UUID.randomUUID().toString()
            else -> UUID.randomUUID().toString()
        }
    }
}