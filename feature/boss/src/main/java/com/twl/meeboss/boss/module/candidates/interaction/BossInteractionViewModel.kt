package com.twl.meeboss.boss.module.candidates.interaction

import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.ktx.getDefaultPageSize
import com.twl.meeboss.base.model.enumeration.InteractionType
import com.twl.meeboss.boss.export.model.CandidateItemResult
import com.twl.meeboss.boss.repos.BossCandidatesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * @author: 冯智健
 * @date: 2024年07月17日 20:54
 * @description:
 */
@HiltViewModel
class BossInteractionViewModel @Inject constructor(
    private val repo: BossCandidatesRepository,
): BaseMviViewModel<BossInteractionUiState, BossInteractionUiIntent>() {

    init {
        getTabPageData(true)
    }

    override fun initUiState() = BossInteractionUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossInteractionUiIntent.GetTabPageData -> getTabPageData(intent.isRefresh)
            is BossInteractionUiIntent.SwitchTabPage -> switchTabPage(intent.tabIndex)
            is BossInteractionUiIntent.UpdateJobList -> updateJobList()
        }
    }

    private fun updateJobList() {
        sendUiState {
            copy(
                tabIndex = tabIndex,
                viewedMeCandidateUiState = viewedMeCandidateUiState.copy(hasRequested = false),
                favoredMeCandidateUiState = favoredMeCandidateUiState.copy(hasRequested = false),
            )
        }
        getTabPageData(true)
    }
    private fun getTabPageData(isRefresh: Boolean) {
        when(uiStateFlow.value.tabIndex) {
            InteractionType.VIEWED_ME -> getViewedMeCandidateList(false, isRefresh)
            InteractionType.LIKED_ME -> getFavoredMeCandidateList(false, isRefresh)
        }
    }
    
    private fun switchTabPage(tabIndex: Int) {
        sendUiState {
            copy(tabIndex = tabIndex)
        }
        when(tabIndex) {
            InteractionType.VIEWED_ME -> getViewedMeCandidateList(notSkip = true, isRefresh = true)
            InteractionType.LIKED_ME -> getFavoredMeCandidateList(notSkip = true, isRefresh = true)
        }
    }

    private fun getViewedMeCandidateList(notSkip: Boolean, isRefresh: Boolean) {
        launcherOnIO {
            val viewedMeUiState = uiStateFlow.value.viewedMeCandidateUiState
            if (viewedMeUiState.hasRequested && notSkip) {
                return@launcherOnIO
            }

            val requestPage = if(isRefresh) 1 else viewedMeUiState.page + 1

            requestData(
                enableLoadState = false,
                request = {
                    repo.getViewedMeCandidateList(requestPage, getDefaultPageSize())
                },
                success = {
                    sendUiState {
                        copy(
                            viewedMeCandidateUiState = viewedMeCandidateUiState.copy(
                                page = requestPage,
                                hasRequested = true,
                                pagingData = if (isRefresh) {
                                    viewedMeCandidateUiState.pagingData.refreshSuccess(
                                        it?.content ?: listOf(), it?.hasMore ?: false
                                    )
                                } else {
                                    viewedMeCandidateUiState.pagingData.loadMoreSuccess(
                                        it?.content ?: listOf(), it?.hasMore ?: false
                                    )
                                }
                            )
                        )
                    }
                },
                fail = {
                    sendUiState {
                        copy(
                            viewedMeCandidateUiState = viewedMeCandidateUiState.copy(
                                page = viewedMeCandidateUiState.page + 1,
                                hasRequested = true,
                                pagingData = if (isRefresh) viewedMeCandidateUiState.pagingData.refreshFail(it.message) else viewedMeCandidateUiState.pagingData.loadMoreFail()
                            )
                        )
                    }
                }
            )
        }
    }

    private fun getFavoredMeCandidateList(notSkip: Boolean, isRefresh: Boolean) {
        launcherOnIO {
            val favoredMeUiState = uiStateFlow.value.favoredMeCandidateUiState
            if (favoredMeUiState.hasRequested && notSkip) {
                return@launcherOnIO
            }

            val requestPage = if(isRefresh) 1 else favoredMeUiState.page + 1

            requestData(
                enableLoadState = false,
                request = {
                    repo.getFavoredMeCandidateList(requestPage, getDefaultPageSize())
                },
                success = {
                    sendUiState {
                        copy(
                            favoredMeCandidateUiState = favoredMeCandidateUiState.copy(
                                page = requestPage,
                                hasRequested = true,
                                pagingData = if (isRefresh) {
                                    favoredMeCandidateUiState.pagingData.refreshSuccess(
                                        it?.content ?: listOf(), it?.hasMore ?: false
                                    )
                                } else {
                                    favoredMeCandidateUiState.pagingData.loadMoreSuccess(
                                        it?.content ?: listOf(), it?.hasMore ?: false
                                    )
                                }
                            )
                        )
                    }
                },
                fail = {
                    sendUiState {
                        copy(
                            favoredMeCandidateUiState = favoredMeCandidateUiState.copy(
                                page = favoredMeCandidateUiState.page + 1,
                                hasRequested = true,
                                pagingData = if (isRefresh) favoredMeCandidateUiState.pagingData.refreshFail(it.message) else favoredMeCandidateUiState.pagingData.loadMoreFail()
                            )
                        )
                    }
                }
            )
        }
    }
}

data class BossInteractionUiState(
    val tabIndex: Int = 0,
    val viewedMeCandidateUiState: CandidateListUiState = CandidateListUiState(),
    val favoredMeCandidateUiState: CandidateListUiState = CandidateListUiState(),
) : IUiState

data class CandidateListUiState(
    val hasRequested: Boolean = false,
    val page: Int = 1,
    val pagingData: XRefreshListState<CandidateItemResult> = XRefreshListState.getDefault(),
)

sealed class BossInteractionUiIntent: IUiIntent {
    data class GetTabPageData(val isRefresh: Boolean = true): BossInteractionUiIntent()
    data class SwitchTabPage(val tabIndex: Int): BossInteractionUiIntent()
    data object UpdateJobList: BossInteractionUiIntent()
}
