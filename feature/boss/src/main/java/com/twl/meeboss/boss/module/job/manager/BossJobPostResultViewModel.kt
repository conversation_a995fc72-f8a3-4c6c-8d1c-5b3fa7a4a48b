package com.twl.meeboss.boss.module.job.manager

import com.twl.meeboss.base.foundation.viewmodel.BaseLifecycleViewModel
import com.twl.meeboss.base.model.job.JobStatus
import com.twl.meeboss.boss.export.model.PostResultBean


/**
 * @author: 冯智健
 * @date: 2024年07月11日 22:12
 * @description:
 */
class BossJobPostResultViewModel: BaseLifecycleViewModel() {
    var postResults = emptyList<PostResultBean>()

    @JobStatus
    val jobStatus: Int
        get() = if (postResults.isNotEmpty()) {
            postResults[0].jobStatus
        } else {
            JobStatus.IN_REVIEW
        }

    val jobId: String
        get() = if (postResults.isNotEmpty()) {
            postResults[0].jobId
        } else {
            ""
        }

    val jobDeniedReason: String
        get() = postResults.firstOrNull{it.rejectReason.isNotEmpty()}?.rejectReason ?: ""

    /**
     * 从哪里进入的职位编辑页，目前主要是区分webview的场景
     */
    var editJobFrom = ""
}