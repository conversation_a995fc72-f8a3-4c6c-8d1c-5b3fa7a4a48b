package com.twl.meeboss.boss.module.complete.company.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.boss.R
import com.twl.meeboss.common.ktx.hasEmoji
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.COLOR_000000
import com.twl.meeboss.core.ui.theme.COLOR_0D9EA3
import com.twl.meeboss.core.ui.theme.GRAY_AAAAAA
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.toResourceString

class BossCompanyAbbreviationBottomSheet : CommonBottomDialogFragment() {

    private var onSaveCallback: ((TextFieldValue) -> Unit)? = null

    private var abbreviation : TextFieldValue? = null

    companion object {
        fun newInstance(
            abbreviation: TextFieldValue? = null,
            onSaveCallback: (TextFieldValue) -> Unit = {}
        ) = BossCompanyAbbreviationBottomSheet().apply {
            this.onSaveCallback = onSaveCallback
            this.abbreviation = abbreviation
        }
    }

    @Composable
    override fun DialogContent() {
        BossCompanyAbbreviationContent(
            abbreviation = abbreviation?: TextFieldValue(),
            onBackClick = {
                dismiss()
            },
            onSaveClick = {
                onSaveCallback?.invoke(it)
                dismiss()
            }
        )
    }
}

@Composable
fun BossCompanyAbbreviationContent(
    abbreviation: TextFieldValue = TextFieldValue(),
    onBackClick: () -> Unit = {},
    onSaveClick: (TextFieldValue) -> Unit = {},
    maxCount: Int = DefaultValueConstants.MAX_INPUT_COUNT_COMPANY_ABBREVIATION
) {

    val locale = LocalConfiguration.current.locales[0]
    XTheme {
        val inputField: MutableLiveData<TextFieldValue> = MutableLiveData()
        LaunchedEffect(Unit) {
            inputField.value = abbreviation
            XLog.info("shy","locale language:${locale.language},country:${locale.country}")
        }
        val content = inputField.observeAsState()

        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(state = rememberScrollState())
                .background(
                    Color.White,
                    shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                )
        ) {

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp, 20.dp)
            ) {

                Image(painter = painterResource(id = R.drawable.ui_dailog_close),
                    contentDescription = "Close dialog",
                    modifier = Modifier
                        .noRippleClickable {
                            onBackClick()
                        }
                        .size(24.dp))

                Text(
                    text = stringResource(id = R.string.common_button_save),
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .noRippleClickable {
                            onSaveClick(content.value ?: TextFieldValue())
                        },
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(600),
                        color = Secondary,
                    )
                )
            }

            Text(
                text = stringResource(id = R.string.common_company_abbreviation),
                modifier = Modifier.padding(16.dp,0.dp),
                style = TextStyle(
                    fontSize = 28.sp,
                    fontWeight = FontWeight(600),
                    color = COLOR_000000,
                )
            )


            Text(
                text = stringResource(id = R.string.common_example),
                modifier = Modifier.padding(16.dp, 14.dp),
                style = TextStyle(
                    fontSize = 13.sp,
                    fontWeight = FontWeight(400),
                    color = Black888888,
                )
            )

            Image(painter = painterResource(
                id = R.mipmap.boss_example_company_abbreviation),
                contentDescription = "",
                modifier = Modifier
                    .padding(16.dp, 0.dp)
                    .fillMaxWidth()
                    .aspectRatio(334f / 192f)
            )

            Text(
                modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 28.dp),
                text = stringResource(id = R.string.common_company_abbreviation),
                color = Black888888,
                fontSize = 13.sp,
                fontWeight = FontWeight.W500)

            val focusRequester = remember { FocusRequester() }
            var isFocus by remember {
                mutableStateOf(false)
            }
            BasicTextField(modifier = Modifier
                .fillMaxWidth()
                .focusRequester(focusRequester)
                .onFocusChanged {
                    isFocus = it.isFocused
                },
                cursorBrush = SolidColor(COLOR_0D9EA3),
                textStyle = TextStyle(
                    fontSize = 17.sp,
                    fontWeight = FontWeight.W400,
                    color = Black222222,
                    ),
                singleLine = true,
                value = content.value?: TextFieldValue(),
                onValueChange = {
                    if (it.text.hasEmoji()) {
                        return@BasicTextField
                    }
                    if (it.text.length <= maxCount) {
                        inputField.value = it
                    } else {
                        T.ss(R.string.common_max_input_toast.toResourceString(maxCount.toString()))
                    }

                }, decorationBox = { innerTextField ->
                    Column(modifier = Modifier.padding(16.dp, 8.dp)) {
                        Box {

                            Box(modifier = Modifier.fillMaxWidth()) {
                                innerTextField()
                            }

                            if (content.value?.text.isNullOrBlank()) {
                                Text(
                                    text = stringResource(id = R.string.common_enter_company_abbreviation),
                                    color = GRAY_AAAAAA,
                                    fontSize = 17.sp,
                                    fontWeight = FontWeight.W400)
                            }
                        }

                        HorizontalDivider(
                            modifier = Modifier.padding(top = 12.dp),
                            thickness = 1.dp,
                            color = if(isFocus) Black222222 else BlackEBEBEB
                        )
                    }

                })

            LaunchedEffect(Unit) {
                focusRequester.requestFocus()
            }
        }
    }
}

@Composable
private fun PreviewBossCompanyAbbreviationContent1() {
    BossCompanyAbbreviationContent()
}
@Composable
private fun PreviewBossCompanyAbbreviationContent2() {
    BossCompanyAbbreviationContent()
}

@Preview(locale = "en-rDE")
@Composable
private fun PreviewBossCompanyAbbreviationContent3() {
    BossCompanyAbbreviationContent()
}