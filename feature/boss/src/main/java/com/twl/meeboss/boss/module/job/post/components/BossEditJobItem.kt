package com.twl.meeboss.boss.module.job.post.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.ktx.stringWithOption
import com.twl.meeboss.boss.R
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.GRAY_AAAAAA
import com.twl.meeboss.core.ui.utils.noRippleClickable

/**
 * 编辑、发布职位页面item
 */
@Composable
fun BossEditJobItem(modifier: Modifier = Modifier, title: String, content: String,
                optional: Boolean, hint: String,canEdit:Boolean = true, canDelete: Boolean = false, onDeleteClick: () -> Unit = {}) {
    Column(modifier = modifier) {
        Spacer(modifier = Modifier.height(20.dp))
        Text(text = title.stringWithOption(optional), fontSize = 13.sp,
            color = Black888888,
            fontWeight = FontWeight.Medium)
        Spacer(modifier = Modifier.height(8.dp))
        val hasContent = content.isNotBlank()
        if (hasContent) {
            Row {
                Text(modifier = Modifier.weight(1f), text = content, color = if(canEdit) Black222222 else GRAY_AAAAAA, fontSize = 17.sp, maxLines = 1, overflow = TextOverflow.Ellipsis, fontWeight = FontWeight.Medium)
                if (canDelete) {
                    Image(
                        modifier = Modifier
                            .noRippleClickable(onClick = onDeleteClick)
                            .align(Alignment.CenterVertically),
                        painter = painterResource(id = R.drawable.ui_form_item_delete),
                        contentDescription = ""
                    )
                }
            }
        } else {
            Text(text = hint, color = GRAY_AAAAAA, fontSize = 17.sp, maxLines = 1, overflow = TextOverflow.Ellipsis, fontWeight = FontWeight.Medium)
        }
        Spacer(modifier = Modifier.height(20.dp))
        HorizontalDivider(thickness = 0.5.dp, color = BlackEBEBEB)
    }
}

@Preview(showBackground = true)
@Composable
private fun BossEditJobItemPreview() {
    Column {
        BossEditJobItem(
            title = "职位名称",
            content = "Android开发工程师",
            optional = false,
            hint = "请填写",
            canEdit = true,
            canDelete = true,
        )

        BossEditJobItem(
            title = "职位名称",
            content = "",
            optional = true,
            hint = "请填写",
            canEdit = true,
        )

        BossEditJobItem(
            title = "职位名称",
            content = "内容不可编辑",
            optional = false,
            hint = "请填写",
            canEdit = false,
        )
    }
}