package com.twl.meeboss.boss.di

import com.twl.meeboss.boss.api.BossApi
import com.twl.meeboss.boss.api.CandidateApi
import com.twl.meeboss.boss.api.CompanyApi
import com.twl.meeboss.boss.api.JobApi
import com.twl.meeboss.core.network.getService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * @author: 冯智健
 * @date: 2024年07月01日 11:38
 * @description:
 */
@Module
@InstallIn(SingletonComponent::class)
class BossApiModule {

    @Provides
    fun provideJobApi(): JobApi = getService(JobApi::class.java)

    @Provides
    fun provideCandidatesApi(): CandidateApi = getService(CandidateApi::class.java)

    @Provides
    fun provideCompanyApi(): CompanyApi = getService(CompanyApi::class.java)

    @Provides
    fun provideBossApi(): BossApi = getService(BossApi::class.java)
}