package com.twl.meeboss.boss.module.me.candidate.dialog


import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.model.CandidateBaseInfo
import com.twl.meeboss.boss.export.model.CandidateItemResult
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black020202
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable

class BossSavedOptionBottomSheet : CommonBottomDialogFragment() {
    private var onRemoveClick: (() -> Unit) ?= null
    private val candidateItem by lazy {
        arguments?.getSerializable("candidateItem") as? CandidateItemResult
    }

    companion object {
        fun newInstance(
            candidateItem: CandidateItemResult,
            onRemoveClick: () -> Unit,
        ) = BossSavedOptionBottomSheet().apply {
            this.onRemoveClick = onRemoveClick
            arguments = Bundle().apply {
                putSerializable("candidateItem", candidateItem)
            }
        }
    }

    @Composable
    override fun DialogContent() {
        val candidateItem = candidateItem ?: return dismissSafely()
        BossSavedOptionContent(
            candidateItem = candidateItem,
            onCloseClick = {
                dismissSafely()
            },
            onRemoveClick = {
                dismissSafely()
                onRemoveClick?.invoke()
            }
        )
    }
}

@Composable
fun BossSavedOptionContent(
    candidateItem: CandidateItemResult,
    onCloseClick: () -> Unit = {},
    onRemoveClick: () -> Unit = {},
) {
    XTheme {
        Column(
            modifier = Modifier
                .background(
                    Color.White,
                    shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                )
        ) {
            Box(modifier = Modifier
                .fillMaxWidth()
                .height(58.dp)) {
                Text(
                    text = candidateItem.fullName,
                    style = TextStyle(
                        fontSize = 18.sp,
                        lineHeight = 26.sp,
                        fontWeight = FontWeight(590),
                        color = Black020202,
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .align(Alignment.Center)
                        .fillMaxWidth()
                        .padding(50.dp, 0.dp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )

                Image(painter = painterResource(id = R.drawable.ui_dailog_close),
                    contentDescription = "Close dialog",
                    modifier = Modifier
                        .noRippleClickable(onClick = onCloseClick)
                        .padding(end = 12.dp)
                        .size(24.dp)
                        .align(Alignment.CenterEnd)
                )
            }

            HorizontalDivider(
                thickness = 1.dp,
                color = BlackEBEBEB
            )

            Text(
                text = stringResource(id = R.string.common_remove),
                modifier = Modifier
                    .fillMaxWidth()
                    .noRippleClickable(onClick = onRemoveClick)
                    .padding(16.dp, 26.dp)
            )
        }
    }
}

@Preview
@Composable
private fun PreviewBossSavedOptionContent(
) {
    BossSavedOptionContent(
        CandidateItemResult(baseInfo = CandidateBaseInfo(firstName = "John",
                lastName = "Doe").apply {
        })
    )
}