package com.twl.meeboss.boss.module.job.home.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.module.job.manager.components.JobDeniedResultComponent
import com.twl.meeboss.core.ui.component.XDivider
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable

class InReviewWarningBottomSheet @JvmOverloads constructor(private val onOkClick: (() -> Unit)? = null) :
    CommonBottomDialogFragment() {
    @Composable
    override fun DialogContent() {
        InReviewWarningContent(
            onCloseClick = { dismissSafely() },
            onOkClick = {
                <EMAIL>?.invoke()
                dismissSafely()
            }
        )
    }
}

@Preview
@Composable
fun InReviewWarningContent(
    onCloseClick: () -> Unit = {},
    onOkClick: () -> Unit = {},
) {
    XTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    Color.White,
                    shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                )
                .padding(horizontal = 16.dp)
        ) {
            Image(
                painter = painterResource(id = R.drawable.ui_dailog_close),
                contentDescription = "Close dialog",
                modifier = Modifier
                    .padding(vertical = 20.dp)
                    .clickable {
                        onCloseClick()
                    }
                    .size(24.dp))
            Spacer(modifier = Modifier.height(17.dp))
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ui_wait_orange_icon),
                    contentDescription = ""
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = stringResource(id = R.string.job_in_review_title),
                    color = Black222222,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.W600,
                    textAlign = TextAlign.Center
                )
            }

            Spacer(modifier = Modifier.height(20.dp))

            Text(
                text = stringResource(id = R.string.job_in_review_tips),
                color = Black484848,
                fontSize = 14.sp,
                lineHeight = 20.sp
            )

            Spacer(modifier = Modifier.height(56.dp))
            XCommonButton(
                onClick = onOkClick,
                text = stringResource(id = R.string.common_ok)
            )
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}
