package com.twl.meeboss.boss.module.me.candidate.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.glide.GlideImage
import com.skydoves.landscapist.placeholder.placeholder.PlaceholderPlugin
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.model.CandidateItemResult
import com.twl.meeboss.boss.module.candidates.recommend.preview.CandidatesListPreviewData
import com.twl.meeboss.core.ui.component.layout.FlowLayout
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.COLOR_13C361
import com.twl.meeboss.core.ui.theme.COLOR_CCCCCC


@Composable
fun BossMyCandidateCardHeadArea(item: CandidateItemResult) {
    Row(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = item.fullName,
                    color = Black222222,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Medium
                )
                item.active?.let {
                    Text(
                        modifier = Modifier.padding(start = 8.dp),
                        text = it,
                        color = COLOR_13C361,
                        fontSize = 13.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            listOf(item.eduLevelDesc, item.locationDesc)
                .mapNotNull { it }
                .filter { it.isNotBlank() }
                .let {
                    FlowLayout(
                        modifier = Modifier.padding(top = 4.dp),
                        maxLine = 1
                    ) {
                        it.withIndex().forEach { (index, value) ->
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                if (index != 0) {
                                    VerticalDivider(
                                        modifier = Modifier
                                            .padding(horizontal = 8.dp)
                                            .height(10.dp),
                                        thickness = 0.5.dp,
                                        color = COLOR_CCCCCC
                                    )
                                }
                                Text(
                                    text = value,
                                    color = Black484848,
                                    fontSize = 13.sp,
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis
                                )
                            }
                        }
                    }
                }
        }
        GlideImage(
            imageModel = {
                item.baseInfo?.avatar
            },
            imageOptions = ImageOptions(
                contentScale = ContentScale.Crop,
                contentDescription = null
            ),
            component = rememberImageComponent {
                +PlaceholderPlugin.Loading(painterResource(id = R.mipmap.base_avatar_placeholder))
                +PlaceholderPlugin.Failure(painterResource(id = R.mipmap.base_avatar_placeholder))
            },
            modifier = Modifier
                .padding(start = 16.dp)
                .size(44.dp)
                .clip(RoundedCornerShape(22.dp)),
        )
    }
}

@Preview
@Composable
private fun PreviewBossMyCandidateCardHeadArea() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier.padding(vertical = 20.dp, horizontal = 16.dp)
        ) {
            BossMyCandidateCardHeadArea(CandidatesListPreviewData.candidateItemResult)
        }
    }
}