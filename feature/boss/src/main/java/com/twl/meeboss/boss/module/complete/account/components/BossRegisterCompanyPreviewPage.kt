package com.twl.meeboss.boss.module.complete.account.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.glide.GlideImage
import com.skydoves.landscapist.placeholder.placeholder.PlaceholderPlugin
import com.twl.meeboss.base.components.pagestate.XStatePage
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.base.ktx.openLinkInSystemBrowser
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.module.complete.account.viewmodel.BossRegisterCompanyPreviewUiState
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.theme.COLOR_888888
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.toResourceString

@Composable
fun BossRegisterCompanyPreviewPage(
    modifier: Modifier = Modifier
        .fillMaxSize()
        .background(Color.White),
    uiState: BossRegisterCompanyPreviewUiState,
    loadState: LoadState = LoadState.Success,
    onClickSave:()->Unit = {},
    onClickEditCompanyInfo:()->Unit = {},
    onRetryClick:()->Unit = {},
    onMediaPreviewClick: (index: Int) -> Unit = {},
) {
    val context = LocalContext.current
    XTheme {
        XStatePage(
            loadState = loadState,
            retryOnClick = onRetryClick
        ) {
            Column(
                modifier = modifier,
            ) {
                XTitleBar(title = stringResource(id = R.string.common_company_overview))

                Column(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .padding(top = 28.dp, start = 16.dp, end = 16.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    Box(modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(12.dp))
                        .background(COLOR_F5F5F5),
                    ) {

                        Column(modifier = Modifier
                            .padding(16.dp)
                            .fillMaxWidth()) {
                            Text(text = stringResource(id = R.string.boss_invitation_company_profile_title),
                                lineHeight = 18.sp,
                                fontSize = 16.sp, color = Black222222,
                                fontWeight = FontWeight.Medium)

                            Text(modifier = Modifier.padding(top = 8.dp),
                                text = stringResource(id = if (uiState.showEditCompanyInfoEntrance) R.string.boss_invitation_company_profile_subtitle else R.string.boss_initial_complete_company_profile_subtitle),
                                fontSize = 13.sp, color = Black484848,
                                lineHeight = 18.sp,
                                fontWeight = FontWeight.Normal)
                        }
                    }
                    Spacer(modifier = Modifier.height(28.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        GlideImage(
                            imageModel = { uiState.logo },
                            imageOptions = ImageOptions(contentDescription = "company logo", contentScale = ContentScale.Inside),
                            component = rememberImageComponent {
                                +PlaceholderPlugin.Loading(painterResource(id = R.drawable.base_company_default_logo))
                                +PlaceholderPlugin.Failure(painterResource(id = R.drawable.base_company_default_logo))
                            },
                            previewPlaceholder = painterResource(id = R.drawable.base_company_default_logo),
                            modifier = Modifier
                                .size(48.dp)
                                .clip(RoundedCornerShape(6.dp))
                        )

                        Column(modifier = Modifier
                            .weight(1f)
                            .padding(start = 13.dp)) {
                            if (!uiState.abbreviation.isNullOrBlank()) {
                                Text(
                                    text = uiState.abbreviation?:"",
                                    style = TextStyle(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight(500),
                                        color = Black222222,
                                    )
                                )
                                Spacer(modifier = Modifier.height(4.dp))
                            }

                            Text(
                                text = uiState.companyName?:"",
                                style = TextStyle(
                                    fontSize = 13.sp,
                                    fontWeight = FontWeight(400),
                                    color = COLOR_484848,
                                )
                            )

                            Text(
                                text = listOf(uiState.industry, uiState.companyNumber).joinToString(" · "),
                                modifier = Modifier.padding(top = 2.dp),
                                style = TextStyle(
                                    fontSize = 13.sp,
                                    fontWeight = FontWeight(400),
                                    color = COLOR_888888,
                                )
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(28.dp))
                    HorizontalDivider(thickness = 1.dp, color = BlackEBEBEB)

                    if (!uiState.companyDescription.isNullOrBlank()) {
                        Spacer(modifier = Modifier.height(28.dp))
                        Text(
                            text = stringResource(id = R.string.common_company_description),
                            modifier = Modifier.padding(top = 2.dp),
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF222222),
                            )
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = uiState.companyDescription?:"",
                            style = TextStyle(
                                fontSize = 15.sp,
                                lineHeight = 23.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFF484848),
                            )
                        )
                        Spacer(modifier = Modifier.height(28.dp))
                        HorizontalDivider(thickness = 1.dp, color = BlackEBEBEB)
                    }

                    if (!uiState.companyWebsite.isNullOrBlank()) {
                        Spacer(modifier = Modifier.height(28.dp))
                        Text(
                            text = stringResource(id = R.string.common_company_website),
                            modifier = Modifier.padding(top = 2.dp),
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF222222),
                            )
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        SelectionContainer {
                            Text(
                                modifier = Modifier.noRippleClickable(preventDoubleClick = true) {
                                    uiState.companyWebsite.openLinkInSystemBrowser(context = context) {
                                        T.ss(com.twl.meeboss.base.R.string.companies_website_click_no_effect.toResourceString())
                                    }
                                },
                                text = uiState.companyWebsite?:"",
                                textDecoration = TextDecoration.Underline,
                                style = TextStyle(
                                    fontSize = 15.sp,
                                    lineHeight = 23.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF484848),
                                )
                            )
                        }
                        Spacer(modifier = Modifier.height(28.dp))
                        HorizontalDivider(thickness = 1.dp, color = BlackEBEBEB)
                    }

                    if (uiState.companyEnvironments.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(26.dp))
                        Text(
                            text = stringResource(id = com.twl.meeboss.base.R.string.common_company_environment),
                            modifier = Modifier.padding(top = 2.dp),
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF222222),
                            )
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        LazyRow(
                            modifier = Modifier
                                .fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            itemsIndexed(uiState.companyEnvironments) { index, item ->
                                GlideImage(
                                    imageModel = { item },
                                    imageOptions = ImageOptions(
                                        contentDescription = "company logo",
                                        contentScale = ContentScale.Crop
                                    ),
                                    modifier = Modifier
                                        .width(280.dp)
                                        .height(156.dp)
                                        .clip(RoundedCornerShape(12.dp))
                                        .noRippleClickable {
                                            onMediaPreviewClick(index)
                                        },
                                    loading = {
                                        Image(
                                            modifier = Modifier
                                                .fillMaxSize()
                                                .background(COLOR_F5F5F5),
                                            painter = painterResource(id = com.twl.meeboss.base.R.drawable.ui_pic_default),
                                            contentScale = ContentScale.Inside,
                                            contentDescription = null
                                        )
                                    },
                                    failure = {
                                        Image(
                                            modifier = Modifier
                                                .fillMaxSize()
                                                .background(COLOR_F5F5F5),
                                            painter = painterResource(id = com.twl.meeboss.base.R.drawable.ui_pic_default),
                                            contentScale = ContentScale.Inside,
                                            contentDescription = null
                                        )
                                    }
                                )
                            }
                        }

                    }
                }

                XCommonButton(
                    text = stringResource(id = R.string.common_join),
                    modifier = Modifier.padding(16.dp),
                    onClick = {
                        onClickSave()
                    })

                if (uiState.showEditCompanyInfoEntrance) {
                    Spacer(modifier = Modifier.height(6.dp))
                    Text(
                        text = stringResource(id = R.string.boss_invitation_not_my_company_button),
                        style = TextStyle(
                            fontSize = 16.sp,
                            lineHeight = 24.sp,
                            fontWeight = FontWeight(590),
                            color = Color(0xFF222222),
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier
                            .padding(bottom = 20.dp)
                            .noRippleClickable(onClick = onClickEditCompanyInfo)
                            .align(Alignment.CenterHorizontally)
                    )
                }

            }
        }
    }
}

@Preview
@Composable
private fun PreviewBossRegisterCompanyPreviewPageInvite() {
    BossRegisterCompanyPreviewPage(
        uiState = BossRegisterCompanyPreviewUiState(
            logo = "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png",
            abbreviation = "abbreviation",
            companyName = "companyName",
            industry = "industry",
            companyNumber = "companyNumber",
            companyDescription = stringResource(id = R.string.ui_common_long_place_holder),
            companyWebsite = "companyWebsite",
            showEditCompanyInfoEntrance = true,
        )
    )
}

@Preview
@Composable
private fun PreviewBossRegisterCompanyPreviewPageLocalData() {
    BossRegisterCompanyPreviewPage(
        uiState = BossRegisterCompanyPreviewUiState(
            logo = "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png",
            abbreviation = "abbreviation",
            companyName = "companyName",
            industry = "industry",
            companyNumber = "companyNumber",
            companyDescription = stringResource(id = R.string.ui_common_long_place_holder),
            companyWebsite = "companyWebsite",
            showEditCompanyInfoEntrance = false,
        )
    )
}