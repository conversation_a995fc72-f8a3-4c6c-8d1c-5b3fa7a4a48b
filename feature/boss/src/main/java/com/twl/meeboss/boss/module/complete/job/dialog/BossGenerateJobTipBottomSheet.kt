package com.twl.meeboss.boss.module.complete.job.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Divider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.protocol.ProtocolHelper
import com.twl.meeboss.boss.R
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.URLUtils
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.webview.export.ProtocolId
import com.twl.meeboss.webview.export.WebUrls

class BossGenerateJobTipBottomSheet : CommonBottomDialogFragment() {
    companion object {
        fun newInstance(): BossGenerateJobTipBottomSheet {
            return BossGenerateJobTipBottomSheet()
        }
    }
    @Composable
    override fun DialogContent() {
        BossGenerateJobTipBottomSheetComponent(
            onCloseClick = {
                dismissSafely()
            },
            onTermsClick = {
                dismissSafely()
                //产品需求要求跳转到页面的固定位置，所以H5需要一个参数
                val termsOfServiceUrl = URLUtils.appendParamsToUrl(
                    WebUrls.getProtocolH5Url(ProtocolId.TERMS_OF_SERVICE), mapOf(
                        "section" to "section-3",
                    )
                )
                ProtocolHelper.parseProtocol(termsOfServiceUrl)
            }
        )
    }
}

@Composable
fun BossGenerateJobTipBottomSheetComponent(
    onCloseClick:()->Unit = {},
    onTermsClick:()->Unit = {},
) {
    val context = LocalContext.current
    Column(modifier = Modifier
        .fillMaxWidth()
        .imePadding()
        .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
        .background(Color.White)) {

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(60.dp)
        ) {

            Image(
                painter = painterResource(id = R.drawable.ui_dailog_close),
                modifier = Modifier
                    .padding(16.dp, 0.dp)
                    .size(24.dp)
                    .align(Alignment.CenterStart)
                    .noRippleClickable(onClick = onCloseClick),
                contentDescription = "Close dialog"
            )
            Text(
                text = stringResource(id = R.string.boss_ai_generate_job_tips),
                fontSize = 16.sp,
                modifier = Modifier
                    .padding(16.dp, 0.dp)
                    .align(Alignment.Center),
                textAlign = TextAlign.Center,
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                color = Black222222,
                fontWeight = FontWeight.Medium
            )
        }

        Divider(modifier = Modifier
            .fillMaxWidth()
            .height(1.dp)
            .background(BlackEBEBEB))

        val prompt1 = R.string.boss_ai_generate_job_legal_prompt.toResourceString(context)
        val prompt2 = R.string.boss_ai_generate_job_legal_prompt2.toResourceString(context)
        val originStr = prompt1 + prompt2
        val termsStr = R.string.login_terms_of_service.toResourceString(context)
        val termsIndex = originStr.indexOf(termsStr)
        val annotatedString = buildAnnotatedString {
            if (termsIndex < 0) {
                append(originStr)
                XLog.error("BossGenerateJobTipBottomSheetComponent","buildAnnotatedString failed:${originStr}")
            } else {
                append(originStr.substring(0, termsIndex))
                withStyle(
                    style = SpanStyle(
                        color = Black222222,
                        textDecoration = TextDecoration.Underline,
                    )
                ) {
                    append(termsStr)
                }
                append(originStr.substring(termsIndex + termsStr.length))
            }
        }

        ClickableText(
            text = annotatedString,
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 21.sp,
                fontWeight = FontWeight.W400,
                color = Black222222,
            ),
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .padding(32.dp, 16.dp)
        ) {
            if (it in termsIndex until termsIndex + termsStr.length) {
                onTermsClick()
            }
        }

    }
}

@Preview
@Composable
private fun PreviewBossGenerateJobTipBottomSheetComponent() {
    BossGenerateJobTipBottomSheetComponent()
}