package com.twl.meeboss.boss.module.candidates.recommend.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.boss.R
import com.twl.meeboss.core.ui.component.button.XCommonMidButton
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_484848

@Composable
fun BossCandidateNotificationCard(
    modifier: Modifier = Modifier,
    onEnableNotificationClick:()->Unit = {},
    enable: Boolean = false
) {
    Column(
        modifier = modifier
            .background(Color.White, shape = RoundedCornerShape(12.dp))
            .padding(bottom = 28.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            modifier = Modifier
                .fillMaxWidth(),
            painter = painterResource(id = R.mipmap.boss_candidate_notification),
            contentDescription = null,
            contentScale = ContentScale.FillWidth
        )
        Text(
            modifier = Modifier
                .width(255.dp)
                .padding(top = 20.dp),
            text = stringResource(R.string.candidates_recommendation_empty_list_title),
            style = TextStyle(
                fontSize = 16.sp,
                lineHeight = 24.sp,
                fontWeight = FontWeight(590),
                color = COLOR_222222,
                textAlign = TextAlign.Center
            )
        )

        Text(
            modifier = Modifier
                .padding(top = 8.dp)
                .padding(horizontal = 20.dp),
            text = if(enable){
                stringResource(id = R.string.candidates_recommendation_empty_list_subtext_post_action)
            }else {
                stringResource(id = R.string.candidates_recommendation_empty_list_subtext)
            },
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 21.sp,
                fontWeight = FontWeight(400),
                color = COLOR_484848,
                textAlign = TextAlign.Center,
            )
        )
        if (!enable) {
            XCommonMidButton(
                modifier = Modifier.padding(top = 20.dp),
                onClick = onEnableNotificationClick,
                buttonText = stringResource(id = R.string.candidates_recommendation_empty_list_action)
            )
        }

    }
}

@Preview
@Composable
fun PreviewBossCandidateNotificationCard() {
    BossCandidateNotificationCard(Modifier.padding(horizontal = 12.dp, vertical = 16.dp))
}

@Preview
@Composable
fun PreviewBossCandidateNotificationCard1() {
    BossCandidateNotificationCard(
        Modifier.padding(horizontal = 12.dp, vertical = 16.dp),
        enable = true
    )
}

