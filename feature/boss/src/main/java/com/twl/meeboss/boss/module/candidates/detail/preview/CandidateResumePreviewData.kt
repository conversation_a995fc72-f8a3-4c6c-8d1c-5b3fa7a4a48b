package com.twl.meeboss.boss.module.candidates.detail.preview

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.twl.meeboss.base.model.common.CommonTypeBean
import com.twl.meeboss.export_share.model.CandidateResumeBaseInfo
import com.twl.meeboss.export_share.model.CandidateResumeCertificate
import com.twl.meeboss.export_share.model.CandidateResumeCity
import com.twl.meeboss.export_share.model.CandidateResumeEduExp
import com.twl.meeboss.export_share.model.CandidateResumeLanguage
import com.twl.meeboss.export_share.model.CandidateResumeProjectExp
import com.twl.meeboss.export_share.model.CandidateResumeResult
import com.twl.meeboss.export_share.model.CandidateResumeWorkExp
import com.twl.meeboss.export_share.topmatches.TopHighlyMatchedDetailModel
import java.util.UUID
import kotlin.random.Random

/**
 * @author: 冯智健
 * @date: 2024年07月18日 15:12
 * @description:
 */
object CandidateResumePreviewData {
    val candidateResume = CandidateResumeResult(
        securityId = UUID.randomUUID().toString(),
        friended = true,
        favorite = true,
        baseInfo = CandidateResumeBaseInfo(
            avatar = "https://img2.baidu.com/it/u=1459566101,1482467068&fm=253&fmt=auto&app=138&f=JPEG?w=400&h=400",
            firstName = "William William William William",
            lastName = "Zhang Zhang Zhang Zhang",
            city = CandidateResumeCity(
                code = Random.nextLong(),
                name = "",
                city = "Hong Kong",
                state = "",
                shortState = "",
                country = "",
                shortCountry = ""
            )
        ),
        highestEduLevelDesc = "Interaction designer cInteraction design erInter action designer cInteraction ",
        expLevelDesc = "",
        companyName = "",
        jobTitle = "",
        workExpList = List(3) {
            CandidateResumeWorkExp(
                id = UUID.randomUUID().toString(),
                jobCategoryCode = Random.nextLong(),
                jobTitle = "Business Operations Executive",
                companyId = UUID.randomUUID().toString(),
                companyName = "Motor Vehicle Manufacturing",
                companyLogo = "https://img2.baidu.com/it/u=1459566101,1482467068&fm=253&fmt=auto&app=138&f=JPEG?w=400&h=400",
                startYear = 2023,
                startMonth = 1,
                endYear = 2024,
                endMonth = 5,
                description = "Working closely with quality developers and designers to del iver " +
                        "the product design and it eration. Worked and helping"
            )
        },
        projectExpList = List(3) {
            CandidateResumeProjectExp(
                id = UUID.randomUUID().toString(),
                projectName = "OfferToday",
                jobCategoryCode = Random.nextLong(),
                jobTitle = "UX designer",
                startYear = 2023,
                startMonth = 1,
                endYear = 2024,
                endMonth = 5,
                description = "Working closely with quality developers and designers to deliver " +
                        "the product design and it eration. Worked and helping",
                performance = "Working closely with quality developers and designers to deliver " +
                        "the product design and it eration. Worked and helping"
            )
        },
        eduExpList = List(3) {
            CandidateResumeEduExp(
                id = UUID.randomUUID().toString(),
                schoolCode =  Random.nextLong(),
                schoolName = "University of the arts London",
                schoolLogo = "https://img2.baidu.com/it/u=1459566101,1482467068&fm=253&fmt=auto&app=138&f=JPEG?w=400&h=400",
                eduLevel = Random.nextInt(),
                eduLevelDesc = "MA",
                majorCode = Random.nextLong(),
                majorName = "Interaction design communication",
                startYear = 2023,
                startMonth = 1,
                endYear = 2024,
                endMonth = 5,
                description = "Working closely with quality developers and designers to del iver " +
                        "the product design and it eration. Worked and helping",
            )
        },
        languages = listOf(
            CandidateResumeLanguage(
                id = UUID.randomUUID().toString(),
                code = Random.nextLong(),
                name = "English",
                fluencyCode = Random.nextLong(),
                fluencyName = "Expert"
            ),
            CandidateResumeLanguage(
                id = UUID.randomUUID().toString(),
                code = Random.nextLong(),
                name = "Chinese",
                fluencyCode = Random.nextLong(),
                fluencyName = "Expert"
            ),
            CandidateResumeLanguage(
                id = UUID.randomUUID().toString(),
                code = Random.nextLong(),
                name = "Japanese",
                fluencyCode = Random.nextLong(),
                fluencyName = "Expert"
            )
        ),
        certificates = List(5) {
            CandidateResumeCertificate(
                id = UUID.randomUUID().toString(),
                code = Random.nextLong(),
                name = "Project Management",
                expirationYear = 2024,
                expirationMonth = 12
            )
        },
        skills = List(5) {
            CommonTypeBean(code = Random.nextLong(), name = "Graphic Design")
        },
        comment = "Hi there! I'm truly excited about the opportunity to join your team at InnovateTech. I believe my skills in software development, particularly in creating innovative solutions and collaborating with diverse teams, would be a fantastic fit for your projects. I look forward to contributing to your success!",
        interested = 1
    )
}

class CandidateResumePreviewParameterProvider : PreviewParameterProvider<CandidateResumeResult> {
    override val values: Sequence<CandidateResumeResult> = sequenceOf(
        CandidateResumePreviewData.candidateResume
    )
}

class TopMatchesCandidateResumePreviewParameterProvider : PreviewParameterProvider<Pair<TopHighlyMatchedDetailModel, CandidateResumeResult>> {
    override val values: Sequence<Pair<TopHighlyMatchedDetailModel, CandidateResumeResult>> = sequenceOf(
        TopHighlyMatchedDetailModel("", "1.5+ years of product management experience in mobile app development, aligned with the job’s core focus.  2. Led cross-functional teams and launched features with >1M users—matches the scale and impact expected.")
                to
        CandidateResumePreviewData.candidateResume
    )
}