package com.twl.meeboss.boss.module.complete.account.activity

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.components.form.XCommonFormText
import com.twl.meeboss.base.components.form.XCommonFormTextField
import com.twl.meeboss.base.components.position.CommonSearchPositionBottomSheet
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.components.titlebar.XTitleBarRightMoreWithPopup
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.showRegisterRightMoreDialog
import com.twl.meeboss.base.ktx.subString
import com.twl.meeboss.base.main.router.BaseServiceRouter
import com.twl.meeboss.base.model.common.GetUserInfoEvent
import com.twl.meeboss.base.mudule.ModuleManager
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.boss.export.BossRouterPath
import com.twl.meeboss.boss.module.complete.account.model.BossRegisterPointerBean
import com.twl.meeboss.boss.module.complete.account.model.BossRegisterProfileSaveResult
import com.twl.meeboss.boss.module.complete.account.viewmodel.BossRegisterProfileUiIntent
import com.twl.meeboss.boss.module.complete.account.viewmodel.BossRegisterProfileUiState
import com.twl.meeboss.boss.module.complete.account.viewmodel.BossRegisterProfileViewModel
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.dialog.showConfirmDialog
import com.twl.meeboss.core.ui.theme.COLOR_000000
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.login.export.LoginRouterPath
import com.twl.meeboss.login.export.LoginServiceRouter
import dagger.hilt.android.AndroidEntryPoint


@RouterPage(path = [BossRouterPath.BOSS_REGISTER_PROFILE_PAGE])
@AndroidEntryPoint
class BossRegisterProfileActivity() : BaseMviActivity<BossRegisterProfileViewModel>() {

    private val REQ_CODE_EMAIL_VERIFY = 1001
    override val viewModel: BossRegisterProfileViewModel by viewModels()
    private var needObserveUserInfo = false //避免粘性event造成跳转错误
    private val hasBack by lazy {
        intent.getBooleanExtra(BossPageRouter.BUNDLE_BOSS_REGISTER_HAS_BACK, true)
    }
    private val hasRightMore by lazy {
        intent.getBooleanExtra(BossPageRouter.BUNDLE_BOSS_REGISTER_HAS_MORE, false)
    }

    override fun preInit(intent: Intent) {
        viewModel.showConfirmDialog.observe(this) {
            if (it) {
                showConfirmDialog()
            }
        }
        viewModel.emailConflictAccount.observe(this) {
            showConfirmDialog(
                title = R.string.boss_register_company_email_be_bound_pop_up_title.toResourceString(),
                content = R.string.boss_register_company_email_be_bound_pop_up_content.toResourceString(it),
                confirmText = R.string.common_continue.toResourceString(),
                onConfirm = {
                    showConfirmDialog()
                }
            )
        }
        viewModel.saveResultBean.observe(this) {
            BossPointReporter.employerAccountNext(it.hasCompanyHomepage, BossRegisterPointerBean.workEmail?:"")
            jumpToNextPage(it)
        }
        liveEventBusObserve<GetUserInfoEvent>(EventBusKey.GET_USER_INFO_FINISH) {
            XLog.info(TAG,"GET_USER_INFO_FINISH userInfo:${needObserveUserInfo}")
            if (needObserveUserInfo) {
                BaseServiceRouter.afterLogin(this)
            }
        }
    }

    private fun showConfirmDialog() {
        showConfirmDialog(
            title = R.string.boss_register_email_confirm_dialog_title.toResourceString(),
            content = R.string.boss_register_email_confirm_dialog_content.toResourceString(),
            cancelText = R.string.common_button_cancel.toResourceString(),
            confirmText = R.string.common_button_confirm.toResourceString(),
            onConfirm = {
                viewModel.sendUiIntent(BossRegisterProfileUiIntent.Save)
            }
        )
    }

    override fun initData() {
        viewModel.sendUiIntent(BossRegisterProfileUiIntent.Init)
    }

    private fun jumpToCommonVerificationCodeActivity() {
        LoginServiceRouter.jumpToCommonVerificationCodeActivity(
            activity = this@BossRegisterProfileActivity,
            account = viewModel.uiStateFlow.value.workEmail,
            isPhone = false,
            sceneType = LoginRouterPath.LOGIN_BOSS_COMPLETE_PROFILE_VERIFY_EMAIL,
            requestCode = REQ_CODE_EMAIL_VERIFY,
        )
    }

    private fun jumpToNextPage(saveResultBean: BossRegisterProfileSaveResult) {
        when {
            saveResultBean.needEmailVerify -> {
                jumpToCommonVerificationCodeActivity()
            }
            saveResultBean.needActive -> {
                //进F1
                AccountManager.setFirstCompleteStatus(UserConstants.COMPLETE_STATUS_ALREADY_COMPLETE)
                ModuleManager.updateUserInfo()
                needObserveUserInfo = true
            }
            saveResultBean.hasCompanyHomepage -> {
                BossPageRouter.jumpToBossRegisterCompanyPreviewActivity(context = this)
            }
            else -> {
                BossPageRouter.jumpToBossRegisterCompanyInfoActivity(this)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        val saveResultBean = viewModel.saveResultBean.value
        if (requestCode == REQ_CODE_EMAIL_VERIFY && resultCode == RESULT_OK && saveResultBean != null) {
            jumpToNextPage(saveResultBean.copy(needEmailVerify = false))
        }
    }

    private fun onPositionClick() {
        CommonSearchPositionBottomSheet.newInstance(viewModel.uiStateFlow.value.companyPosition) {
            viewModel.sendUiIntent(BossRegisterProfileUiIntent.OnCompanyPositionChange(it.name))
        }.showSafely(this)
    }

    private fun showRightMoreDialog(rect: Rect) {
        showRegisterRightMoreDialog(
            rect,
        )
    }

    @Composable
    override fun ComposeContent() {
        val uiState = viewModel.uiStateFlow.collectAsStateWithLifecycle().value
        BossRegisterProfileContent(
            uiState = uiState,
            hasBack = hasBack,
            hasRightMore = hasRightMore,
            onClickRightMore = this::showRightMoreDialog,
            onFirstNameChange = {
                viewModel.sendUiIntent(BossRegisterProfileUiIntent.OnFirstNameChange(it))
            },
            onLastNameChange = {
                viewModel.sendUiIntent(BossRegisterProfileUiIntent.OnLastNameChange(it))
            },
            onClickCompanyPosition = this::onPositionClick,
            onWorkEmailChange = {
                viewModel.sendUiIntent(BossRegisterProfileUiIntent.OnWorkEmailChange(it))
            },
            onSaveClick = {
                BossRegisterPointerBean.workEmail = uiState.workEmail
                viewModel.sendUiIntent(BossRegisterProfileUiIntent.CheckEmail)
            }
        )
    }

}

@Composable
fun BossRegisterProfileContent(
    modifier: Modifier = Modifier
        .fillMaxSize()
        .background(Color.White),
    uiState: BossRegisterProfileUiState,
    hasBack:Boolean = true,
    hasRightMore: Boolean = false,
    onClickRightMore: (Rect) -> Unit = {},
    onFirstNameChange: (String) -> Unit = {},
    onLastNameChange: (String) -> Unit = {},
    onClickCompanyPosition: () -> Unit = {},
    onWorkEmailChange: (String) -> Unit = {},
    onSaveClick: () -> Unit = {},
) {
    val firstNameField = remember { mutableStateOf(TextFieldValue()) }
    val lastNameField = remember { mutableStateOf(TextFieldValue()) }
    val workEmailField = remember { mutableStateOf(TextFieldValue()) }

    LaunchedEffect(key1 = uiState.firstName, key2 = uiState.lastName, key3 = uiState.workEmail) {
        firstNameField.value = TextFieldValue(
            uiState.firstName,
            TextRange(uiState.firstName.length)
        )
        lastNameField.value = TextFieldValue(
            uiState.lastName,
            TextRange(uiState.lastName.length)
        )
        workEmailField.value = TextFieldValue(
            uiState.workEmail,
            TextRange(uiState.workEmail.length)
        )
    }

    val focusManager = LocalFocusManager.current

    XTheme {
        Column(
            modifier = modifier
        ) {
            XTitleBar(
                showBackIcon = hasBack,
                rightContent = {
                    if (hasRightMore) {
                        XTitleBarRightMoreWithPopup {
                            onClickRightMore(it)
                        }
                    }
                }
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(16.dp, 0.dp)
                    .verticalScroll(rememberScrollState(), true)
            ) {
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = stringResource(id = R.string.common_boss_create_an_employer_account),
                    style = TextStyle(
                        fontSize = 28.sp,
                        fontWeight = FontWeight.W500,
                        color = COLOR_000000,
                    )
                )

                XCommonFormTextField(
                    modifier = Modifier.padding(top = 20.dp),
                    title = stringResource(id = R.string.common_first_name),
                    placeHolder = stringResource(id = R.string.common_enter_first_name),
                    content = firstNameField.value,
                    singleLine = true
                ) {
                    it.subString(DefaultValueConstants.MAX_USER_NAME_LENGTH, true)
                        .let { firstName ->
                            firstNameField.value = firstName
                            onFirstNameChange(firstName.text)
                        }
                }

                XCommonFormTextField(
                    title = stringResource(id = R.string.common_last_name),
                    placeHolder = stringResource(id = R.string.common_enter_last_name),
                    content = lastNameField.value,
                    singleLine = true
                ) {
                    it.subString(DefaultValueConstants.MAX_USER_NAME_LENGTH, true).let { lastName ->
                        lastNameField.value = lastName
                        onLastNameChange(lastName.text)
                    }
                }

                XCommonFormText(
                    modifier = Modifier.noRippleClickable(onClick = {
                        focusManager.clearFocus()
                        onClickCompanyPosition()
                    }),
                    title = stringResource(id = R.string.common_my_position_in_the_company),
                    placeHolder = stringResource(id = R.string.common_enter_the_company_position),
                    content = uiState.companyPosition,
                )

                XCommonFormTextField(
                    title = stringResource(id = R.string.boss_invitation_work_email_field),
                    placeHolder = stringResource(id = R.string.boss_invitation_work_email_placeholer),
                    content = workEmailField.value,
                    singleLine = true
                ) {
                    it.subString(DefaultValueConstants.MAX_INPUT_COUNT_EMAIL, true)
                        .let { workEmail ->
                            workEmailField.value = workEmail
                            onWorkEmailChange(workEmail.text)
                        }
                }

            }

            XCommonButton(
                modifier = Modifier.padding(16.dp),
                text = stringResource(id = R.string.common_next),
                enabled = uiState.canSave,
                onClick = onSaveClick
            )
        }
    }
}

@Preview
@Composable
private fun PreviewBossRegisterProfileContent() {
    BossRegisterProfileContent(
        uiState = BossRegisterProfileUiState()
    )

}