package com.twl.meeboss.boss.module.me.personal

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.glide.GlideImage
import com.skydoves.landscapist.placeholder.placeholder.PlaceholderPlugin
import com.twl.meeboss.base.components.form.XCommonFormText
import com.twl.meeboss.base.components.form.XCommonFormTextField
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.ktx.subString
import com.twl.meeboss.boss.R
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable

/**
 * @author: 冯智健
 * @date: 2024年08月08日 14:14
 * @description:
 */
@Preview
@Composable
fun BossPersonalInfoPage(
    uiState: BossPersonalInfoUIState = BossPersonalInfoUIState(),
    onAvatarClick: () -> Unit = {},
    onFirstNameChange: (String) -> Unit = {},
    onLastNameChange: (String) -> Unit = {},
    onPositionClick: (String) -> Unit = {},
    onCompanyNameClick: () -> Unit = {},
    onSaveClick: () -> Unit = {}
) {
    val firstNameField = remember {
        mutableStateOf(TextFieldValue(uiState.firstName, TextRange(uiState.firstName.length)))
    }
    val lastNameField = remember {
        mutableStateOf(TextFieldValue(uiState.lastName, TextRange(uiState.lastName.length)))
    }
    XTheme {
        Column(modifier = Modifier
            .background(Color.White)
            .fillMaxSize()) {
            XTitleBar()
            Column(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
                    .weight(1F),
            ) {
                Text(
                    modifier = Modifier.padding(top = 12.dp),
                    text = stringResource(id = R.string.common_boss_personal_information),
                    fontWeight = FontWeight.SemiBold,
                    fontSize = 28.sp
                )
                Box(
                    modifier = Modifier
                        .padding(top = 24.dp)
                        .noRippleClickable {
                            onAvatarClick()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    GlideImage(
                        modifier = Modifier
                            .size(100.dp)
                            .clip(RoundedCornerShape(50.dp)),
                        imageModel = {
                            uiState.avatar
                        },
                        imageOptions = ImageOptions(
                            contentScale = ContentScale.Crop,
                            contentDescription = null
                        ),
                        previewPlaceholder = painterResource(id = R.mipmap.base_avatar_placeholder),
                        component = rememberImageComponent {
                            +PlaceholderPlugin.Loading(painterResource(id = R.mipmap.base_avatar_placeholder))
                            +PlaceholderPlugin.Failure(painterResource(id = R.mipmap.base_avatar_placeholder))
                        }
                    )
                    Icon(
                        modifier = Modifier.align(Alignment.BottomEnd),
                        imageVector = ImageVector.vectorResource(id = R.drawable.ui_edit_icon),
                        contentDescription = null,
                        tint = Color.Unspecified
                    )
                }
                XCommonFormTextField(
                    modifier = Modifier.padding(top = 44.dp),
                    title = stringResource(id = R.string.common_first_name),
                    placeHolder = stringResource(id = R.string.common_enter_first_name),
                    content = firstNameField.value,
                    singleLine = true
                ) {
                    it.subString(DefaultValueConstants.MAX_USER_NAME_LENGTH, true).let { name->
                        firstNameField.value = name
                        onFirstNameChange(name.text)
                    }
                }
                XCommonFormTextField(
                    title = stringResource(id = R.string.common_last_name),
                    placeHolder = stringResource(id = R.string.common_enter_last_name),
                    content = lastNameField.value,
                    singleLine = true
                ) {
                    it.subString(DefaultValueConstants.MAX_USER_NAME_LENGTH, true).let { name->
                        lastNameField.value = name
                        onLastNameChange(name.text)
                    }
                }

                XCommonFormText(
                    title = stringResource(id = R.string.common_work_email_address),
                    content = uiState.workEmail,
                    enable = false
                )
                XCommonFormText(
                    modifier = Modifier.noRippleClickable {
                        onPositionClick(uiState.position)
                    },
                    title = stringResource(id = R.string.common_my_position_in_the_company),
                    placeHolder = stringResource(id = R.string.common_enter_the_company_position),
                    content = uiState.position,
                    )

                XCommonFormText(
                    modifier = Modifier.noRippleClickable(onClick = onCompanyNameClick),
                    title = stringResource(id = R.string.common_company_name),
                    content = uiState.companyName,
                )
            }
            XCommonButton(
                modifier = Modifier.padding(16.dp),
                text = stringResource(id = R.string.common_button_save),
                enabled = uiState.saveEnable,
                onClick = onSaveClick
            )
        }
    }
}
