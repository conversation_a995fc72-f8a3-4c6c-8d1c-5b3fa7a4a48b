package com.twl.meeboss.boss.module.job.post.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.os.bundleOf
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.module.complete.ai.BossCompleteChooseType
import com.twl.meeboss.boss.module.complete.ai.activity.BossCompleteChooseTypeItem
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black020202
import com.twl.meeboss.core.ui.theme.COLOR_DDDDDD
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable

class JobQuickPostBottomSheet : CommonBottomDialogFragment() {
    private val showTemplate: Boolean by lazy {
        arguments?.getBoolean("showTemplate") ?: false
    }
    private var onClickItem: ((@BossCompleteChooseType Int) -> Unit) ?= null

    companion object {
        fun newInstance(
            showTemplate: Boolean = false,
            onClickItem: (@BossCompleteChooseType Int) -> Unit = {}
        ) = JobQuickPostBottomSheet().apply {
            this.onClickItem = onClickItem
             arguments = bundleOf(
                "showTemplate" to showTemplate
            )
        }
    }
    @Composable
    override fun DialogContent() {
        JobQuickPostContent(
            showTemplate = showTemplate,
            onClickClose = { dismissSafely() },
            onClickItem = {
                dismissSafely()
                onClickItem?.invoke(it)
            },
        )
    }
}

@Preview
@Composable
fun JobQuickPostContent(
    showTemplate: Boolean = true,
    onClickClose: () -> Unit = {},
    onClickItem: (@BossCompleteChooseType Int) -> Unit = {}
) {
    XTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                .background(Color.White)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(60.dp)
            ) {

                Image(
                    painter = painterResource(id = R.drawable.ui_dailog_close),
                    modifier = Modifier
                        .padding(16.dp, 0.dp)
                        .size(24.dp)
                        .align(Alignment.CenterStart)
                        .noRippleClickable(onClick = onClickClose),
                    contentDescription = "Close dialog"
                )
                Text(
                    text = stringResource(id = R.string.boss_post_job_ai_quick_post),
                    fontSize = 16.sp,
                    modifier = Modifier
                        .align(Alignment.Center),
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    color = Black020202,
                    fontWeight = FontWeight.Medium
                )
            }

            HorizontalDivider(thickness = 1.dp, color = COLOR_DDDDDD)

            BossCompleteChooseTypeItem(
                modifier = Modifier.padding(20.dp, 20.dp),
                type = BossCompleteChooseType.TYPE_LINK,
                showNext = false,
                onClick = {
                    onClickItem(BossCompleteChooseType.TYPE_LINK)
                },
            )
            HorizontalDivider(
                modifier = Modifier.padding(20.dp, 0.dp),
                thickness = 1.dp,
                color = COLOR_DDDDDD
            )


            BossCompleteChooseTypeItem(
                modifier = Modifier.padding(20.dp, 30.dp),
                type = BossCompleteChooseType.TYPE_JOB_DESCRIPTION,
                showNext = false,
                onClick = {
                    onClickItem(BossCompleteChooseType.TYPE_JOB_DESCRIPTION)
                },
            )
            HorizontalDivider(
                modifier = Modifier.padding(20.dp, 0.dp),
                thickness = 1.dp,
                color = COLOR_DDDDDD
            )

            BossCompleteChooseTypeItem(
                modifier = Modifier.padding(20.dp, 30.dp),
                type = BossCompleteChooseType.TYPE_KEYWORDS,
                showNext = false,
                onClick = {
                    onClickItem(BossCompleteChooseType.TYPE_KEYWORDS)
                },
            )
            if (showTemplate) {
                HorizontalDivider(
                    modifier = Modifier.padding(20.dp, 0.dp),
                    thickness = 1.dp,
                    color = COLOR_DDDDDD
                )

                BossCompleteChooseTypeItem(
                    modifier = Modifier.padding(20.dp, 30.dp),
                    type = BossCompleteChooseType.TYPE_TEMPLATE,
                    showNext = false,
                    onClick = {
                        onClickItem(BossCompleteChooseType.TYPE_TEMPLATE)
                    },
                )
            }
        }
    }
}