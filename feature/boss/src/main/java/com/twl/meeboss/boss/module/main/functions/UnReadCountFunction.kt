package com.twl.meeboss.boss.module.main.functions

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.twl.meeboss.base.function.AbsFunction
import com.twl.meeboss.base.ktx.launcherOnIO
import com.twl.meeboss.base.manager.BadgerManager
import com.twl.meeboss.chat.export.ChatServiceRouter
import kotlinx.coroutines.flow.collectLatest

class UnReadCountFunction(): AbsFunction() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        owner.lifecycleScope.launcherOnIO {
            ChatServiceRouter.getUnreadCount().collectLatest {
                BadgerManager.setBadgeCount(it)
            }
        }
    }
}