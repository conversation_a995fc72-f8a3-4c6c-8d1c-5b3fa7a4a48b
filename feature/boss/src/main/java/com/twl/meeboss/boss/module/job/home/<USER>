package com.twl.meeboss.boss.module.job.home

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.platform.LocalContext
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.paging.compose.collectAsLazyPagingItems
import com.google.accompanist.pager.rememberPagerState
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.main.router.BasePageRouter
import com.twl.meeboss.boss.export.BossEventBusKey
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.boss.foundation.moduleservice.BossUserInfoService
import com.twl.meeboss.boss.module.job.home.dialog.JobCopiedBottomSheet
import com.twl.meeboss.boss.module.job.home.dialog.JobPostedBottomSheet
import com.twl.meeboss.boss.module.job.manager.BossJobManagementNavigation
import com.twl.meeboss.boss.module.job.manager.BossJobManagementPage
import com.twl.meeboss.boss.module.job.manager.BossJobManagementUiIntent
import com.twl.meeboss.boss.module.job.manager.BossJobManagementViewModel
import com.twl.meeboss.boss.module.job.manager.JobListContent
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.core.ui.utils.observeLifecycleEvents
import com.twl.meeboss.core.ui.utils.showSafely

@Composable
fun BossJobPage(
    vm: BossJobManagementViewModel = viewModel(),
    showLoading: (Boolean, Boolean) -> Unit = { _, _ -> }
) {
    vm.observeLifecycleEvents(LocalLifecycleOwner.current.lifecycle)
    val uiState by vm.uiStateFlow.collectAsStateWithLifecycle()
    val pagerState = rememberPagerState(
        initialPage = 0
    )
    val owner = LocalLifecycleOwner.current
    val context = LocalContext.current
    LaunchedEffect(owner) {
        owner.liveEventBusObserve(BossEventBusKey.UPDATE_JOB_STATUS) { _: Int ->
            vm.sendUiIntent(BossJobManagementUiIntent.RefreshPage)
        }
        owner.liveEventBusObserve(BossEventBusKey.POST_JOB_SUCCESS) { _: String ->
            vm.sendUiIntent(BossJobManagementUiIntent.RefreshPage)
        }
        owner.liveEventBusObserve(BossEventBusKey.DELETE_JOB_SUCCESS) { _: String ->
            vm.sendUiIntent(BossJobManagementUiIntent.RefreshPage)
        }
        owner.liveEventBusObserve(BossEventBusKey.UPDATE_JOB_LIST) { _: String ->
            vm.sendUiIntent(BossJobManagementUiIntent.RefreshPage)
        }

        vm.loading.observe(owner) {
            showLoading(it.show, it.cancelable)
        }
    }
    LaunchedEffect(pagerState.currentPage) {
        vm.sendUiIntent(BossJobManagementUiIntent.SwitchTabPage(pagerState.currentPage))
    }
    LaunchedEffect(uiState.navigation) {
        uiState.navigation?.let {
            when{
                it is BossJobManagementNavigation.JobCopied ->{
                    val jobCopyResult = it.jobCopyResult
                    if (context is FragmentActivity) {
                        JobCopiedBottomSheet.newInstance(
                            jobId = jobCopyResult.jobId ?: "",
                            jobTitle = jobCopyResult.jobInfo?.jobTitle ?: "",
                            employmentType = jobCopyResult.jobInfo?.jobType?.map { type -> type.name }
                                ?.joinToString(",") ?: "",
                            workplaceType = jobCopyResult.jobInfo?.locationType?.name ?: ""
                        ).showSafely(context)
                    }
                }
                it is BossJobManagementNavigation.JobPosted->{
                    if(context is FragmentActivity){
                        JobPostedBottomSheet.newInstance().showSafely(context)
                    }
                }
            }
            vm.sendUiIntent(BossJobManagementUiIntent.NavigationHandled)
        }
    }
    val bossUserInfo = BossUserInfoService.bossUserInfo.observeAsState(null)
    val hasActiveJob = (bossUserInfo.value?.bossInfoBossVO?.activeJobCount ?: 0) > 0
    val onCopyClick: (String) -> Unit = {
        vm.sendUiIntent(BossJobManagementUiIntent.CopyJob(it))
    }
    val onPostClick: (String) -> Unit = {
        vm.sendUiIntent(BossJobManagementUiIntent.PostJob(it))
    }
    BossJobManagementPage(
        pagerState = pagerState,
        uiState = uiState,
        hasActiveJob = hasActiveJob,
        onClickFindCandidate = {
            BasePageRouter.jumpToMainActivity(context, 0, false)
        },
        onClickPostJob = {
            BossPointReporter.bossClickPostAJob(7)
            BossPageRouter.jumpToBossPostJobActivity(context)
        }
    ) { page ->
        when (page) {
            0 -> {
                uiState.allListUiState.pagingData?.collectAsLazyPagingItems()?.let {
                    JobListContent(
                        0,
                        hasActiveJob,
                        onCopyClick,
                        onPostClick,
                        it,
                        uiState.allListUiState.listState
                    )
                }
            }

            1 -> {
                uiState.openingListUiState.pagingData?.collectAsLazyPagingItems()?.let {
                    JobListContent(
                        1,
                        hasActiveJob,
                        onCopyClick,
                        onPostClick,
                        it,
                        uiState.openingListUiState.listState
                    )
                }
            }

            2 -> {
                uiState.closedListUiState.pagingData?.collectAsLazyPagingItems()?.let {
                    JobListContent(
                        2,
                        hasActiveJob,
                        onCopyClick,
                        onPostClick,
                        it,
                        uiState.closedListUiState.listState
                    )
                }
            }
        }
    }
}