package com.twl.meeboss.boss.module.candidates.detail.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.os.bundleOf
import androidx.lifecycle.ViewModelProvider
import com.twl.meeboss.base.components.list.refresh.XRefreshList
import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.boss.module.job.manager.model.JobListItemResult
import com.twl.meeboss.boss.module.job.manager.preview.JobManagementPreviewData
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.chat.export.ChatServiceRouter
import com.twl.meeboss.core.ui.component.XDivider
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.component.button.XCommonMidButton
import com.twl.meeboss.core.ui.component.state.XErrorContent
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.theme.COLOR_888888
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable
import dagger.hilt.android.AndroidEntryPoint

/**
 * <AUTHOR>
 * @date 2025/3/4
 * description:
 */
@AndroidEntryPoint
class BossCandidateResumeJobSelectBottomSheet : CommonBottomDialogFragment() {

    companion object {
        fun newInstance(
            friendId: String, friended: Boolean, candidateName: String
        ) = BossCandidateResumeJobSelectBottomSheet().apply {
            arguments = bundleOf(
                "friendId" to friendId,
                "friended" to friended,
                "candidateName" to candidateName,
            )
        }
    }

    private val friendId by lazy {
        arguments?.getString("friendId") ?: ""
    }
    private val friended by lazy {
        arguments?.getBoolean("friended") ?: false
    }
    private val candidateName by lazy {
        arguments?.getString("candidateName") ?: ""
    }

    val viewModel: BossCandidateResumeJobSelectViewModel by lazy {
        ViewModelProvider(this)[BossCandidateResumeJobSelectViewModel::class.java]
    }

    @Composable
    override fun DialogContent() {

        val vm = viewModel
        vm.friendId = friendId
        LaunchedEffect(key1 = Unit) {
            vm.requestJobList()
        }
        val rememberJobs by vm.jobs.observeAsState(
            initial = XRefreshListState.getDefault<JobListItemResult>().copy(
                loadFinishText = ""
            )
        )

        BossCandidateResumeJobSelectDialog(pagingData = rememberJobs,
            candidateName = candidateName,
            onCloseClick = { dismissSafely() },
            onItemClick = { securityId ->
                activity?.let {
                    ChatServiceRouter.checkStartChat(
                        context = it,
                        securityId = securityId,
                        friendId = friendId,
                        friendIdentity = UserConstants.GEEK_IDENTITY,
                        isFriend = this.friended,
                        source = ChatSource.Detail
                    )
                    dismissSafely()
                }
            },
            onRefresh = { vm.requestJobList() },
            onLoadMore = { vm.requestJobList(false) },
            onPostJob = {
                activity?.let {
                    BossPointReporter.bossClickPostAJob(6)
                    BossPageRouter.jumpToBossPostJobActivity(it)
                    dismissSafely()
                }
            })
    }
}

@Composable
private fun BossCandidateResumeJobSelectDialog(
    pagingData: XRefreshListState<JobListItemResult>,
    candidateName: String = "",
    onCloseClick: () -> Unit = {},
    onItemClick: (String) -> Unit = {},
    onRefresh: () -> Unit = {},
    onLoadMore: () -> Unit = {},
    onPostJob: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxHeight()
            .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .background(Color.White)
    ) {
        var selectIndex by rememberSaveable {
            mutableIntStateOf(0)
        }
        if (selectIndex > pagingData.list.size - 1) {
            selectIndex = 0
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 20.dp)
        ) {
            Image(
                painter = painterResource(id = R.drawable.ui_dailog_close),
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .align(Alignment.CenterStart)
                    .noRippleClickable {
                        onCloseClick()
                    },
                contentDescription = null
            )
            Text(
                modifier = Modifier
                    .align(Alignment.Center)
                    .widthIn(200.dp, 250.dp),
                text = stringResource(R.string.boss_talent_pool_select_a_job, candidateName),
                fontSize = 18.sp,
                textAlign = TextAlign.Center,
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                color = Black222222,
                fontWeight = FontWeight.SemiBold
            )
        }

        Box(modifier = Modifier.weight(1f)) {
            XRefreshList(listState = pagingData,
                getUniqueKey = { it.jobId ?: "" },
                onRefresh = onRefresh,
                onLoadMore = onLoadMore,
                emptyContent = {
                    EmptyView(onPostJob)
                },
                errorContent = { _, _ ->
                    XErrorContent(retryOnClick = onRefresh)
                },
                itemContent = { bean, index ->
                    Column(modifier = Modifier.padding(horizontal = 16.dp)) {
                        JobItem(item = bean,
                            position = index,
                            selectPosition = selectIndex,
                            onClick = { selectIndex = it })

                        XDivider()

                        if (!pagingData.hasMore && index >= pagingData.list.size - 1) {
                            FooterView(onPostJob)
                        }
                    }
                })
        }

        if (pagingData.list.isNotEmpty()) {
            XCommonButton(modifier = Modifier.padding(16.dp),
                text = stringResource(R.string.boss_talent_pool_select_job_button),
                onClick = { onItemClick(pagingData.list[selectIndex].securityId ?: "") })
        }
    }
}

@Composable
private fun FooterView(onPostJob: () -> Unit) {
    Row(
        modifier = Modifier
            .padding(vertical = 12.dp)
            .fillMaxWidth()
            .noRippleClickable { onPostJob() }, horizontalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(R.string.boss_talent_pool_post_a_job) + " ", style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 22.sp,
                fontWeight = FontWeight(400),
                color = COLOR_484848,
            )
        )

        Text(
            text = stringResource(R.string.job_post_a_job),
            textDecoration = TextDecoration.Underline,
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 22.sp,
                fontWeight = FontWeight(400),
                color = COLOR_222222,
            )
        )
    }
}

@Composable
private fun EmptyView(onPostJob: () -> Unit) {
    Column(
        Modifier.fillMaxWidth()
    ) {
        Image(
            painter = painterResource(id = R.drawable.boss_icon_select_job_empty),
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .padding(top = 140.dp),
            contentDescription = null
        )

        Text(
            modifier = Modifier
                .width(247.dp)
                .padding(vertical = 20.dp)
                .align(Alignment.CenterHorizontally),
            text = stringResource(R.string.boss_talent_pool_select_a_job_empty),
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 21.sp,
                fontWeight = FontWeight(400),
                color = COLOR_484848,
                textAlign = TextAlign.Center,
            )
        )

        XCommonMidButton(
            modifier = Modifier.align(Alignment.CenterHorizontally),
            buttonText = stringResource(R.string.job_post_a_job),
            onClick = onPostJob
        )
    }
}

@Composable
private fun JobItem(
    item: JobListItemResult, position: Int = 0, selectPosition: Int = 0, onClick: (Int) -> Unit = {}
) {
    Row(modifier = Modifier
        .padding(vertical = 24.dp)
        .fillMaxWidth()
        .noRippleClickable {
            onClick(position)
        }) {
        Image(
            modifier = Modifier.align(Alignment.CenterVertically),
            painter = if (position == selectPosition) painterResource(id = R.drawable.ui_radio_selected)
            else painterResource(id = R.drawable.ui_dialog_item_single_normal),
            contentDescription = null
        )

        Column(modifier = Modifier.padding(start = 12.dp)) {
            Text(
                modifier = Modifier.fillMaxWidth(),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                text = item.jobTitle ?: "",
                style = TextStyle(
                    fontSize = 16.sp,
                    lineHeight = 24.sp,
                    fontWeight = FontWeight(510),
                    color = COLOR_222222,
                )
            )

            Text(
                modifier = Modifier
                    .padding(top = 4.dp)
                    .fillMaxWidth(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                text = item.getDesc(),
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 21.sp,
                    fontWeight = FontWeight(400),
                    color = COLOR_888888,
                )
            )
        }
    }
}

private fun JobListItemResult.getDesc(): String {
    val tags = arrayListOf<String>()
    jobType?.forEach {
        val name = it.name
        if (!name.isNullOrBlank()) {
            tags.add(name)
        }
    }
    locationType?.run {
        this.name?.let {
            tags.add(it)
        }
    }
    jobLocations?.forEach {
        val location = it
        if (!location.isNullOrBlank()) {
            tags.add(location)
        }
    }
    val payDesc = payDesc
    if (!payDesc.isNullOrBlank()) {
        tags.add(payDesc)
    }
    return tags.joinToString(" · ")
}


@Preview
@Composable
fun PreviewSingleChoiceDialog() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Gray)
    ) {
        BossCandidateResumeJobSelectDialog(
            candidateName = "moo",
            pagingData = XRefreshListState.getPreviewDefault(list = JobManagementPreviewData.jobList)
        )
    }
}

@Preview
@Composable
fun PreviewSingleChoiceDialog1() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        EmptyView {}
    }
}
