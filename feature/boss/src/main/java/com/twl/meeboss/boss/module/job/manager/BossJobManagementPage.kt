package com.twl.meeboss.boss.module.job.manager

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import androidx.paging.compose.itemKey
import com.google.accompanist.pager.PagerScope
import com.google.accompanist.pager.PagerState
import com.google.accompanist.pager.rememberPagerState
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.model.job.JobStatus
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.module.job.manager.components.JobListItemContent
import com.twl.meeboss.boss.module.job.manager.components.PreviewJobListItemContent
import com.twl.meeboss.boss.module.job.manager.model.JobListItemResult
import com.twl.meeboss.boss.module.job.manager.preview.JobManagementPreviewData
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.component.list.XRefreshLoadList
import com.twl.meeboss.core.ui.component.state.XLoadingItem
import com.twl.meeboss.core.ui.component.state.XNoMoreItem
import com.twl.meeboss.core.ui.component.tabview.XTabView
import com.twl.meeboss.core.ui.theme.XTheme
import java.util.UUID

/**
 * @author: 冯智健
 * @date: 2024年07月03日 16:47
 * @description:
 */
@Composable
fun BossJobManagementPage(
    pagerState: PagerState,
    hasActiveJob: Boolean = true,
    uiState: BossJobManagementUiState = BossJobManagementUiState(),
    onClickPostJob: () -> Unit = {},
    onClickFindCandidate: () -> Unit = {},
    content: @Composable PagerScope.(Int) -> Unit
) {
    val closeEmpty =
        pagerState.currentPage == 2 && (uiState.closedListUiState.pagingData?.collectAsLazyPagingItems()?.itemCount
            ?: 0) == 0
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {

        XTabView(
            modifier = Modifier.weight(1f),
            pagerState = pagerState,
            options = listOf(
                stringResource(id = R.string.common_all),
                stringResource(id = R.string.job_status_active),
                stringResource(id = R.string.job_status_closed)
            ),
            tabIndicatorPercent = 0.6F,
            tabEdgePadding = 8.dp
        ) {
            XTheme {
                Box(
                    modifier = Modifier
                        .padding(top = 8.dp, bottom = 10.dp)
                        .fillMaxSize()
                        .padding(horizontal = 16.dp)
                ) {
                    content(it)
                }
            }
        }
        if (hasActiveJob && closeEmpty) {
            XCommonButton(
                modifier = Modifier.padding(16.dp),
                onClick = onClickFindCandidate,
                text = stringResource(id = R.string.employer_jobsmanagement_findcandidates_button)
            )
        } else {
            XCommonButton(
                modifier = Modifier.padding(16.dp),
                onClick = onClickPostJob,
                text = stringResource(id = R.string.job_post_a_job)
            )

        }
    }
}

@Composable
fun JobListContent(
    pageIndex: Int,
    hasActiveJob: Boolean,
    onCopyClick: (String) -> Unit,
    onPostClick: (String) -> Unit,
    lazyPagingItems: LazyPagingItems<JobListItemResult>,
    lazyListState: LazyListState
) {
    XRefreshLoadList(
        emptyText = stringResource(
            id = if (pageIndex == 2 && hasActiveJob) {
                R.string.employer_jobsmanagement_findcandidates_body
            } else {
                R.string.job_management_list_placeholder
            }
        ),
        refreshFinishText = stringResource(id = R.string.common_position_updated),
        loadFinishText = "",
        emptyImage = R.drawable.boss_f1_job_empty_icon,
        lazyPagingItems = lazyPagingItems,
        lazyListState = lazyListState
    ) {
        items(
            lazyPagingItems.itemCount,
            key = lazyPagingItems.itemKey { it.jobId ?: UUID.randomUUID().toString() }
        ) { index ->
            lazyPagingItems[index]?.let { item ->
                when (pageIndex) {
                    0 -> {
                        JobListItemContent(item, onCopyClick, onPostClick)
                    }

                    1 -> {
                        if (item.jobStatus == JobStatus.OPENING) {
                            JobListItemContent(item, onCopyClick, onPostClick)
                        }
                    }

                    2 -> {
                        if (item.jobStatus == JobStatus.CLOSED) {
                            JobListItemContent(item, onCopyClick, onPostClick)
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewJobManagementPage() {
    BossJobManagementPage(pagerState = rememberPagerState(initialPage = 0)) {
        Column {
            PreviewJobListItemContent(JobManagementPreviewData.jobList)
            XNoMoreItem()
            XLoadingItem()
        }
    }
}