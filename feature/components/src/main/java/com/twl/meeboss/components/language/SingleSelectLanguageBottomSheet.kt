package com.twl.meeboss.components.language

import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.twl.meeboss.base.R
import com.twl.meeboss.base.components.indexlist.IndexListComponent
import com.twl.meeboss.base.components.indexlist.IndexListItem
import com.twl.meeboss.base.components.titlebar.XDialogTitleBar
import com.twl.meeboss.base.model.IndexBean
import com.twl.meeboss.base.model.IndexWrapperBean
import com.twl.meeboss.components.language.preview.LanguagePreviewProvider
import com.twl.meeboss.components.language.preview.PreviewLanguageData
import com.twl.meeboss.core.ui.component.textfield.XSearchTextField
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable

/**
 * 单选语言底部弹窗
 */
class SingleSelectLanguageBottomSheet :
    CommonBottomDialogFragment() {

    private var onSelected: ((IndexBean) -> Unit)? = null

    companion object {
        fun newInstance(
            onSelected: (IndexBean) -> Unit
        ) = SingleSelectLanguageBottomSheet().apply {
            this.onSelected = onSelected
        }
    }
    @Composable
    override fun DialogContent() {
        val vm: SingleSelectLanguageViewModel = viewModel()
        LaunchedEffect(Unit) {
            vm.getAllLanguages()
        }
        val uiState by vm.uiStateFlow.collectAsStateWithLifecycle()

        SingleSelectLanguageBottomSheetContent(indexes = uiState.indexes,
            input = uiState.input,
            indexList = uiState.indexList,
            searchList = uiState.searchList,
            positionMap = uiState.positionMap,
            onSelected = {
                onSelected?.invoke(it)
                dismissSafely()
            }, onInputChanged = {
                vm.sendUiIntent(SingleSelectLanguageUiIntent.InputChanged(it))
            }, onCloseClick = {
                dismissSafely()
            })
    }

    override fun getDialogHeight(): Int {
        return ViewGroup.LayoutParams.MATCH_PARENT
    }
}

@Composable
fun SingleSelectLanguageBottomSheetContent(
    modifier: Modifier = Modifier,
    input: TextFieldValue = TextFieldValue(), //默认输入
    indexList: List<IndexWrapperBean>, //带字母的列表
    indexes: List<String>, //右侧的字母索引列表
    searchList: List<IndexBean>, //搜索结果列表
    positionMap: Map<String, Int>, //右侧字母对应内容列表要滚动的位置
    onInputChanged: (TextFieldValue)->Unit = {},
    onSelected: (IndexBean) -> Unit,
    onCloseClick: () -> Unit
) {

    Column(
        modifier = modifier
            .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .background(MaterialTheme.colorScheme.background)
    ) {
        XDialogTitleBar(
            title = stringResource(id = R.string.common_language),
            onCloseClick = onCloseClick
        )
        XSearchTextField(modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 20.dp),
            value = input, placeHolder = R.string.common_search_language,
            onValueChange = onInputChanged)
        Box(modifier = Modifier
            .fillMaxSize()
            .background(Color.White)) {
            if (input.text.isBlank()) {
                IndexListComponent(
                    modifier = Modifier
                        .padding(top = 20.dp)
                        .fillMaxSize(),
                    indexList = indexList,
                    indexes = indexes,
                    positionMap = positionMap,
                    onSelected = onSelected
                )
            } else {
                if (searchList.isEmpty()) {
                    IndexListItem(
                        modifier.padding(start = 16.dp, end = 16.dp, top = 8.dp),
                        text = stringResource(id = R.string.common_no_search_result),
                        showDivider = true
                    )
                } else {
                    LazyColumn(
                        modifier = Modifier
                            .padding(start = 16.dp, end = 16.dp, top = 8.dp)
                            .fillMaxSize()
                    ) {
                        items(searchList.size) { index ->
                            IndexListItem(
                                modifier = Modifier.noRippleClickable {
                                    onSelected(searchList[index])
                                },
                                text = searchList[index].getItemName(),
                                showDivider = index != searchList.size - 1
                            )
                        }
                    }
                }

            }
        }
    }
}

@Preview
@Composable
private fun PreviewIndexListComponent(@PreviewParameter(LanguagePreviewProvider::class) data: PreviewLanguageData) {
    SingleSelectLanguageBottomSheetContent(
        input = TextFieldValue(data.input),
        searchList = data.searchList,
        indexList = data.indexList,
        indexes = data.indexes,
        positionMap = data.positionMap,
        onSelected = {}, onInputChanged = {}, onCloseClick = {})
}


