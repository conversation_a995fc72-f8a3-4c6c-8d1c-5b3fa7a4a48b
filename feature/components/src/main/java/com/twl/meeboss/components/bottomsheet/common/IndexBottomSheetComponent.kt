package com.twl.meeboss.components.bottomsheet.common

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.twl.meeboss.base.R
import com.twl.meeboss.base.components.titlebar.XDialogTitleBar
import com.twl.meeboss.base.model.IIndexBean
import com.twl.meeboss.components.bottomsheet.common.indexlist.IndexListComponent
import com.twl.meeboss.components.bottomsheet.common.indexlist.IndexListItem
import com.twl.meeboss.core.ui.component.textfield.XSearchTextField
import com.twl.meeboss.core.ui.utils.noRippleClickable


@Composable
fun <T : IIndexBean> IndexBottomSheetComponent(
    modifier: Modifier = Modifier,
    title: String = "",
    @StringRes
    placeHolder: Int = 0,
    input: TextFieldValue = TextFieldValue(), //默认输入
    dataSource: IndexComponentDataSource<T>,
    searchList: List<T>, //搜索结果列表
    onSelected: (T) -> Unit,
    onTextChanged: (TextFieldValue) -> Unit,
    onCloseClick: () -> Unit
) {

    Column(
        modifier = modifier
            .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .background(MaterialTheme.colorScheme.background)
    ) {
        XDialogTitleBar(
            title = title,
            onCloseClick = onCloseClick
        )
        XSearchTextField(modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 20.dp),
            value = input, placeHolder = placeHolder,
            onValueChange = {
                onTextChanged(it)
            })
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
        ) {
            if (input.text.isBlank()) {
                IndexListComponent(
                    modifier = Modifier
                        .padding(top = 20.dp)
                        .fillMaxSize(),
                    indexList = dataSource.indexList,
                    indexes = dataSource.indexes,
                    positionMap = dataSource.getPositionMap(),
                    onSelected = onSelected
                )
            } else {
                if (searchList.isEmpty()) {
                    IndexListItem(
                        modifier.padding(start = 16.dp, end = 16.dp, top = 8.dp),
                        text = stringResource(id = R.string.common_no_search_result),
                        showDivider = true
                    )
                } else {
                    LazyColumn(
                        modifier = Modifier
                            .padding(start = 16.dp, end = 16.dp, top = 8.dp)
                            .fillMaxSize()
                    ) {
                        items(searchList.size) { index ->
                            IndexListItem(
                                modifier = Modifier.noRippleClickable {
                                    onSelected(searchList[index])
                                },
                                text = searchList[index].getItemName(),
                                showDivider = index != searchList.size - 1
                            )
                        }
                    }
                }

            }
        }
    }
}

data class IndexGroupBean<T : IIndexBean>(val groupName: String, val list: List<T>)

data class IndexComponentDataSource<T : IIndexBean>(
    val indexList: List<IndexGroupBean<T>> = listOf(), //带字母的列表
    val indexes: List<String> = listOf(), //右侧的字母索引列表
    private var positionMap: Map<String, Int> = mapOf()
) {

    /**
     * 初始化计算索引的位置
     * @param topList 顶部热门部分，不参与和右侧字母索引联动
     * @param targetList 内容部分，参与和右侧字母索引联动
     */
    fun  calculate(topList: List<IndexGroupBean<T>>?, targetList: List<IndexGroupBean<T>>?):IndexComponentDataSource<T> {
        val map = mutableMapOf<String, Int>()
        if (targetList.isNullOrEmpty()) {
            return this
        }
        var startOffset: Int = 0
        topList?.forEach {
            //字母分割+列表数量，topList不参与右侧索引
            startOffset += (it.list.size + 1)
        }
        targetList.forEachIndexed { _, indexWrapperBean ->
            map[indexWrapperBean.groupName] = startOffset
            startOffset += indexWrapperBean.list.size + 1
        }
        return copy(positionMap = map)
    }

    fun getPositionMap(): Map<String, Int> {
        return positionMap
    }
}