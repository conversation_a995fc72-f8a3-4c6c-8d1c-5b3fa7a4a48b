package com.twl.meeboss.components.bottomsheet.common.indexlist

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.model.IIndexBean
import com.twl.meeboss.base.model.IndexBean
import com.twl.meeboss.components.bottomsheet.common.IndexGroupBean
import com.twl.meeboss.core.ui.theme.Black020202
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.COLOR_F6F6F6
import com.twl.meeboss.core.ui.utils.noRippleClickable
import kotlinx.coroutines.launch


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun <T:IIndexBean>IndexListComponent(modifier: Modifier = Modifier,
                       indexList: List<IndexGroupBean<T>>,
                       indexes: List<String>,
                       positionMap: Map<String, Int>,
                       onSelected: (T) -> Unit) {
    Box(modifier = modifier) {
        val listState = rememberLazyListState()
        // Remember a CoroutineScope to be able to launch
        val coroutineScope = rememberCoroutineScope()
        LazyColumn(modifier = Modifier, state = listState) {
            indexList.forEachIndexed { _, wrapper ->
                stickyHeader {
                    IndexStickyHeader(text = wrapper.groupName)
                }
                items(wrapper.list.size) { index ->
                    IndexListItem(modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .noRippleClickable {
                            onSelected(wrapper.list[index])
                        }, text = wrapper.list[index].getItemName(), showDivider = index != wrapper.list.size - 1)
                }
            }

        }
        Index(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = 67.dp), charList = indexes
        ) { char ->
            val index = positionMap[char] ?: -1
            if (index <= -1) return@Index
            coroutineScope.launch {
                // Animate scroll to the first item
                listState.scrollToItem(index = index)
            }

        }
    }
}

@Composable
fun IndexListItem(modifier: Modifier = Modifier, text: String, showDivider: Boolean) {
    Column(modifier = modifier) {
        Text(text = text,
            fontSize = 16.sp,
            color = Black020202,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            fontWeight = FontWeight.Medium,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 24.dp))
        if (showDivider) {
            HorizontalDivider(thickness = 1.dp, color = BlackEBEBEB)
        }
    }
}

@Composable
fun IndexStickyHeader(modifier: Modifier = Modifier, text: String) {
    Box(modifier = modifier
        .fillMaxWidth()
        .background(COLOR_F6F6F6)
        .padding(vertical = 8.dp)) {
        Text(text = text, modifier = Modifier.padding(horizontal = 16.dp),
            fontSize = 12.sp,
            fontWeight = FontWeight.Normal,
            color = Black888888)
    }

}


@Preview
@Composable
private fun IndexListComponentPreview() {
    IndexListComponent(modifier = Modifier
        .fillMaxSize()
        .background(Color.White),
        indexList = listOf(IndexGroupBean(groupName = "A", listOf(IndexBean(0, "Apple"), IndexBean(0, "Ang"), IndexBean(0, "Agg"))),
            IndexGroupBean(groupName = "B", listOf(IndexBean(0, "Banana"), IndexBean(0, "Bing"), IndexBean(0, "Bing"))),
            IndexGroupBean(groupName = "C", listOf(IndexBean(0, "Coco"), IndexBean(0, "Coco"), IndexBean(0, "Coco"))),
            IndexGroupBean(groupName = "D", listOf(IndexBean(0, "Dodo"), IndexBean(0, "Dodo"), IndexBean(0, "Dodo"))),
            IndexGroupBean(groupName = "E", listOf(IndexBean(0, "Egg"), IndexBean(0, "Egg"), IndexBean(0, "Egg"))),
            IndexGroupBean(groupName = "F", listOf(IndexBean(0, "Frog"), IndexBean(0, "Frog"), IndexBean(0, "Frog"))),
            IndexGroupBean(groupName = "G", listOf(IndexBean(0, "Gog"), IndexBean(0, "Gog"), IndexBean(0, "Gog"))),
            IndexGroupBean(groupName = "H", listOf(IndexBean(0, "Hog"), IndexBean(0, "Hog"), IndexBean(0, "Hog"))),
            IndexGroupBean(groupName = "I", listOf(IndexBean(0, "Ig"), IndexBean(0, "Ig"), IndexBean(0, "Ig"))),
            IndexGroupBean(groupName = "J", listOf(IndexBean(0, "Jog"), IndexBean(0, "Jog"), IndexBean(0, "Jog"))),
            IndexGroupBean(groupName = "K", listOf(IndexBean(0, "Kog"), IndexBean(0, "Kog"), IndexBean(0, "Kog"))),
            IndexGroupBean(groupName = "L", listOf(IndexBean(0, "Log"), IndexBean(0, "Log"), IndexBean(0, "Log"))),
            IndexGroupBean(groupName = "M", listOf(IndexBean(0, "Mog"), IndexBean(0, "Mog"), IndexBean(0, "Mog"))),
            IndexGroupBean(groupName = "N", listOf(IndexBean(0, "Nog"), IndexBean(0, "Nog"), IndexBean(0, "Nog"))),
            IndexGroupBean(groupName = "O", listOf(IndexBean(0, "Og"), IndexBean(0, "Og"), IndexBean(0, "Og"))),
            IndexGroupBean(groupName = "P", listOf(IndexBean(0, "Pog"), IndexBean(0, "Pog"), IndexBean(0, "Pog")))),
        indexes = listOf("A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"),
        positionMap = mapOf("A" to 0,
            "B" to 1,
            "C" to 2,
            "D" to 3,
            "E" to 4,
            "F" to 5,
            "G" to 6,
            "H" to 7,
            "I" to 8,
            "J" to 9,
            "K" to 10,
            "L" to 11,
            "M" to 12,
            "N" to 13,
            "O" to 14,
            "P" to 15,
            "Q" to 16,
            "R" to 17,
            "S" to 18,
            "T" to 19,
            "U" to 20,
            "V" to 21,
            "W" to 22,
            "X" to 23,
            "Y" to 24,
            "Z" to 25),
        onSelected = {})
}


