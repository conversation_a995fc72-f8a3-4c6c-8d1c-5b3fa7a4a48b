package com.twl.meeboss.components.language

import androidx.compose.ui.text.input.TextFieldValue
import com.twl.meeboss.base.api.BaseApi
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.repo.toResult
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.model.IndexBean
import com.twl.meeboss.base.model.IndexWrapperBean
import com.twl.meeboss.core.network.getService
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
internal class MultiSelectLanguageViewModel @Inject constructor(
    //private val repos: BossJobRepository
) : BaseMviViewModel<MultiSelectLanguageUiState, MultiSelectLanguageUiIntent>() {

    private var originList: List<IndexWrapperBean> = listOf()//下面字母内容列表

    override fun initUiState(): MultiSelectLanguageUiState = MultiSelectLanguageUiState()


    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is MultiSelectLanguageUiIntent.Init -> {
                sendUiState {
                    copy(maxCount = intent.maxCount)
                }
                getAllLanguages(intent.defaultSelectedList)
            }

            is MultiSelectLanguageUiIntent.InputChanged -> {
                sendUiState {
                    copy(input = intent.input)
                }
                searchLanguages(intent.input.text)
            }

            is MultiSelectLanguageUiIntent.Selected -> {
                sendUiState {
                    copy(selectedList = selectedList + intent.item, input = TextFieldValue())
                }
                updateSelected()
            }

            is MultiSelectLanguageUiIntent.Deleted -> {
                sendUiState {
                    copy(selectedList = selectedList.filter { it.localUniqueKey() != intent.item.localUniqueKey() })
                }
                updateSelected()
            }

            is MultiSelectLanguageUiIntent.Save -> {

            }

            else -> {

            }

        }
    }

    private fun getAllLanguages(defaultList: List<IndexBean>) {
        launcherOnIO {
            getService(BaseApi::class.java).getLanguages().toResult().onSuccess { result ->
                result.run {
                    originList = list ?: listOf()
                    val selectedKeyList = defaultList.map { it.localUniqueKey() }
                    originList.forEach { wrap ->
                        wrap.list.forEach { bean ->
                            if (selectedKeyList.contains(bean.localUniqueKey())) {
                                bean.localSelected = true
                            }

                        }
                    }
                    sendUiState {
                        copy(
                            indexList = mutableListOf<IndexWrapperBean>().also {
                                if (!topList.isNullOrEmpty()) {
                                    it.addAll(topList ?: listOf())
                                }
                                if (originList.isNotEmpty()) {
                                    it.addAll(originList)
                                }
                            },
                            selectedList = defaultList,
                            indexes = originList.map { it.type },
                            positionMap = createIndexMap(result.topList, originList)
                        )
                    }

                }
            }.onFailure {

            }
        }
    }

    private fun updateSelected() {
        launcherOnIO {
            val selectedKey = uiStateFlow.value.selectedList.map { it.localUniqueKey() }
            uiStateFlow.value.indexList.forEach { wrap ->
                wrap.list.forEach { bean ->
                    bean.localSelected = selectedKey.contains(bean.localUniqueKey())
                }
            }
            sendUiState {
                copy(indexList = uiStateFlow.value.indexList)
            }
        }

    }

    private fun createIndexMap(
        topList: List<IndexWrapperBean>?,
        targetList: List<IndexWrapperBean>?
    ): Map<String, Int> {
        val map = mutableMapOf<String, Int>()
        if (targetList.isNullOrEmpty()) {
            return map
        }
        var startOffset: Int = 0
        topList?.forEach {
            //字母分割+列表数量，topList不参与右侧索引
            startOffset += (it.list.size + 1)
        }
        targetList.forEachIndexed { _, indexWrapperBean ->
            map[indexWrapperBean.type] = startOffset
            startOffset += indexWrapperBean.list.size + 1
        }
        return map
    }

    /**
     * 搜索语言
     */
    private fun searchLanguages(searchKey: String) {
        if (searchKey.isBlank()) {
            sendUiState { copy(searchList = emptyList()) }
            return
        }
        launcherOnIO {
            val listAfterFilter = mutableListOf<IndexBean>()
            originList.forEach { indexWrapperBean ->
                indexWrapperBean.list.forEach { bean ->
                    if (bean.getItemName().contains(searchKey, true)) {
                        listAfterFilter.add(bean)
                    }
                }
            }
            sendUiState { copy(searchList = listAfterFilter.distinctBy { it.name + it.code }) }
        }

    }

}

internal data class MultiSelectLanguageUiState(
    val input: TextFieldValue = TextFieldValue(), //默认输入
    val indexList: List<IndexWrapperBean> = emptyList(), //带字母的列表
    val indexes: List<String> = emptyList(), //右侧的字母索引列表
    val searchList: List<IndexBean> = emptyList(), //搜索结果列表
    val positionMap: Map<String, Int> = emptyMap(),
    val maxCount: Int = 5,
    val selectedList: List<IndexBean> = emptyList(),
) : IUiState

internal sealed class MultiSelectLanguageUiIntent : IUiIntent {
    data class Init(val defaultSelectedList: List<IndexBean>, val maxCount: Int) :
        MultiSelectLanguageUiIntent()

    data class InputChanged(val input: TextFieldValue) : MultiSelectLanguageUiIntent()
    data class Selected(val item: IndexBean) : MultiSelectLanguageUiIntent()
    data class Deleted(val item: IndexBean) : MultiSelectLanguageUiIntent()
    data object Save : MultiSelectLanguageUiIntent()
}