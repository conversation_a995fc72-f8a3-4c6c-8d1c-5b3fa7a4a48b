package com.twl.meeboss.components.router

import androidx.fragment.app.FragmentActivity
import com.sankuai.waimai.router.annotation.RouterService
import com.twl.meeboss.base.main.router.BaseRouterPath
import com.twl.meeboss.base.main.router.IBaseComponentService
import com.twl.meeboss.base.model.common.CommonAreaCodeBean
import com.twl.meeboss.components.bottomsheet.areacode.SelectAreaCodeBottomSheet
import com.twl.meeboss.core.ui.utils.showSafely

@RouterService(
    interfaces = [IBaseComponentService::class],
    key = [BaseRouterPath.BASE_COMPONENT_SERVICE],
    singleton = true
)
class BaseComponentServiceImpl : IBaseComponentService {
    override fun showSelectAreaCodeBottomSheet(
        activity: FragmentActivity,
        onCancel: ()-> Unit,
        onSelected: (CommonAreaCodeBean) -> Unit
    ) {
        SelectAreaCodeBottomSheet.newInstance(onCancel = onCancel, onSelected = onSelected).showSafely(activity)
    }
}