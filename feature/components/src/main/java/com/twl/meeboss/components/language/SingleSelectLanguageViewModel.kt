package com.twl.meeboss.components.language

import androidx.compose.ui.text.input.TextFieldValue
import com.twl.meeboss.base.api.BaseApi
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.repo.toResult
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.model.IndexBean
import com.twl.meeboss.base.model.IndexWrapperBean
import com.twl.meeboss.core.network.getService
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class SingleSelectLanguageViewModel @Inject constructor() :
    BaseMviViewModel<SingleSelectLanguageUiState, SingleSelectLanguageUiIntent>() {

    override fun initUiState(): SingleSelectLanguageUiState = SingleSelectLanguageUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {

            is SingleSelectLanguageUiIntent.InputChanged -> {
                sendUiState { copy(input = intent.input) }
                searchLanguages(intent.input.text)
            }

            else -> {

            }

        }
    }

    private var mContentList: List<IndexWrapperBean> = listOf()//下面字母内容列表

    fun getAllLanguages() {
        launcherOnIO {
            getService(BaseApi::class.java).getLanguages().toResult().onSuccess { result ->
                result.run {
                    mContentList = list ?: listOf()
                    sendUiState {
                        copy(indexList = mutableListOf<IndexWrapperBean>().also {
                            if (!topList.isNullOrEmpty()) {
                                it.addAll(topList ?: listOf())
                            }
                            if (mContentList.isNotEmpty()) {
                                it.addAll(mContentList)
                            }
                        }, indexes = mContentList.map { it.type },
                            positionMap = createIndexMap(result.topList, mContentList)
                        )
                    }
                }
            }.onFailure {

            }
        }
    }

    /**
     * 搜索语言
     */
    fun searchLanguages(searchKey: String) {
        if (searchKey.isBlank()) {
            sendUiState { copy(searchList = emptyList()) }
        }
        launcherOnIO {
            val listAfterFilter = mutableListOf<IndexBean>()
            mContentList.forEach { indexWrapperBean ->
                indexWrapperBean.list.forEach { bean ->
                    if (bean.getItemName().contains(searchKey, true)) {
                        listAfterFilter.add(bean)
                    }
                }
            }
            sendUiState { copy(searchList = listAfterFilter) }

        }

    }

    private fun createIndexMap(
        topList: List<IndexWrapperBean>?,
        targetList: List<IndexWrapperBean>?
    ): Map<String, Int> {
        val map = mutableMapOf<String, Int>()
        if (targetList.isNullOrEmpty()) {
            return map
        }
        var startOffset: Int = 0
        topList?.forEach {
            //字母分割+列表数量，topList不参与右侧索引
            startOffset += (it.list.size + 1)
        }
        targetList.forEachIndexed { _, indexWrapperBean ->
            map[indexWrapperBean.type] = startOffset
            startOffset += indexWrapperBean.list.size + 1
        }
        return map
    }

}

data class SingleSelectLanguageUiState(
    val input: TextFieldValue = TextFieldValue(),
    val searchList: List<IndexBean> = emptyList(), //搜索结果列表
    val indexList: List<IndexWrapperBean> = emptyList(), //带字母的内容列表，由topList+contentList组成
    val indexes: List<String> = emptyList(), //右侧字母列表
    val positionMap: Map<String, Int> = emptyMap() //右侧字母对应内容列表要滚动的位置
) : IUiState

sealed class SingleSelectLanguageUiIntent : IUiIntent {
    data class InputChanged(val input: TextFieldValue) : SingleSelectLanguageUiIntent()

}