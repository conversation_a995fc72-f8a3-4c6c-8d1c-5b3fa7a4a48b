package com.twl.meeboss.components.language.preview

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.twl.meeboss.base.model.IndexBean
import com.twl.meeboss.base.model.IndexWrapperBean

class PreviewLanguageData(
    val input: String = "AAA",
    val searchList: List<IndexBean> = listOf(
        IndexBean(0, "Apple"),
        IndexBean(0, "Ang"),
        IndexBean(0, "Agg"), IndexBean(0, "Apple"),
        IndexBean(0, "Ang"),
        IndexBean(0, "Agg"), IndexBean(0, "Apple"),
        IndexBean(0, "Ang"),
        IndexBean(0, "Agg"), IndexBean(0, "Apple"),
        IndexBean(0, "Ang"),
        IndexBean(0, "Agg")
    ),
    val selectedList: List<IndexBean> = listOf(
        IndexBean(0, "English"),
        IndexBean(0, "Germany"),
        IndexBean(0, "Japanese")
    ),
    val indexList: List<IndexWrapperBean> = listOf(
        IndexWrapperBean(
            type = "A",
            listOf(IndexBean(0, "Apple"), IndexBean(0, "Ang"), IndexBean(0, "Agg"))
        ),
        IndexWrapperBean(
            type = "B",
            listOf(IndexBean(0, "Banana"), IndexBean(0, "Bing"), IndexBean(0, "Bing"))
        ),
        IndexWrapperBean(
            type = "C",
            listOf(IndexBean(0, "Coco"), IndexBean(0, "Coco"), IndexBean(0, "Coco"))
        ),
        IndexWrapperBean(
            type = "D",
            listOf(IndexBean(0, "Dodo"), IndexBean(0, "Dodo"), IndexBean(0, "Dodo"))
        ),
        IndexWrapperBean(
            type = "E",
            listOf(IndexBean(0, "Egg"), IndexBean(0, "Egg"), IndexBean(0, "Egg"))
        ),
        IndexWrapperBean(
            type = "F",
            listOf(IndexBean(0, "Frog"), IndexBean(0, "Frog"), IndexBean(0, "Frog"))
        ),
        IndexWrapperBean(
            type = "G",
            listOf(IndexBean(0, "Gog"), IndexBean(0, "Gog"), IndexBean(0, "Gog"))
        ),
        IndexWrapperBean(
            type = "H",
            listOf(IndexBean(0, "Hog"), IndexBean(0, "Hog"), IndexBean(0, "Hog"))
        ),
        IndexWrapperBean(
            type = "I",
            listOf(IndexBean(0, "Ig"), IndexBean(0, "Ig"), IndexBean(0, "Ig"))
        ),
        IndexWrapperBean(
            type = "J",
            listOf(IndexBean(0, "Jog"), IndexBean(0, "Jog"), IndexBean(0, "Jog"))
        ),
        IndexWrapperBean(
            type = "K",
            listOf(IndexBean(0, "Kog"), IndexBean(0, "Kog"), IndexBean(0, "Kog"))
        ),
        IndexWrapperBean(
            type = "L",
            listOf(IndexBean(0, "Log"), IndexBean(0, "Log"), IndexBean(0, "Log"))
        ),
        IndexWrapperBean(
            type = "M",
            listOf(IndexBean(0, "Mog"), IndexBean(0, "Mog"), IndexBean(0, "Mog"))
        ),
        IndexWrapperBean(
            type = "N",
            listOf(IndexBean(0, "Nog"), IndexBean(0, "Nog"), IndexBean(0, "Nog"))
        ),
        IndexWrapperBean(
            type = "O",
            listOf(IndexBean(0, "Og"), IndexBean(0, "Og"), IndexBean(0, "Og"))
        ),
        IndexWrapperBean(
            type = "P",
            listOf(IndexBean(0, "Pog"), IndexBean(0, "Pog"), IndexBean(0, "Pog"))
        )
    ),
    val indexes: List<String> = listOf<String>(
        "A",
        "B",
        "C",
        "D",
        "E",
        "F",
        "G",
        "H",
        "I",
        "J",
        "K",
        "L",
        "M",
        "N",
        "O",
        "P",
        "Q",
        "R",
        "S",
        "T",
        "U",
        "V",
        "W",
        "X",
        "Y",
        "Z"
    ),
    val positionMap: Map<String, Int> = mapOf(
        "A" to 0,
        "B" to 1,
        "C" to 2,
        "D" to 3,
        "E" to 4,
        "F" to 5,
        "G" to 6,
        "H" to 7,
        "I" to 8,
        "J" to 9,
        "K" to 10,
        "L" to 11,
        "M" to 12,
        "N" to 13,
        "O" to 14,
        "P" to 15,
        "Q" to 16,
        "R" to 17,
        "S" to 18,
        "T" to 19,
        "U" to 20,
        "V" to 21,
        "W" to 22,
        "X" to 23,
        "Y" to 24,
        "Z" to 25
    ),
)

class LanguagePreviewProvider : PreviewParameterProvider<PreviewLanguageData> {
    override val values: Sequence<PreviewLanguageData> = sequenceOf(
        PreviewLanguageData(input = ""),
        PreviewLanguageData(),
        PreviewLanguageData(searchList = listOf())
    )
}