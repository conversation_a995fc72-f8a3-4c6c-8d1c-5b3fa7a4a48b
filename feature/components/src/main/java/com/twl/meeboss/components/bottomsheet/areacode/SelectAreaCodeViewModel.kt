package com.twl.meeboss.components.bottomsheet.areacode

import androidx.compose.ui.text.input.TextFieldValue
import com.twl.meeboss.base.api.BaseApi
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.repo.toResult
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.model.common.CommonAreaCodeBean
import com.twl.meeboss.components.bottomsheet.common.IndexComponentDataSource
import com.twl.meeboss.components.bottomsheet.common.IndexGroupBean
import com.twl.meeboss.core.network.getService


class SelectAreaCodeViewModel : BaseMviViewModel<SelectAreaCodeUiState, SelectAreaCodeUiIntent>() {

    override fun initUiState(): SelectAreaCodeUiState = SelectAreaCodeUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is SelectAreaCodeUiIntent.Init -> {
                getAllAreaCode()
            }

            is SelectAreaCodeUiIntent.UpdateInput -> {
                sendUiState {
                    copy(input = intent.input)
                }
                getFilterList()
            }

            else -> {

            }

        }
    }

    private fun getFilterList() {
        val input = uiStateFlow.value.input.text
        if (input.isEmpty()) {
            sendUiState {
                copy(searchList = listOf())
            }
            return
        }
        launcherOnIO {
            val list = uiStateFlow.value.dataSource.indexList.flatMap { it.list }
            val listAfterFilter = mutableListOf<CommonAreaCodeBean>()

            list.forEach {
                if (it.getItemName().contains(input, ignoreCase = true)) {
                    listAfterFilter.add(it)
                }
            }
            sendUiState {
                copy(searchList = listAfterFilter.distinctBy { it.countryName })
            }

        }

    }

    private fun getAllAreaCode() {
        launcherOnIO {
            getService(BaseApi::class.java).getCallingCodeList()
                .toResult()
                .onSuccess {
                    //转成分组数据
                    val topList = it.popular.map { bean ->
                        IndexGroupBean(
                            groupName = bean.firstLetter,
                            list = bean.list
                        )
                    }
                    //转成分组数据
                    val list = it.all.map { bean ->
                        IndexGroupBean(
                            groupName = bean.firstLetter,
                            list = bean.list
                        )
                    }

                    sendUiState {
                        copy(
                            dataSource = dataSource.copy(
                                indexes = list.map { bean -> bean.groupName },
                                indexList = topList + list,
                            ).calculate(topList, list)
                        )
                    }
                }
                .onFailure {
                    getAllAreaCode()
                }
        }
    }

}

data class SelectAreaCodeUiState(
    val input: TextFieldValue = TextFieldValue(),
    val dataSource: IndexComponentDataSource<CommonAreaCodeBean> = IndexComponentDataSource(),
    val searchList: List<CommonAreaCodeBean> = listOf()
) : IUiState

sealed class SelectAreaCodeUiIntent : IUiIntent {
    data object Init : SelectAreaCodeUiIntent()
    data class UpdateInput(val input: TextFieldValue) : SelectAreaCodeUiIntent()
}