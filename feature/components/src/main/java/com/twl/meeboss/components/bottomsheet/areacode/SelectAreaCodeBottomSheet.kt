package com.twl.meeboss.components.bottomsheet.areacode

import android.content.DialogInterface
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.twl.meeboss.base.model.common.CommonAreaCodeBean
import com.twl.meeboss.components.R
import com.twl.meeboss.components.bottomsheet.common.IndexBottomSheetComponent
import com.twl.meeboss.components.bottomsheet.common.IndexComponentDataSource
import com.twl.meeboss.components.bottomsheet.common.IndexGroupBean
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.utils.dismissSafely

/**
 * 选择地区码半弹层
 */
class SelectAreaCodeBottomSheet : CommonBottomDialogFragment() {

    private var onCancel: (()-> Unit) ?= null
    private var onSelected: ((CommonAreaCodeBean) -> Unit)? = null

    companion object {
        fun newInstance(
            onCancel: (() -> Unit)? = null,
            onSelected: ((CommonAreaCodeBean) -> Unit)? = null
        ) = SelectAreaCodeBottomSheet().apply {
            this.onCancel = onCancel
            this.onSelected = onSelected
        }
    }
    @Composable
    override fun DialogContent() {
        val viewModel: SelectAreaCodeViewModel = viewModel()
        LaunchedEffect(Unit) {
            viewModel.sendUiIntent(SelectAreaCodeUiIntent.Init)
        }
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        IndexBottomSheetComponent(title = stringResource(id = R.string.sign_up_phone_change_country),
            placeHolder = R.string.common_search_location,
            input = uiState.input,
            dataSource = uiState.dataSource,
            searchList = uiState.searchList,
            onSelected = {
                onSelected?.invoke(it)
                dismissSafely()
            },
            onTextChanged = {
                viewModel.sendUiIntent(SelectAreaCodeUiIntent.UpdateInput(it))
            },
            onCloseClick = {
                onCancel?.invoke()
                dismissSafely()
            })
    }

    override fun getDialogHeight(): Int {
        return ViewGroup.LayoutParams.MATCH_PARENT
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        onCancel?.invoke()
    }

}

@Preview
@Composable
private fun PreviewSelectAreaCodeContent() {
    val list = listOf(
        IndexGroupBean(
            groupName = "A",
            listOf(
                CommonAreaCodeBean(countryName = "Apple", smsPrefix = "+1"),
                CommonAreaCodeBean(countryName = "Apple", smsPrefix = "+1"),
                CommonAreaCodeBean(countryName = "Apple", smsPrefix = "+1")
            )
        ),
        IndexGroupBean(
            groupName = "B",
            listOf(
                CommonAreaCodeBean(countryName = "Banana", smsPrefix = "+86"),
                CommonAreaCodeBean(countryName = "Banana", smsPrefix = "+123"),
                CommonAreaCodeBean(countryName = "Banana", smsPrefix = "+145")
            )
        ),
    )
    val indexes = listOf(
        "A",
        "B",
        "C",
        "D",
        "E",
        "F",
        "G",
        "H",
        "I",
        "J",
        "K",
        "L",
        "M",
        "N",
        "O",
        "P",
        "Q",
        "R",
        "S",
        "T",
        "U",
        "V",
        "W",
        "X",
        "Y",
        "Z"
    )
    IndexBottomSheetComponent(title = stringResource(id = R.string.sign_up_phone_change_country),
        placeHolder = R.string.common_search_location,
        input = TextFieldValue(""),
        dataSource = IndexComponentDataSource<CommonAreaCodeBean>(
            indexList = list,
            indexes = indexes
        ),
        searchList = listOf(),
        onSelected = {},
        onTextChanged = {},
        onCloseClick = {})
}