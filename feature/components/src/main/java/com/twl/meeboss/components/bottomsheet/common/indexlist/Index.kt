package com.twl.meeboss.components.bottomsheet.common.indexlist

import android.util.Log
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.PointerEventType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.positionChange
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.common.ktx.toPx
import com.twl.meeboss.core.ui.theme.Primary
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.abs

private const val TAG = "Index"

@Composable
fun Index(
    modifier: Modifier = Modifier,
    charList: List<String> = ('A'..'Z').map { it.toString() }.toList(),
    onSlide: (String) -> Unit,
) {
    Log.d(TAG, "Index: $charList")

    Box(
        modifier = modifier
            .wrapContentWidth()
            .wrapContentHeight(),
        contentAlignment = Alignment.Center
    ) {
        var columnHeightPx by remember {
            mutableFloatStateOf(0f)
        }
        var charHeightPx by remember {
            mutableFloatStateOf(0f)
        }
        val coroutineScope = rememberCoroutineScope()
        key(charList) {
            val configuration = LocalConfiguration.current
            val screenHeight = configuration.screenHeightDp.dp
            Column(
                modifier = Modifier
                    .onGloballyPositioned { coordinates ->
                        columnHeightPx = coordinates.size.height.toFloat() - 12.toPx
                    }
                    .wrapContentSize(align = Alignment.Center)
                    .heightIn(max = screenHeight * 2 / 3)
                    .align(Alignment.CenterEnd)
                    .pointerInput(Unit) {
                        awaitPointerEventScope {
                            while (true) {
                                val event = awaitPointerEvent()
                                val pos = event.changes.first().position
                                var targetChar: String?
                                if (event.type == PointerEventType.Press || event.type == PointerEventType.Move) {
                                    var targetCharPosition = (pos.y / charHeightPx).toInt()
                                    if (targetCharPosition < 0) {
                                        targetCharPosition = 0
                                    }
                                    if (targetCharPosition > charList.size - 1) {
                                        targetCharPosition = charList.size - 1
                                    }

                                    Log.d(TAG, ">>> Index: $charList")

                                    targetChar = charList[targetCharPosition]

                                    onSlide(targetChar)
                                } else {
                                    coroutineScope.launch {
                                        delay(200)
                                        onSlide(Char.MIN_VALUE.toString())
                                    }
                                }

                                event.changes.forEach {
                                    val offset = it.positionChange()
                                    if (abs(offset.y) > 0f) {
                                        it.consume()
                                    }
                                }
                            }
                        }
                    },
            ) {
                charList.forEach {
                    Text(
                        text = it.toString(),
                        modifier = Modifier
                            .padding(horizontal = 3.dp)
                            .width(7.dp)
                            .height(14.dp)
                            .onGloballyPositioned { coordinates ->
                                charHeightPx = coordinates.size.height.toFloat()
                            },
                        textAlign = TextAlign.Center,
                        style = TextStyle(
                            fontSize = 10.sp,
                            fontWeight = FontWeight(500),
                            color = Primary,
                        )
                    )
                }
            }
        }
    }
}

@Preview(showSystemUi = true)
@Composable
fun PreviewIndex() {
    Index(
//        charList = "不, 中, 丹, 也, 亞, 以, 伊, 佛, 保, 克, 冰, 列, 利, 剛, 加, 匈, 千, 南, 卡, 印, 厄, 吉, 哈, 哥, 喀, 喬, 土, 坦, 埃, 塔, 塞, 墨, 多, 奈, 奧, 委, 孟, 安, 宏, 尚, 尼, 巴, 布, 希, 帛, 幾, 庫, 德, 愛, 拉, 挪, 捷, 摩, 斐, 斯, 新, 日, 智, 東, 查, 柬, 格, 模, 比, 汶, 沙, 法, 波, 泰, 海, 澳, 烏, 牙, 特, 獅, 玻, 瑞, 瓜, 瓦, 甘, 留, 白, 百, 盧, 直, 祕, 科, 突, 立, 約, 納, 紐, 索, 緬, 羅, 美, 義, 老, 聖, 肯, 芬, 英, 茅, 荷, 莫, 菲, 葛, 葡, 蒙, 蒲, 蓋, 薩, 蘇, 衣, 西, 象, 貝, 賴, 賽, 赤, 越, 辛, 迦, 開, 關, 阿, 香, 馬, 黎"
//            .split(", ").map { it.first() },
        onSlide = {},
    )
}