package com.twl.meeboss.components.language

import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.twl.meeboss.base.R
import com.twl.meeboss.base.components.indexlist.IndexListComponent
import com.twl.meeboss.base.components.indexlist.IndexListItem
import com.twl.meeboss.base.model.IndexBean
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.components.language.preview.LanguagePreviewProvider
import com.twl.meeboss.components.language.preview.PreviewLanguageData
import com.twl.meeboss.core.ui.component.XDivider
import com.twl.meeboss.core.ui.component.item.XThemeTagItem
import com.twl.meeboss.core.ui.component.textfield.XSearchTextField
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black020202
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.COLOR_888888
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.theme.alpha
import com.twl.meeboss.core.ui.theme.xSmall
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.toResourceString

/**
 * 多选语言底部弹窗
 */
class MultiSelectLanguageBottomSheet : CommonBottomDialogFragment() {

    private val selectedList:List<IndexBean> by lazy {
        arguments?.getSerializable("selectedList") as? List<IndexBean> ?: emptyList()
    }

    private val maxCount:Int by lazy {
        arguments?.getInt("maxCount") ?: 5
    }
    private var callback:((List<IndexBean>)->Unit)? = null

     companion object {
         fun newInstance(
             selectedList: List<IndexBean>,
             maxCount: Int = 5,
             callback: (List<IndexBean>) -> Unit
         ) = MultiSelectLanguageBottomSheet().apply {
             this.callback = callback
             arguments = Bundle().apply {
                 putSerializable("selectedList", ArrayList(selectedList))
                 putInt("maxCount", maxCount)
             }
         }
     }

    @Composable
    override fun DialogContent() {
        val context = LocalContext.current
        val vm: MultiSelectLanguageViewModel = viewModel()
        LaunchedEffect(key1 = vm) {
            vm.sendUiIntent(MultiSelectLanguageUiIntent.Init(selectedList.toList(), 5))
        }
        val uiState by vm.uiStateFlow.collectAsStateWithLifecycle()
        val focusManager = LocalFocusManager.current
        MultiSelectLanguageContent(
            uiState = uiState,
            onCloseClick = {
                dismissSafely()
            },
            onInputChanged = {
                vm.sendUiIntent(MultiSelectLanguageUiIntent.InputChanged(it))
            },
            onSelected = {
                val selectedKeyList = uiState.selectedList.map { i->i.localUniqueKey() }
                if(selectedKeyList.size == uiState.maxCount){
                    T.ss(R.string.choose_up_to_max.toResourceString(context,uiState.maxCount.toString()))
                    return@MultiSelectLanguageContent
                }
                if (selectedKeyList.contains(it.localUniqueKey())) {
                    T.ss(R.string.common_already_selected_this_one)
                    return@MultiSelectLanguageContent
                }
                focusManager.clearFocus(true)
                vm.sendUiIntent(MultiSelectLanguageUiIntent.Selected(it))
            },
            onDeleted = {
                vm.sendUiIntent(MultiSelectLanguageUiIntent.Deleted(it))
            },
            onClickSave = {
                vm.uiStateFlow.value.selectedList.let { list ->
                    callback?.invoke(list)
                }
                dismissSafely()
            }
        )
    }

}


@Composable
private fun MultiSelectLanguageContent(
    modifier: Modifier = Modifier,
    uiState: MultiSelectLanguageUiState = MultiSelectLanguageUiState(),
    onInputChanged: (TextFieldValue) -> Unit = {},
    onCloseClick: () -> Unit = {},
    onSelected: (IndexBean) -> Unit = {},
    onDeleted: (IndexBean) -> Unit = {},
    onClickSave: () -> Unit = {}
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .background(MaterialTheme.colorScheme.background)
    ) {
        Row(modifier = Modifier.height(60.dp), verticalAlignment = Alignment.CenterVertically) {
            Image(
                painter = painterResource(id = R.drawable.ui_dailog_close),
                modifier = Modifier
                    .padding(16.dp, 0.dp)
                    .size(24.dp)
                    .noRippleClickable {
                        onCloseClick()
                    },
                contentDescription = "Close dialog"
            )
            Spacer(modifier = Modifier.weight(1f))
            Text(
                text = stringResource(id = R.string.common_button_save),
                fontSize = 16.sp,
                modifier = Modifier
                    .padding(end = 16.dp)
                    .noRippleClickable {
                        onClickSave()
                    },
                color = Secondary,
                fontWeight = FontWeight.Medium
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth(), verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(id = R.string.common_language),
                modifier = Modifier.padding(16.dp, 0.dp),
                fontSize = 28.sp, fontWeight = FontWeight.SemiBold,
                color = Black020202
            )
            Spacer(modifier = Modifier.weight(1f))
            val count = uiState.selectedList.size
            Text(
                text = count.toString(),
                fontSize = 28.sp,
                modifier = Modifier
                    .padding(0.dp, 0.dp),
                maxLines = 1,
                color = if (count == 0) Secondary.alpha(0.5f) else Secondary,
                fontWeight = FontWeight.SemiBold
            )

            Text(
                text = "/${uiState.maxCount}",
                fontSize = 28.sp,
                modifier = Modifier
                    .padding(0.dp, 0.dp, 16.dp, 0.dp),
                maxLines = 1,
                color = Black222222,
                fontWeight = FontWeight.SemiBold
            )
        }
        XSearchTextField(
            modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 20.dp),
            value = uiState.input, placeHolder = R.string.common_search_language,
            onValueChange = onInputChanged
        )
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
        ) {
            if (uiState.input.text.isBlank()) {
                Column {
                    IndexListComponent(
                        modifier = Modifier
                            .padding(top = 20.dp)
                            .fillMaxWidth()
                            .weight(1F),
                        indexList = uiState.indexList,
                        indexes = uiState.indexes,
                        positionMap = uiState.positionMap,
                        onSelected = onSelected
                    )
                    if (uiState.selectedList.isNotEmpty()) {
                        XDivider()
                        Row(
                            modifier = Modifier.padding(vertical = 16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                modifier = Modifier.padding(start = 16.dp, end = 12.dp),
                                text = "Selected：",
                                style = xSmall.copy(color = COLOR_888888)
                            )
                            LazyRow {
                                items(uiState.selectedList.size) { index ->
                                    XThemeTagItem(
                                        modifier = Modifier.padding(end = 12.dp),
                                        text = uiState.selectedList[index].getItemName(),
                                        horizontalPadding = 12.dp,
                                        verticalPadding = 8.dp,
                                        radius = 28.dp,
                                        onRemoveClick = {
                                            onDeleted(uiState.selectedList[index])
                                        }
                                    )
                                }
                            }
                        }
                    }

                }

            } else {
                if (uiState.searchList.isEmpty()) {
                    IndexListItem(
                        modifier.padding(start = 16.dp, end = 16.dp, top = 8.dp),
                        text = stringResource(id = R.string.common_no_search_result),
                        showDivider = true
                    )
                } else {
                    LazyColumn(
                        modifier = Modifier
                            .padding(start = 16.dp, end = 16.dp, top = 8.dp)
                            .fillMaxSize()
                    ) {
                        items(uiState.searchList.size) { index ->
                            IndexListItem(
                                modifier = Modifier.noRippleClickable {
                                    onSelected(uiState.searchList[index])
                                },
                                text = uiState.searchList[index].getItemName(),
                                showDivider = index != uiState.searchList.size - 1
                            )
                        }
                    }
                }

            }
        }
    }
}


@Preview
@Composable
private fun PreviewMultiSelectLanguageContent(@PreviewParameter(LanguagePreviewProvider::class) data: PreviewLanguageData) {
    MultiSelectLanguageContent(
        uiState = MultiSelectLanguageUiState(
            input = TextFieldValue(data.input),
            indexList = data.indexList,
            indexes = data.indexes,
            positionMap = data.positionMap,
            searchList = data.searchList,
            selectedList = data.selectedList
        )
    )
}