package com.twl.meeboss.components.cards

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.components.R
import com.twl.meeboss.core.ui.component.button.XCommonSmallOutlineButton
import com.twl.meeboss.core.ui.utils.noRippleClickable

/**
 * @author: musa on 2025/06/09
 * @e-mail: <EMAIL>
 * @desc: 两个操作按钮的卡片
 */
@Composable
fun UserTwoButtonCard(
    modifier: Modifier = Modifier,
    title: String,
    @DrawableRes iconResId: Int,
    leftButtonText: String,
    rightButtonText: String,
    onLeftButtonClick: () -> Unit = {},
    onRightButtonClick: () -> Unit = {},
    onCloseClick: () -> Unit = {},
) {
    Box(modifier = modifier
        .background(Color.White, RoundedCornerShape(12.dp))) {
        Icon(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(12.dp)
                .size(50.dp, 54.dp),
            painter = painterResource(iconResId),
            contentDescription = "",
            tint = Color.Unspecified
        )
        Column(Modifier.padding(horizontal = 16.dp, vertical = 20.dp)) {
            Row {
                Text(
                    modifier = Modifier.weight(1f),
                    text = title,
                    style = TextStyle(
                        fontSize = 16.sp,
                        lineHeight = 24.sp,
                        fontWeight = FontWeight(510),
                        color = Color(0xFF000000),
                    )
                )

                Icon(
                    modifier = Modifier
                        .padding(start = 16.dp)
                        .size(12.dp)
                        .noRippleClickable {
                            onCloseClick()
                        },
                    painter = painterResource(id = R.drawable.ui_item_close),
                    tint = Color.Unspecified,
                    contentDescription = ""
                )
            }

            Row(modifier = Modifier.padding(top = 16.dp)){
                XCommonSmallOutlineButton(
                    contentPadding = PaddingValues(horizontal = 26.dp, vertical = 8.dp),
                    buttonText = leftButtonText,
                    onClick = onLeftButtonClick,
                )

                XCommonSmallOutlineButton(
                    modifier = Modifier.padding(start = 16.dp),
                    contentPadding = PaddingValues(horizontal = 26.dp, vertical = 8.dp),
                    buttonText = rightButtonText,
                    onClick = onRightButtonClick,
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewUserTwoButtonCard() {
    UserTwoButtonCard(modifier = Modifier.fillMaxWidth(),
        title = stringResource(R.string.job_seeker_job_prep_visa_sponsor_ask), iconResId = R.drawable.ui_icon_visa, leftButtonText = "Yes", rightButtonText = "No")
}