package com.twl.meeboss.components.cards

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.components.R
import com.twl.meeboss.core.ui.component.button.XCommonSmallButton
import com.twl.meeboss.core.ui.utils.noRippleClickable

/**
 * 新手引导卡片
 */
@Composable
fun UserBeginnerGuideCard(
    modifier: Modifier = Modifier,
    @StringRes title:Int,
    @StringRes content:Int,
    hasContent:Boolean = false,
    @StringRes buttonText:Int,
    @DrawableRes icon:Int,
    closable:Boolean = false,
    onCardShow: () -> Unit = {},
    onCardClick: () -> Unit = {},
    onCloseClick: () -> Unit = {}
) {
    val scope = rememberCoroutineScope()
    F1ListCardContainer(modifier = modifier) {
        Box {
            Icon(
                modifier = Modifier.align(Alignment.BottomEnd),
                painter = painterResource(icon),
                contentDescription = "",
                tint = Color.Unspecified
            )

            Column {
                Row(modifier = Modifier.padding(16.dp)) {
                    Text(
                        modifier = Modifier.weight(1F),
                        text = stringResource(title),
                        style = TextStyle(
                            fontSize = 16.sp,
                            lineHeight = 24.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.Black,
                        )
                    )
                    if (closable) {
                        Icon(
                            modifier = Modifier
                                .padding(start = 10.dp)
                                .noRippleClickable {
                                    onCloseClick()

                                },
                            painter = painterResource(id = R.drawable.ui_item_close),
                            tint = Color.Unspecified,
                            contentDescription = ""
                        )
                    }

                }

                if (hasContent) {
                    Text(
                        modifier = Modifier.padding(start = 16.dp, end = 16.dp, bottom = 16.dp),
                        text = stringResource(id = content),
                        style = TextStyle(
                            fontSize = 14.sp,
                            lineHeight = 20.sp,
                            fontWeight = FontWeight.Normal,
                            color = Color.Gray,
                        )
                    )
                }

                XCommonSmallButton(modifier = Modifier
                    .padding(start = 16.dp, bottom = 16.dp),
                    buttonText = stringResource(buttonText), onClick = {
                        onCardClick()
                    }
                )
            }
        }
    }
    scope.run {
        onCardShow()
    }
}



@Preview
@Composable
private fun PreviewGeekBeginnerGuideCard() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Gray)
            .verticalScroll(rememberScrollState())
    ) {
        UserBeginnerGuideCard(title = R.string.common_unverified_email_hint_card_title,
            content = R.string.common_unverified_email_hint_card_content,
            buttonText = R.string.common_unverified_email_hint_card_button,
            icon = R.drawable.user_guide_verify_email,
            closable = true,
            hasContent = true)


        UserBeginnerGuideCard(title = R.string.geek_card_workexp_complete,
            content = 0,
            buttonText = R.string.common_unverified_email_hint_card_button,
            icon = R.drawable.user_guide_verify_email,
            closable = true,
            hasContent = false)
    }
}
