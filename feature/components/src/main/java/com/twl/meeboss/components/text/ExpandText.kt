package com.twl.meeboss.components.text

import android.util.TypedValue
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.ActionBar.LayoutParams
import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.viewinterop.AndroidView
import com.twl.meeboss.base.view.ExpandTextUtil
import com.twl.meeboss.common.ktx.toPx
import com.twl.meeboss.components.R
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.Primary

@Composable
fun ExpandText(
    modifier: Modifier = Modifier,
    text: CharSequence,
    maxLines: Int = 3,
    textColor: Color = Black888888,
    textSize: Float = 15f,
    lineHeight: Float = 22f,
    expandText:String = stringResource(id = R.string.common_lower_case_more),
    expandTextColor:Color = Primary
) {
    val context = LocalContext.current
    Column(modifier = modifier) {
        AndroidView(
            factory = {
                val tv = TextView(it)
                val layoutParams = LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                tv.layoutParams = layoutParams
                tv.setTextColor(textColor.toArgb())
                tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, textSize)
                tv.lineHeight = lineHeight.toPx
                tv
            },
            update = {
                val expandTextUtil = ExpandTextUtil(context)
                    .setMaxLine(maxLines)
                    .setExpandStr(expandText)
                    .setColorStr(expandTextColor.toArgb())
                expandTextUtil.show(it, text)
            }
        )
    }
}


@Preview
@Composable
private fun PreviewExpandText() {
    Column {
        ExpandText(
            text = stringResource(id = R.string.ui_common_long_place_holder)
        )
    }
}