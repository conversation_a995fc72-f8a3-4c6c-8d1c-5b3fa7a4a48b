package com.twl.meeboss.login.activity

import android.app.Activity
import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import com.twl.meeboss.base.constants.BUNDLE_PHONE
import com.twl.meeboss.base.constants.BUNDLE_STRING
import com.twl.meeboss.base.google.login.GoogleLoginManager
import com.twl.meeboss.base.ktx.showRobotCheckDialog
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.login.components.PasswordLoginContent
import com.twl.meeboss.login.export.LoginRouterPath
import com.twl.meeboss.login.util.LoginPointReporter
import com.twl.meeboss.login.viewmodel.PasswordLoginUiIntent

class PhonePasswordLoginActivity : BasePasswordLoginActivity() {
    private val mGoogleLoginManager = GoogleLoginManager(this)
    override fun preInit(intent: Intent) {
        super.preInit(intent)
        mGoogleLoginManager.initClient()
        viewModel.account = intent.getStringExtra(BUNDLE_PHONE) ?: ""
        viewModel.isEmail = false
        viewModel.robotCheckToken = intent.getStringExtra(BUNDLE_STRING) ?: ""
    }

    override fun onGoogleLogin() {
        LoginPointReporter.login(5)
        mGoogleLoginManager.loginWithGoogle {
            viewModel.sendUiIntent(PasswordLoginUiIntent.LoginByGoogle(it))
        }
    }

    override fun onClickNext() {
        this.showRobotCheckDialog(viewModel.robotCheckToken) {
            viewModel.robotCheckToken = it
            viewModel.sendUiIntent(PasswordLoginUiIntent.PhonePasswordLogin)
        }
    }

    override fun onClickForgotPassword() {
        CommonVerificationCodeActivity.intent(this, viewModel.account, true,
            LoginRouterPath.LOGIN_VERIFY_CODE_FORGET)

    }

    override fun onClickSignByVerificationCode() {
        this.showRobotCheckDialog(viewModel.robotCheckToken) {
            viewModel.robotCheckToken = it
            PhoneLoginActivity.intent(this, viewModel.account, it,false)
        }
    }

    companion object {
        fun intent(activity: Activity, phoneNumber: String,robotCheckToken: String) {
            Intent(activity, PhonePasswordLoginActivity::class.java).run {
                putExtra(BUNDLE_PHONE, phoneNumber)
                putExtra(BUNDLE_STRING, robotCheckToken)
                activity.startActivity(this)
            }
        }
    }
}


@Preview
@Composable
fun PreviewPhonePasswordLoginActivity() {
    XTheme {
        PasswordLoginContent()
    }
}
