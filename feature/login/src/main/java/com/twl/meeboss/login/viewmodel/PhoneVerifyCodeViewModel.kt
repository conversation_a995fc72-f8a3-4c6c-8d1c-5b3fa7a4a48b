package com.twl.meeboss.login.viewmodel

import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.config.AreaCodeHelper
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.http.isRobotCheckCodeFail
import com.twl.meeboss.base.ktx.buildAreaCodeParam
import com.twl.meeboss.base.ktx.toFormatPhoneNumber
import com.twl.meeboss.base.manager.ABUpdateManager
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.HttpResult
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.login.R
import com.twl.meeboss.login.api.LoginApi
import com.twl.meeboss.login.util.LoginPointReporter
import com.twl.meeboss.login.util.getRegisterIdentityParam
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class PhoneVerifyCodeLoginViewModel @Inject constructor(
    //private val repos: BossJobRepository
) : VerifyCodeLoginBaseViewModel() {
    var phoneNumber: String = ""

    fun showPhoneNumber(): String {
        return phoneNumber.toFormatPhoneNumber(true)
    }

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is PhoneVerifyCodeLoginUiIntent.Save ->{
            }
            else->{
                super.handleIntent(intent)
            }

        }
    }

    override fun getVerificationCode() {
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val areaCode = AreaCodeHelper.getAreaCode()
            val sendSmsCodeResult = service.sendSmsCode(phoneNumber,
                if (isRegister) "1" else "3", robotCheckToken,
                buildAreaCodeParam(areaCode.countryCode,areaCode.smsPrefix)
            )
            if (sendSmsCodeResult.isSuccess) {
                T.ss(R.string.setting_verification_code_sent_successfully)
                countDown.start()
            } else {
                when {
                    sendSmsCodeResult is HttpResult.ApiError -> {
                        T.ss(sendSmsCodeResult.message)
                        if(sendSmsCodeResult.isRobotCheckCodeFail()){
                            robotCheckToken = ""
                            reShowRobotCheckForGetCode.postValue(true)
                        }
                    }

                    else -> {
                        T.ss(R.string.setting_send_code_fail)
                    }
                }
            }
        }
    }

    override fun register() {
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val registerByPhoneResult = service.registerByPhone(phoneNumber, uiStateFlow.value.code, getRegisterIdentityParam(), AreaCodeHelper.getAreaCodeAsParams())
            if (registerByPhoneResult.isSuccess) {
                registerByPhoneResult.getOrNull()?.run {
                    AccountManager.activeAccount(this)
                    ABUpdateManager.update().let {
                        registerResult.postValue(this)
                        if(register){
                            LoginPointReporter.registerSuccess(1)
                        } else {
                            LoginPointReporter.loginSuccess(1)
                        }
                    }

                }
            } else {
                when {
                    registerByPhoneResult is HttpResult.ApiError -> {
                        setError(registerByPhoneResult.message)
                    }
                }
            }
        }
    }

    override fun loginByCode() {
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val loginByPhoneCodeResult = service.loginByPhoneCode(phoneNumber, uiStateFlow.value.code, robotCheckToken, getRegisterIdentityParam(),AreaCodeHelper.getAreaCodeAsParams())
            handleLoginResult(loginByPhoneCodeResult,false)
        }
    }

    override fun isEmail(): Boolean = false

}

sealed class PhoneVerifyCodeLoginUiIntent : IUiIntent {
    data object Save : PhoneVerifyCodeLoginUiIntent()
}