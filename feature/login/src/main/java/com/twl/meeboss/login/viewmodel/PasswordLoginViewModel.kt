package com.twl.meeboss.login.viewmodel

import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.repo.toResult
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.http.HttpErrorCodes
import com.twl.meeboss.base.http.isRobotCheckCodeFail
import com.twl.meeboss.base.ktx.checkPasswordSecVersion
import com.twl.meeboss.base.ktx.containSpace
import com.twl.meeboss.base.ktx.toEncryptPassword
import com.twl.meeboss.base.ktx.toFormatPhoneNumber
import com.twl.meeboss.base.ktx.toPhoneWithAreaCode
import com.twl.meeboss.base.manager.ABUpdateManager
import com.twl.meeboss.base.model.LoginResult
import com.twl.meeboss.common.exp.ApiException
import com.twl.meeboss.common.ktx.hasEmoji
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.HttpResult
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.core.ui.component.InputState
import com.twl.meeboss.login.R
import com.twl.meeboss.login.activity.VerifyEmailCodeActIntent
import com.twl.meeboss.login.activity.VerifyEmailCodeActivity
import com.twl.meeboss.login.api.LoginApi
import com.twl.meeboss.login.util.LoginPointReporter
import com.twl.meeboss.login.util.getRegisterIdentityParam
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class PasswordLoginViewModel @Inject constructor() :
    BaseMviViewModel<PasswordLoginUiState, PasswordLoginUiIntent>() {

    val loginResultLiveData: MutableLiveData<Boolean> = MutableLiveData()

    val loginNeedEmailLink: MutableLiveData<Boolean> = MutableLiveData()

    val loginNeedEmailRegister: MutableLiveData<Boolean> = MutableLiveData()

    val reShowRobotCheck: MutableLiveData<Boolean> = MutableLiveData()


    var account: String = ""

    var robotCheckToken: String = ""

    var isEmail: Boolean = false

    override fun initUiState(): PasswordLoginUiState = PasswordLoginUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is PasswordLoginUiIntent.ChangeMode -> {
                sendUiState { copy(isPhoneLogin = intent.isPhoneLogin) }
            }

            is PasswordLoginUiIntent.InputPassword -> {
                if (intent.password.hasEmoji()) {
                    return
                }
                sendUiState {
                    copy(
                        password = intent.password,
                        inputState = InputState.Focus,
                        errorMessage = ""
                    )
                }
            }

            is PasswordLoginUiIntent.StateChanged -> {
                sendUiState { copy(inputState = intent.state) }
            }

            is PasswordLoginUiIntent.EmailPasswordLogin -> {
                emailPasswordLogin()
            }

            is PasswordLoginUiIntent.PhonePasswordLogin -> {
                phonePasswordLogin()
            }

            is PasswordLoginUiIntent.LoginByGoogle -> {
                loginByGoogle(intent.token)
            }

            else -> {

            }

        }
    }

    fun showAccount(): String {
        return if (uiStateFlow.value.isPhoneLogin) account.toFormatPhoneNumber() else account
    }

    private fun phonePasswordLogin() {
        if (uiStateFlow.value.password.containSpace()) {
            return
        }
        showLoadingDialog()
        launcherOnIO {
            checkPasswordSecVersion(phone = account.toPhoneWithAreaCode()) { isSecVersion2, secVersion ->
                getService(LoginApi::class.java).loginByPhonePassword(
                    account,
                    if (isSecVersion2) uiStateFlow.value.password.toEncryptPassword() else uiStateFlow.value.password,
                    robotCheckToken,
                    getRegisterIdentityParam(),
                    secVersion = secVersion
                ).toResult().onSuccess {
                    AccountManager.activeAccount(it,false)
                    loginNeedEmailLink.postValue(true)
                }.onFailure {
                    dismissLoadingDialog()
                    if (it is ApiException) {
                        if (it.isRobotCheckCodeFail { T.ss(it.message) }) {
                            robotCheckToken = ""
                            reShowRobotCheck.postValue(true)
                        } else {
                            T.ss(it.message)
                            sendUiState {
                                copy(
                                    inputState = InputState.Error,
                                    errorMessage = it.message.notNull()
                                )
                            }
                        }
                    }
                }
            }
        }

    }

    private fun emailPasswordLogin() {
        if (uiStateFlow.value.password.containSpace()) {
            return
        }
        showLoadingDialog()
        launcherOnIO {
            checkPasswordSecVersion(email = account) { isSecVersion2, secVersion ->
                val service = getService(LoginApi::class.java)
                val phoneLoginResult = service.loginByEmail(
                    email = account,
                    password = if (isSecVersion2) uiStateFlow.value.password.toEncryptPassword() else uiStateFlow.value.password,
                    cloudflareToken = robotCheckToken,
                    identity = getRegisterIdentityParam(),
                    secVersion = secVersion
                )
                dismissLoadingDialog()
                handleLoginResult(phoneLoginResult, false)
            }

        }

    }

    private suspend fun handleLoginResult(loginResult: HttpResult<LoginResult>, isGoogle: Boolean) {
        if (loginResult.isSuccess) {
            loginResult.getOrNull()?.run {
                AccountManager.activeAccount(this)
                ABUpdateManager.update().let {
                    loginResultLiveData.postValue(true)
                    T.ss(R.string.login_signed_your_account)
                    if (register) {
                        LoginPointReporter.registerSuccess(if (isGoogle) 3 else 2)
                    } else {
                        LoginPointReporter.loginSuccess(if (isGoogle) 5 else if (isEmail) 4 else 2)
                    }
                }

            }
        } else {
            when {
                loginResult is HttpResult.ApiError -> {
                    if (loginResult.code == HttpErrorCodes.LOGIN_NEED_EMAIL_VERIFY) {
                        loginNeedEmailRegister.postValue(true)
                    } else if (loginResult.isRobotCheckCodeFail {
                            T.ss(loginResult.message)
                        }) {
                        robotCheckToken = ""
                        reShowRobotCheck.postValue(true)
                    } else {
                        T.ss(loginResult.message)
                        sendUiState {
                            copy(
                                inputState = InputState.Error,
                                errorMessage = loginResult.message.notNull()
                            )
                        }
                    }
                }

                else -> {
                    T.ss(loginResult.exceptionOrNull()?.message)
                }
            }
        }
    }

    private fun loginByGoogle(token: String) {
        showLoadingDialog()
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val phoneLoginResult = service.loginByGoogle(token, getRegisterIdentityParam())
            dismissLoadingDialog()
            handleLoginResult(phoneLoginResult, true)
        }
    }

}

data class PasswordLoginUiState(
    val isPhoneLogin: Boolean = true,
    val password: String = "",
    val inputState: InputState = InputState.None,
    val errorMessage: String = ""
) : IUiState

sealed class PasswordLoginUiIntent : IUiIntent {
    data class ChangeMode(val isPhoneLogin: Boolean) : PasswordLoginUiIntent()
    data class InputPassword(val password: String) : PasswordLoginUiIntent()
    data class StateChanged(val state: InputState) : PasswordLoginUiIntent()
    data class LoginByGoogle(val token: String) : PasswordLoginUiIntent()
    data object EmailPasswordLogin : PasswordLoginUiIntent()
    data object PhonePasswordLogin : PasswordLoginUiIntent()
}
