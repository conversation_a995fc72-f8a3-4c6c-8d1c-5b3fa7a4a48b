package com.twl.meeboss.login.viewmodel

import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.ktx.containSpace
import com.twl.meeboss.base.manager.PasswordValidator
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.HttpResult
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.core.ui.component.InputState
import com.twl.meeboss.login.api.LoginApi
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import com.twl.meeboss.base.ktx.checkPasswordSecVersion
import com.twl.meeboss.base.ktx.toEncryptPassword
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.login.R
import com.twl.meeboss.login.util.LoginPointReporter

@HiltViewModel
class SetPasswordViewModel @Inject constructor(
    //private val repos: BossJobRepository
) : BaseMviViewModel<SetPasswordUiState, SetPasswordUiIntent>() {

    val passwordResult = MutableLiveData<Boolean>()

    override fun initUiState(): SetPasswordUiState = SetPasswordUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is SetPasswordUiIntent.Save -> {
                setPassword()
            }
            is SetPasswordUiIntent.InputPassword -> {
                onPasswordChanged(intent.password)
            }
            is SetPasswordUiIntent.InputStateChanged -> {
                launcherOnIO {
                    val errorMessage = if (intent.state != InputState.Focus) {
                        val code = uiStateFlow.value.password
                        if (code.isEmpty()) {
                            ""
                        } else {
                            PasswordValidator.checkPasswordStrength(code).toast ?: ""
                        }
                    } else {
                        ""
                    }
                    sendUiState { copy(inputState = intent.state, errorMessage = errorMessage) }
                }
            }

            else -> {

            }

        }
    }

    private fun onPasswordChanged(code: String) {
        if (code.isNotEmpty() && code.length !in 8..32) {
            sendUiState {
                copy(
                    password = code,
                    inputState = InputState.Error,
                    errorMessage = R.string.common_password_range.toResourceString()
                )
            }
        } else {
            sendUiState {
                copy(
                    password = code,
                    inputState = InputState.Focus,
                    errorMessage = ""
                )
            }
        }
    }

    private fun setPassword() {
        val password = uiStateFlow.value.password
        if (password.containSpace()) {
            return
        }
        showLoadingDialog()
        launcherOnIO {
            val result = PasswordValidator.checkPasswordStrength(password)
            if (!result.checkResult) {
                dismissLoadingDialog()
                LoginPointReporter.setPassword(1, false)
                val error = result.toast
                if (!error.isNullOrBlank()) {
                    sendUiState { copy(inputState = InputState.Error, errorMessage = error) }
                }
                return@launcherOnIO
            }

            checkPasswordSecVersion { isSecVersion2, version ->
                val service = getService(LoginApi::class.java)
                val sendSmsCodeResult = service.phoneSetPassword(
                    password = if (isSecVersion2) password.toEncryptPassword() else password,
                    secVersion = version
                )
                dismissLoadingDialog()
                LoginPointReporter.setPassword(1, sendSmsCodeResult.isSuccess)
                if (sendSmsCodeResult.isSuccess) {
                    passwordResult.postValue(true)
                } else {
                    when {
                        sendSmsCodeResult is HttpResult.ApiError -> {
                            T.ss(sendSmsCodeResult.message)
                        }

                        else -> {
                            T.ss(sendSmsCodeResult.exceptionOrNull()?.message)
                        }
                    }
                }
            }
        }
    }
}

data class SetPasswordUiState(
    val password: String = "",
    val inputState: InputState = InputState.None,
    val errorMessage: String = ""
) : IUiState

sealed class SetPasswordUiIntent : IUiIntent {
    data object Save : SetPasswordUiIntent()
    data class InputPassword(val password: String) : SetPasswordUiIntent()
    data class InputStateChanged(val state: InputState) : SetPasswordUiIntent()

}