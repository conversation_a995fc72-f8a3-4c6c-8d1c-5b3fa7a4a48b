package com.twl.meeboss.login.viewmodel

import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.http.isRobotCheckCodeFail
import com.twl.meeboss.base.manager.ABUpdateManager
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.HttpResult
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.login.R
import com.twl.meeboss.login.api.LoginApi
import com.twl.meeboss.login.util.LoginPointReporter
import com.twl.meeboss.login.util.getRegisterIdentityParam
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class EmailVerifyCodeLoginViewModel @Inject constructor(
) : VerifyCodeLoginBaseViewModel() {
    var email: String = ""

    fun showEmail(): String {
        return email
    }

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is EmailVerifyCodeLoginUiIntent.Save ->{

            }
            else->{
                super.handleIntent(intent)
            }

        }
    }

    override fun getVerificationCode() {
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val sendSmsCodeResult = service.sendEmailCode(
                email,
                if (isRegister) "20" else "21",
                robotCheckToken
            )
            if (sendSmsCodeResult.isSuccess) {
                T.ss(R.string.setting_verification_code_sent_successfully)
                countDown.start()
            } else {
                when {
                    sendSmsCodeResult is HttpResult.ApiError -> {
                        T.ss(sendSmsCodeResult.message)
                        if(sendSmsCodeResult.isRobotCheckCodeFail()){
                            robotCheckToken = ""
                            reShowRobotCheckForGetCode.postValue(true)
                        }
                    }

                    else -> {
                        T.ss(R.string.setting_send_code_fail)
                    }
                }
            }
        }
    }

    override fun register() {
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val registerByPhoneResult = service.registerByEmail(
                email = email,
                verifyCode = uiStateFlow.value.code,
                cloudflareToken = robotCheckToken,
                identity = getRegisterIdentityParam()
            )
            if (registerByPhoneResult.isSuccess) {
                registerByPhoneResult.getOrNull()?.run {
                    AccountManager.activeAccount(this)
                    ABUpdateManager.update().let {
                        registerResult.postValue(this)
                        if(register){
                            LoginPointReporter.registerSuccess(5)
                        } else {
                            LoginPointReporter.loginSuccess(3)
                        }
                    }
                }
            } else {
                when {
                    registerByPhoneResult is HttpResult.ApiError -> {
                        setError(registerByPhoneResult.message)
                    }
                }
            }
        }
    }

    override fun loginByCode() {
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val loginByPhoneCodeResult = service.loginByEmail(
                email = email,
                verifyCode = uiStateFlow.value.code,
                cloudflareToken = robotCheckToken,
                identity = getRegisterIdentityParam(),
            )
            handleLoginResult(loginByPhoneCodeResult,false)
        }
    }

    override fun isEmail(): Boolean = true

}

sealed class EmailVerifyCodeLoginUiIntent : IUiIntent {
    data object Save : EmailVerifyCodeLoginUiIntent()
}