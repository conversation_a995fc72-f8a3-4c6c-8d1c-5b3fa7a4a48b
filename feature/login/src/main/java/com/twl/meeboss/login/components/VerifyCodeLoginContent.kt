package com.twl.meeboss.login.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.components.LoginCodeTextField
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.throttleClick
import com.twl.meeboss.login.R
import com.twl.meeboss.login.viewmodel.VerifyCodeLoginBaseUiState

@Composable
fun VerifyCodeLoginContent(
    uiState: VerifyCodeLoginBaseUiState = VerifyCodeLoginBaseUiState(),
    titleStr:String = "",
    sendCodeToStr:String = "", //手机号或者邮箱, 拼在文案上
    canContinueWithThird: Boolean = true,
    onValueChanged: (String) -> Unit = {},
    onResendClick: () -> Unit = {},
    onGoogleLogin: () -> Unit = {},
    onClickNext: () -> Unit = {},
) {
    Column(modifier = Modifier
        .fillMaxSize()
        .background(Color.White)) {
        XTitleBar()
        Spacer(modifier = Modifier.height(12.dp))
        Text(modifier = Modifier.padding(16.dp, 0.dp),
            text = titleStr,
            fontSize = 28.sp, fontWeight = FontWeight.SemiBold)
        Spacer(modifier = Modifier.height(6.dp))
        Text(modifier = Modifier.padding(16.dp, 0.dp),
            text = stringResource(id = R.string.setting_enter_the_verification_code_sent_to,sendCodeToStr),
            fontSize = 14.sp, fontWeight = FontWeight.W400)

        Spacer(modifier = Modifier.height(20.dp))
        // 验证码输入框
        LoginCodeTextField(code = uiState.code, modifier = Modifier.padding(6.dp, 0.dp),
            isError = uiState.isError,
            errorMessage = uiState.errorMessage,
            onValueChanged = {
                onValueChanged(it)
            }
        )
        Spacer(modifier = Modifier.height(20.dp))
        Row {
            Text(text = stringResource(id = R.string.login_haven_not_receive_a_code),
                modifier = Modifier.padding(16.dp, 0.dp, 0.dp, 0.dp),
                color = Black888888,
                fontSize = 14.sp, fontWeight = FontWeight.Medium)
            val count = uiState.countDown
            if (count == 0) {
                Text(text = stringResource(id = R.string.common_send_again),
                    modifier = Modifier
                        .padding(6.dp, 0.dp)
                        .noRippleClickable {
                            onResendClick()
                        },
                    textDecoration = TextDecoration.Underline,
                    color = COLOR_222222,
                    fontSize = 14.sp, fontWeight = FontWeight.Medium)
            } else {
                Text(text = "${stringResource(id = R.string.common_send_again)} (${count}s)",
                    modifier = Modifier.padding(6.dp, 0.dp),
                    color = Black888888,
                    fontSize = 14.sp, fontWeight = FontWeight.Medium)
            }
        }
        Spacer(modifier = Modifier.height(48.dp))

        //登录按钮
        XCommonButton(
            text = stringResource(id = R.string.common_next),
            enabled = uiState.code.length == 4,
            modifier = Modifier.padding(16.dp, 0.dp),
            onClick = onClickNext
        )

        if(canContinueWithThird){
            Spacer(modifier = Modifier.height(28.dp))
            Box(modifier = Modifier
                .padding(16.dp, 0.dp)
                .fillMaxWidth()
                .height(48.dp), contentAlignment = Alignment.CenterStart) {
                Button(
                    modifier = Modifier
                        .fillMaxSize(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.White,
                        contentColor = Black222222
                    ),
                    border = BorderStroke(1.dp, BlackEBEBEB),
                    shape = RoundedCornerShape(24.dp),
                    onClick = onGoogleLogin.throttleClick()
                ) {
                    Text(text = stringResource(id = R.string.login_with_google),
                        fontWeight = FontWeight.Medium, fontSize = 16.sp, color = Black222222
                    )
                }
                Image(modifier = Modifier
                    .padding(12.dp, 0.dp, 0.dp, 0.dp)
                    .size(20.dp), painter = painterResource(id = R.mipmap.login_ic_google), contentDescription = stringResource(id = R.string.login_with_google)
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewPhoneVerifyCodeLoginContent() {
    VerifyCodeLoginContent(
        titleStr = stringResource(id = R.string.setting_verify_your_phone_number),
        sendCodeToStr = "9110001204"
    )
}

@Preview
@Composable
private fun PreviewEmailVerifyCodeLoginContent() {
    VerifyCodeLoginContent(
        titleStr = stringResource(id = R.string.setting_verify_your_email),
        sendCodeToStr = "<EMAIL>"
    )
}