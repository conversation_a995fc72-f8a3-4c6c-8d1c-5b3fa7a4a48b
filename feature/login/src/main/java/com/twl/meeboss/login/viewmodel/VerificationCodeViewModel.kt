package com.twl.meeboss.login.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.twl.meeboss.base.config.AreaCodeHelper
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.http.isRobotCheckCodeFail
import com.twl.meeboss.base.ktx.buildAreaCodeParam
import com.twl.meeboss.base.ktx.toFormatPhoneNumber
import com.twl.meeboss.base.mudule.ModuleManager
import com.twl.meeboss.common.utils.Countdown
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.HttpResult
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.login.R
import com.twl.meeboss.login.api.LoginApi
import com.twl.meeboss.login.export.LoginRouterPath
import com.twl.meeboss.login.util.LoginPointReporter
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class VerificationCodeViewModel @Inject constructor() :
    BaseMviViewModel<VerificationCodeUiState, VerificationCodeUiIntent>() {
    val checkResult = MutableLiveData<String?>()

    val verifyEmailResult = MutableLiveData<Boolean>() //邮箱认证结果

    val reShowRobotCheck: MutableLiveData<Boolean> = MutableLiveData()


    var account: String = ""

    var isPhone: Boolean = false

    var sceneType: Int =
        0 //使用场景 1-用户注册 2-忘记密码 3绑定邮箱 4 换绑邮箱  8-修改密码（邮箱填空）9-首善后的验证邮箱使用 （2024-07-23新增）

    var emailSuffixId: String? = null

    var robotCheckToken: String = ""

    private val countDown: Countdown = Countdown {
        viewModelScope.launch(Dispatchers.Main) {
            sendUiState { copy(countDown = it) }
        }
    }


    fun showAccount(): String {
        return if (isPhone) account.toFormatPhoneNumber() else account
    }


    override fun initUiState(): VerificationCodeUiState = VerificationCodeUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is VerificationCodeUiIntent.SendCode -> {
                getVerificationCode()
            }

            is VerificationCodeUiIntent.ChangeCode -> {
                sendUiState {
                    copy(
                        code = intent.code,
                    )
                }
            }

            is VerificationCodeUiIntent.Confirm -> {
                checkVerificationCode()
            }

            else -> {

            }

        }
    }


    private fun getVerificationCode() {
        if (isPhone) {
            getPhoneVerificationCode()
        } else {
            getEmailVerificationCode()
        }
    }


    private fun checkVerificationCode() {
        if (isPhone) {
            checkPhoneCode()
        } else {
            if (sceneType == LoginRouterPath.LOGIN_BOSS_COMPLETE_PROFILE_VERIFY_EMAIL) {
                LoginPointReporter.employerEmailSubmit()
                checkVerifyEmailCode()
            } else {
                checkEmailCode()
            }
        }
    }


    /**
     * 获取邮箱验证码
     */
    private fun getEmailVerificationCode() {
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val sendSmsCodeResult =
                service.sendEmailCode(account, sceneType.toString(), robotCheckToken)
            if (sendSmsCodeResult.isSuccess) {
                T.ss(R.string.setting_verification_code_sent_successfully)
                countDown.start()
            } else {
                when {
                    sendSmsCodeResult is HttpResult.ApiError -> {
                        T.ss(sendSmsCodeResult.message)
                        if (sendSmsCodeResult.isRobotCheckCodeFail()) {
                            robotCheckToken = ""
                            reShowRobotCheck.postValue(true)
                        }
                    }

                    else -> {
                        T.ss(R.string.setting_send_code_fail)
                    }
                }
            }
        }

    }

    /**
     * 检查邮箱验证码
     */
    private fun checkEmailCode() {
        showLoadingDialog(false)
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val registerByPhoneResult = service.verifyEmailCode(account, uiStateFlow.value.code)
            dismissLoadingDialog()
            if (registerByPhoneResult.isSuccess) {
                registerByPhoneResult.getOrNull()?.run {
                    if (!this.uniqStr.isNullOrBlank()) {
                        checkResult.postValue(this.uniqStr)
                    }

                }
            } else {
                when {
                    registerByPhoneResult is HttpResult.ApiError -> {
                        T.ss(registerByPhoneResult.message)
                    }

                }
            }
        }
    }

    /**
     * 检查邮箱验证码
     */
    private fun checkVerifyEmailCode() {
        showLoadingDialog(false)
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val registerByPhoneResult =
                service.bossRegisterVerifyEmailCode(account, uiStateFlow.value.code)
            dismissLoadingDialog()
            if (registerByPhoneResult.isSuccess) {
                //邮箱验证完成后先重新拉取boss信息保证F1状态回显正常
                ModuleManager.updateUserInfo()
                verifyEmailResult.postValue(true)
            } else {
                when {
                    registerByPhoneResult is HttpResult.ApiError -> {
                        T.ss(registerByPhoneResult.message)
                    }

                }
            }
        }
    }


    /**
     * 获取手机验证码
     */
    private fun getPhoneVerificationCode() {
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val areaCode = AreaCodeHelper.getAreaCode()
            val sendSmsCodeResult = service.sendSmsCode(
                account, "2", robotCheckToken,
                buildAreaCodeParam(areaCode.countryCode, areaCode.smsPrefix)
            )
            if (sendSmsCodeResult.isSuccess) {
                T.ss(R.string.setting_verification_code_sent_successfully)
                countDown.start()
            } else {
                when {
                    sendSmsCodeResult is HttpResult.ApiError -> {
                        T.ss(sendSmsCodeResult.message)
                        if (sendSmsCodeResult.isRobotCheckCodeFail()) {
                            robotCheckToken = ""
                            reShowRobotCheck.postValue(true)
                        }
                    }

                    else -> {
                        T.ss(R.string.setting_send_code_fail)
                    }
                }
            }
        }

    }

    /**
     * 检查手机验证码
     */
    private fun checkPhoneCode() {
        showLoadingDialog(false)
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val registerByPhoneResult = service.verifyPhoneCode(account,
                uiStateFlow.value.code,AreaCodeHelper.getAreaCodeAsParams())
            dismissLoadingDialog()
            if (registerByPhoneResult.isSuccess) {
                registerByPhoneResult.getOrNull()?.run {
                    if (!this.uniqStr.isNullOrBlank()) {
                        checkResult.postValue(this.uniqStr)
                    }

                }
            } else {
                when {
                    registerByPhoneResult is HttpResult.ApiError -> {
                        T.ss(registerByPhoneResult.message)
                    }

                }
            }
        }
    }


    override fun onCleared() {
        super.onCleared()
        countDown.cancel()
    }


}


data class VerificationCodeUiState(
    val code: String = "",
    val countDown: Int = 0
) : IUiState


sealed class VerificationCodeUiIntent : IUiIntent {
    data class ChangeCode(val code: String) : VerificationCodeUiIntent()
    data object SendCode : VerificationCodeUiIntent()
    data object Confirm : VerificationCodeUiIntent()
}

