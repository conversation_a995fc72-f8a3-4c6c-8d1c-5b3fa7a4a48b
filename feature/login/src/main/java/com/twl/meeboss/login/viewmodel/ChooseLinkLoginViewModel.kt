package com.twl.meeboss.login.viewmodel

import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.config.AreaCodeHelper
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.http.isRobotCheckCodeFail
import com.twl.meeboss.base.manager.ABUpdateManager
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.HttpResult
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.login.R
import com.twl.meeboss.login.api.LoginApi
import com.twl.meeboss.login.util.LoginPointReporter
import com.twl.meeboss.login.util.getRegisterIdentityParam
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ChooseLinkLoginViewModel @Inject constructor() :
    BaseMviViewModel<ChooseLinkLoginUiState, ChooseLinkLoginUiIntent>() {

    var phone = ""
    var robotCheckToken = ""
    override fun initUiState(): ChooseLinkLoginUiState = ChooseLinkLoginUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is ChooseLinkLoginUiIntent.LinkGoogle -> {
                linkGoogle(intent.token)
            }

            is ChooseLinkLoginUiIntent.NavigationHandled -> {
                sendUiState {
                    copy(navigation = null)
                }
            }
        }
    }


    private fun linkGoogle(token: String) {
        showLoadingDialog()
        launcherOnIO {
            val service = getService(LoginApi::class.java)
            val loginResult = service.bindByGoogle(
                googleAuthToken = token,
            )

            if (loginResult.isSuccess) {
                loginResult.getOrNull()?.run {
                    AccountManager.activeCurrentAccount()
                    ABUpdateManager.update().let {
                        dismissLoadingDialog()
                        sendUiState {
                            copy(navigation = ChooseLinkLoginNavigation.Login)
                        }
                    }

                } ?: apply {
                    dismissLoadingDialog()
                }

            } else {
                dismissLoadingDialog()
                when {
                    loginResult is HttpResult.ApiError -> {
                        T.ss(loginResult.message)
                        if (loginResult.isRobotCheckCodeFail()) {
                            robotCheckToken = ""
                            sendUiState { copy(navigation = ChooseLinkLoginNavigation.SendCode) }
                        }
                    }

                    else -> {
                        T.ss(loginResult.exceptionOrNull()?.message)
                    }
                }
            }
        }
    }
}


data class ChooseLinkLoginUiState(
    val navigation: ChooseLinkLoginNavigation? = null
) : IUiState

sealed class ChooseLinkLoginUiIntent : IUiIntent {
    data class LinkGoogle(val token: String) : ChooseLinkLoginUiIntent()
    data object NavigationHandled : ChooseLinkLoginUiIntent()
}

sealed class ChooseLinkLoginNavigation {
    data object SendCode : ChooseLinkLoginNavigation()
    data object Login : ChooseLinkLoginNavigation()
}
