package com.twl.meeboss.login.bottomsheet

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.core.ui.R
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.login.components.GoogleLoginButton

class GoogleEmailLoginBottomSheet : CommonBottomDialogFragment() {
    private var onGoogleLogin: (() -> Unit)? = null
    private var onVerifyCodeLogin: (() -> Unit)? = null
    companion object {
        fun newInstance(
            onGoogleLogin: () -> Unit = {},
            onVerifyCodeLogin: () -> Unit = {},
        ) = GoogleEmailLoginBottomSheet().apply {
            this.onGoogleLogin = onGoogleLogin
            this.onVerifyCodeLogin = onVerifyCodeLogin
        }
    }
    @Composable
    override fun DialogContent() {
        GoogleEmailLoginContent(
            onCloseClick = {
                dismissSafely()
            },
            onGoogleLogin = {
                dismissSafely()
                onGoogleLogin?.invoke()
            },
            onVerifyCodeLogin = {
                dismissSafely()
                onVerifyCodeLogin?.invoke()
            }
        )
    }
}

@Composable
fun GoogleEmailLoginContent(
    onCloseClick: () -> Unit = {},
    onGoogleLogin: () -> Unit = {},
    onVerifyCodeLogin: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .background(Color.White)
            .padding(16.dp, 20.dp)
    ) {

        Image(
            painter = painterResource(id = R.drawable.ui_dailog_close),
            modifier = Modifier
                .size(24.dp)
                .align(Alignment.Start)
                .noRippleClickable(onClick = onCloseClick),
            contentDescription = null
        )

        Spacer(modifier = Modifier.height(12.dp))

        Text(
            text = stringResource(id = R.string.thrid_sheet_title_google),
            style = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight.W600,
                color = COLOR_222222,
            )
        )

        Spacer(modifier = Modifier.height(20.dp))

        GoogleLoginButton(onGoogleLogin = onGoogleLogin)

        Spacer(modifier = Modifier.height(12.dp))

        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(0.dp, 13.dp)
                .noRippleClickable(onClick = onVerifyCodeLogin),
            text = stringResource(id = R.string.login_sign_in_by_verification_code),
            textAlign = TextAlign.Center,

            // WEB - 1.5 line height/16 - Medium
            style = TextStyle(
                fontSize = 16.sp,
                lineHeight = 24.sp,
                fontWeight = FontWeight.W500,
                color = COLOR_222222,
            )
        )


    }
}

@Preview
@Composable
private fun PreviewGoogleEmailLoginContent() {
    GoogleEmailLoginContent()
}