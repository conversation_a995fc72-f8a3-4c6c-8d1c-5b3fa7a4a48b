package com.twl.meeboss.chat.module.setting.report

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.constants.BUNDLE_INT
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.export.ChatPageRouter
import com.twl.meeboss.chat.export.ChatRouterPath
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.AndroidEntryPoint

/**
 * @author: 冯智健
 * @date: 2024年08月18日 10:43
 * @description:
 */
@AndroidEntryPoint
@RouterPage(path = [ChatRouterPath.CHAT_REPORT_PAGE])
class ChatReportActivity: BaseMviActivity<ChatReportViewModel>() {
    override val viewModel: ChatReportViewModel by viewModels()
    private val mScene by lazy {
        intent.getIntExtra(BUNDLE_INT, 0)
    }

    override fun preInit(intent: Intent) {

    }

    override fun initData() {

    }

    @Composable
    override fun ComposeContent() {
        val vm: ChatReportViewModel by viewModels()
        val uiState by vm.uiStateFlow.collectAsStateWithLifecycle()
        val securityId: MutableState<String?> = rememberSaveable {
            mutableStateOf(intent.getStringExtra(ChatPageRouter.BUNDLE_SECURITY_ID))
        }
        LaunchedEffect(uiState.submitFinish) {
            if (uiState.submitFinish) {
                T.ss(R.string.chat_report_submitted.toResourceString())
                finish()
            }
        }
        ChatReportPage(uiState.reasonTypeResult) { type, content ->
            securityId.value?.let {
                vm.sendUiIntent(ChatReportUiIntent.SubmitReport(type, content, it, mScene))
            }
        }
    }
}