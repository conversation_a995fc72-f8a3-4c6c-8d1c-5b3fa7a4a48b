package com.twl.meeboss.chat.module.common.conversation

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.glide.GlideImage
import com.twl.meeboss.chat.R
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.theme.COLOR_FF4D57

/**
 * @author: musa on 2025/05/07
 * @e-mail: <EMAIL>
 * @desc: 推荐高度匹配的候选人
 */
@Composable
fun ConversationTopMatches(
    modifier: Modifier = Modifier,
    title: String,
    subTitle: String,
    avatars: List<String>,
    showRedDot: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = modifier
            .clickable { onClick() }
    ) {
        Image(
            painter = painterResource(id = R.drawable.chat_bg_highly_matched),
            contentDescription = "Background Image",
            modifier = Modifier
                .matchParentSize()
                .clip(RoundedCornerShape(12.dp)),
            contentScale = ContentScale.Crop
        )

        Column(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 14.dp)
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Row(Modifier.weight(1f)) {
                    Image(
                        painter = painterResource(id = R.drawable.chat_icon_top_matches),
                        contentDescription = "Top matches icon",
                        modifier = Modifier.size(20.dp)
                            .align(Alignment.CenterVertically),
                    )
                    Text(
                        modifier = Modifier.padding(start = 4.dp)
                            .align(Alignment.CenterVertically),
                        text = title,
                        color = COLOR_222222,
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 16.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    if (showRedDot) {
                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .background(COLOR_FF4D57, CircleShape)
                                .align(Alignment.Top)
                        )
                    }
                }
                StackAvatar(Modifier.padding(start = 10.dp), contentAlignment = Alignment.CenterStart, avatars = avatars)

            }
            Row(Modifier.padding(top = 4.dp), verticalAlignment = Alignment.CenterVertically) {
                Text(
                    modifier = Modifier.weight(1f),
                    text = subTitle,
                    color = COLOR_484848,
                    fontSize = 14.sp,
                    lineHeight = 18.sp
                )

                Image(
                    painter = painterResource(id = R.drawable.chat_icon_left_arrow),
                    contentDescription = "arrow",
                    modifier = Modifier.size(16.dp),
                    colorFilter = ColorFilter.tint(Color(0xFF757575)) // 箭头的中灰色着色
                )
            }
        }
    }
}

@Composable
private fun StackAvatar(modifier: Modifier = Modifier, contentAlignment: Alignment, maxAvatarNum: Int = 3, avatarSize: Dp = 24.dp, overlap: Dp = 5.dp, avatars: List<String>) {

    Box (modifier, contentAlignment = contentAlignment) {
        // 最多显示3个头像，从右（顶部）到左（底部）堆叠
        avatars.take(maxAvatarNum).forEachIndexed { index, avatarUrl ->
            GlideImage(
                modifier = Modifier
                    .padding(start = (avatarSize - overlap) * index)
                    .clip(RoundedCornerShape(avatarSize))
                    .size(avatarSize)
                    .background(Color.White)
                    .padding(2.dp)
                    .clip(RoundedCornerShape(avatarSize)),
                imageModel = { avatarUrl },
                imageOptions = ImageOptions(
                    contentScale = ContentScale.Crop,
                    alignment = Alignment.Center
                ),
                previewPlaceholder = ColorPainter(COLOR_FF4D57),
            )
        }
    }
}

@Preview
@Composable
fun PreviewConversationTopMatches() {
    ConversationTopMatches(Modifier.fillMaxWidth(),title = "Top matches long long long long long long", subTitle = "Discover top matches, without endless long long long.", avatars = listOf("", "", ""), showRedDot = true, onClick = {})
}