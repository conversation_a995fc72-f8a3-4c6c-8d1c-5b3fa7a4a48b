package com.twl.meeboss.chat.core.model.message.bean

import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.base.model.common.CommonTypeBean

class ChatJobBean(val securityId: String? = "",
                  val jobTitle: String? = "",
                  val companyName: String? = "",
                  val jobTypes: List<CommonTypeBean>? = null,
                  val companySize: CommonTypeBean? = null,
                  val companyIndustry: String = "",
                  val locationType: CommonTypeBean? = null,
                  val jobLocations: List<CommonTypeBean>? = listOf(),
                  val salaryDesc: String? = "",
                  val status:Int = 0  //0未感兴趣，1感兴趣
):BaseEntity {
}