package com.twl.meeboss.chat.core.model.convert

import cn.techwolf.international.washington.chat.protocol.WashingtonProtocol
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.chat.export.constant.GeneralMsgType
import com.twl.meeboss.chat.export.constant.MessageStatus
import com.twl.meeboss.chat.core.constant.PBMessageType
import com.twl.meeboss.chat.core.facade.FacadeManager
import com.twl.meeboss.chat.core.model.message.MessageRecord
import com.twl.meeboss.chat.core.model.message.PictureMessage
import com.twl.meeboss.chat.core.model.message.TextMessage
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.core.network.HttpParamsProvider
import com.twl.meeboss.core.network.interceptor.CommonParamsInterceptor

class Message2PBConvert {
    companion object {
        private const val TAG = "Message2PBConvert"

        /**
         * 上线
         */
        fun createOnLinePresence(presenceMsgType: Int): ByteArray {
            val maxReceivedId: Long = FacadeManager.getInstance().messagePatchFacade.getMaxMessageId()
            val maxConfirmedId: Long = FacadeManager.getInstance().messagePatchFacade.getMaxConfirmId()
            TLog.info(TAG, "createOnLinePresence() called with: maxReceivedId = [$maxReceivedId], maxConfirmedId = [$maxConfirmedId]")
            val messageBuilder = WashingtonProtocol.ChatProtocol.newBuilder()
            messageBuilder.setMessageType(PBMessageType.PRESENCE)
            val builder = WashingtonProtocol.PresenceMessage.newBuilder()
            builder.setType(presenceMsgType) //1上线 , 2 下线
            builder.setLastReceivedMid(maxReceivedId)
            builder.setLastConfirmedMid(maxConfirmedId)
            builder.setClientInfo(HttpParamsProvider.getClientInfo())
            messageBuilder.setPresenceMessage(builder.build())
            return messageBuilder.build().toByteArray()
        }


        fun messageToByte(messageRecord: MessageRecord): ByteArray {
            val builder: WashingtonProtocol.ChatProtocol.Builder = WashingtonProtocol.ChatProtocol.newBuilder()
            val generalBuilder: WashingtonProtocol.GeneralMessage.Builder = WashingtonProtocol.GeneralMessage.newBuilder()
            generalBuilder.setCmid(messageRecord.cmid)
            generalBuilder.setFromId(messageRecord.fromId)
            generalBuilder.setFromIdentity(messageRecord.fromIdentity)
            generalBuilder.setToId(messageRecord.toId)
            generalBuilder.setToIdentity(messageRecord.toIdentity)
            generalBuilder.setAddTime(messageRecord.addTime)
            generalBuilder.setStatus(MessageStatus.SENDING)
            val bodyBuilder = WashingtonProtocol.MessageBody.newBuilder()
            when (messageRecord.bodyType) {
                GeneralMsgType.MSG_TEXT -> {
                    val textBuilder = WashingtonProtocol.Text.newBuilder()
                    if(messageRecord is TextMessage){
                        messageRecord.textBody?.apply {
                            textBuilder.setContent(content)
                            textBuilder.setType(type)
                            textBuilder.setUrl(url.notNull())
                        }
                    }else{
                        textBuilder.setContent(messageRecord.content)
                    }
                    bodyBuilder.setText(textBuilder.build())
                }
                GeneralMsgType.MSG_PIC -> {
                    if(messageRecord is PictureMessage){
                        val messageBody = messageRecord.pictureBody
                        val imageBuilder = WashingtonProtocol.Image.newBuilder()
                        val thumbBuilder = WashingtonProtocol.ImageInfo.newBuilder()
                        val originBuilder = WashingtonProtocol.ImageInfo.newBuilder()
                        thumbBuilder.setUrl(messageBody?.tinyUrl.notNull())
                        thumbBuilder.setWidth(messageBody?.tinyWith ?: 0)
                        thumbBuilder.setHeight(messageBody?.tinyHeight ?: 0)
                        imageBuilder.setThumb(thumbBuilder)
                        originBuilder.setUrl(messageBody?.url?.notNull())
                        originBuilder.setWidth(messageBody?.width ?: 0)
                        originBuilder.setHeight(messageBody?.height ?: 0)
                        imageBuilder.setOrigin(originBuilder)
                        bodyBuilder.setImage(imageBuilder)
                    }
                }

            }
            generalBuilder.setBodyType(messageRecord.bodyType)
            generalBuilder.setBody(bodyBuilder.build())
            builder.addGeneralMessages(generalBuilder.build())
            builder.setMessageType(PBMessageType.GENERAL)
            return builder.build().toByteArray()
        }

        fun readMessageToByte(friendMaxMessageId:Long,friendId:String): ByteArray{
            val builder: WashingtonProtocol.ChatProtocol.Builder = WashingtonProtocol.ChatProtocol.newBuilder()
            val readBuilder: WashingtonProtocol.ReadMessage.Builder = WashingtonProtocol.ReadMessage.newBuilder()
            readBuilder.setUserId(friendId)
            readBuilder.setMid(friendMaxMessageId)
            readBuilder.setIdentity(if(UserProvider.isGeek()) UserConstants.BOSS_IDENTITY else UserConstants.GEEK_IDENTITY)
            builder.setReadMessage(readBuilder.build())
            builder.setMessageType(PBMessageType.READ)
            return builder.build().toByteArray()
        }
    }


}