package com.twl.meeboss.chat.core.model.message.custom

import cn.techwolf.international.washington.chat.protocol.WashingtonProtocol
import com.twl.meeboss.base.ktx.buildFullName
import com.twl.meeboss.base.ktx.report
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.bean.ChatResumeBean
import com.twl.meeboss.chat.export.constant.LocalMessageType
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.core.ui.utils.toResourceString

/**
 * 破冰个人简历消息
 */
class ChatGreetingResumeMessage : ChatMessage() {
     var resumeBean: ChatResumeBean? = null

    override fun parseMessage(pbMessage: WashingtonProtocol.GeneralMessage) {
        try {
            resumeBean = GsonUtils.fromJson(pbMessage.body?.custom?.content, ChatResumeBean::class.java)
        }catch (e:Exception){
            e.report("parse_greeting_resume_exception")
        }
    }

    override fun parseFromDatabase() {
        resumeBean = GsonUtils.fromJson(content, ChatResumeBean::class.java)

    }

    override fun prepareForDatabase() {
        content = GsonUtils.toJson(resumeBean)
    }

    override fun getSummary(): String {
         return if(localMessageType == LocalMessageType.GEEK_GREETING_RESUME_IN_BOSS){
            "${buildFullName(firstName = resumeBean?.firstName.notNull(), lastName = resumeBean?.lastName.notNull())}${R.string.chat_applied_your_job.toResourceString()}"
        }else{
             R.string.chat_card_active_geek_card.toResourceString()
        }
    }
}