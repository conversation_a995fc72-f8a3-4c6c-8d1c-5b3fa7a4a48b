package com.twl.meeboss.chat.core.constant

import androidx.annotation.IntDef

@Target(AnnotationTarget.TYPE, AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.SOURCE)
@IntDef(flag = true, value = [
    CustomBodyType.GEEK_GREETING_JOB_IN_GEEK,
    CustomBodyType.GEEK_GREETING_RESUME_IN_BOSS,
    CustomBodyType.BOSS_GREETING_RESUME_IN_BOSS,
    CustomBodyType.BOSS_GREETING_JOB_IN_GEEK,
    CustomBodyType.INTEREST_GRAY_HINT,
    CustomBodyType.EXCHANGE_EMAIL_RESULT,
    CustomBodyType.EXCHANGE_PHONE_RESULT,
    CustomBodyType.EXCHANGE_RESUME_RESULT,
    CustomBodyType.EXCHANGE_REQUEST_EMAIL,
    CustomBodyType.EXCHANGE_REQUEST_PHONE,
    CustomBodyType.EXCHANGE_REQUEST_RESUME,
    CustomBodyType.EXCHANGE_SEND_RESUME_REQUEST,
    CustomBodyType.FIRST_MESSAGE_POST_JOB,
    CustomBodyType.FIRST_MESSAGE_BROWSE_CANDIDATES,
    CustomBodyType.FIRST_MESSAGE_BROWSE_JOBS,
])
annotation class CustomBodyType {
    companion object {
        const val GEEK_GREETING_JOB_IN_GEEK = 1
        const val BOSS_GREETING_JOB_IN_GEEK = 2
        const val BOSS_GREETING_RESUME_IN_BOSS = 3
        const val GEEK_GREETING_RESUME_IN_BOSS = 4
        const val INTEREST_GRAY_HINT = 5        //感兴趣灰条
        const val EXCHANGE_EMAIL_RESULT = 8    //交换邮件结果
        const val EXCHANGE_PHONE_RESULT = 9    //交换电话结果
        const val EXCHANGE_RESUME_RESULT = 10   //交换简历结果
        const val EXCHANGE_REQUEST_EMAIL = 11   //请求交换邮箱
        const val EXCHANGE_REQUEST_PHONE = 12   //请求交换电话
        const val EXCHANGE_REQUEST_RESUME = 13   //请求交换简历
        const val EXCHANGE_SEND_RESUME_REQUEST = 14  //请求发送简历
        const val FIRST_MESSAGE_POST_JOB = 15  //Mee Boss first message : post job
        const val FIRST_MESSAGE_BROWSE_CANDIDATES = 16  //Mee Boss first message : browse candidates
        const val FIRST_MESSAGE_BROWSE_JOBS = 17  //Mee Boss first message : browse jobs

    }
}