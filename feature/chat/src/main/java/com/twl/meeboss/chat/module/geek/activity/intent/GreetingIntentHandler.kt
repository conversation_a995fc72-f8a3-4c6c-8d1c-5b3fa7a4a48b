package com.twl.meeboss.chat.module.geek.activity.intent

import androidx.lifecycle.viewModelScope
import com.twl.meeboss.chat.module.geek.viewmodel.GeekChatViewModel
import com.twl.meeboss.chat.repos.ChatGeekRepository
import com.twl.meeboss.common.utils.T
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class GreetingIntentHandler(private val viewModel: GeekChatViewModel,
                            private val repository: ChatGeekRepository) {

    fun handleMessageIntent(intent: GeekChatUiIntent.Greeting) {
        when (intent) {
            is GeekChatUiIntent.Greeting.InteractInterest -> {
                interactInterest(intent.mid)
            }
        }
    }

    private fun interactInterest(mid: Long) {
        viewModel.viewModelScope.launch (Dispatchers.IO){
            repository.geekInteractInterest(mid).onSuccess {
                viewModel.showGreetingResult.postValue(it.replySettings)
            }.onFailure {
                T.ss(it.message)
            }

        }
    }

}