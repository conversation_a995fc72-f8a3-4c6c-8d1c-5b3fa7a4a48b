package com.twl.meeboss.chat.module.boss.bottomsheet.strategy

import androidx.fragment.app.FragmentActivity
import com.twl.meeboss.base.components.dialog.chain.BaseDialogChainStrategy
import com.twl.meeboss.base.components.dialog.chain.DialogChainManager
import com.twl.meeboss.chat.export.model.BossChatParams
import com.twl.meeboss.chat.module.boss.bottomsheet.BossSendGreetingBottomSheet
import com.twl.meeboss.chat.module.boss.viewmodel.BossChatViewModel
import com.twl.meeboss.chat.utils.ChatPointReporter
import com.twl.meeboss.core.ui.utils.showSafely

/**
 * Geek设置问候语管理弹框
 */
class BossSendGreetingBottomSheetStrategy(activity: FragmentActivity, private val params: BossChatParams, val chatViewModel: BossChatViewModel) : BaseDialogChainStrategy(activity) {

    override fun condition(): <PERSON><PERSON><PERSON> {
        return params.greetingSettings
    }


    override fun showDialog(manager: <PERSON>alog<PERSON>hai<PERSON>Manager) {
        val jobId = chatViewModel.conversation?.jobId ?: ""
        val dialog = BossSendGreetingBottomSheet.newInstance(chatViewModel.mFriendId, 1, jobId){
            manager.process()
        }
        dialog.showSafely(context)
        ChatPointReporter.greetingGuideShow(1)
    }
}