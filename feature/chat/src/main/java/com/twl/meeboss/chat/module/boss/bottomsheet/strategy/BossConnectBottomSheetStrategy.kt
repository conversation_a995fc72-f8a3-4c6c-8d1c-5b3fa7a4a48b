package com.twl.meeboss.chat.module.boss.bottomsheet.strategy

import androidx.fragment.app.FragmentActivity
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.components.dialog.chain.BaseDialogChainStrategy
import com.twl.meeboss.base.components.dialog.chain.DialogChainManager
import com.twl.meeboss.chat.module.boss.bottomsheet.BossChatConnectedBottomSheet
import com.twl.meeboss.chat.module.boss.viewmodel.BossChatViewModel
import com.twl.meeboss.chat.utils.ChatPointReporter
import com.twl.meeboss.common.preference.SpKey
import com.twl.meeboss.common.preference.SpManager
import com.twl.meeboss.core.ui.utils.showSafely

/**
 * Geek首次和人双聊弹框
 */
class BossConnectBottomSheetStrategy(activity: FragmentActivity,val viewModel: BossChatViewModel) : BaseDialogChainStrategy(activity) {

    override fun condition(): Boolean {
        //是否展示目前存在本地
        val key = getKey()
        return SpManager.getUserBoolean(key, true).also {
            if (it) {
                SpManager.putUserBoolean(key, false)
            }
        }
    }

    private fun getKey():String{
        return SpKey.KEY_CHAT_SHOW_CONNECTED_DIALOG + isActive().toString()
    }

    private fun isActive():Boolean{
        return viewModel.conversation?.friendSource != 2
    }


    override fun showDialog(manager: DialogChainManager) {
        val dialog = BossChatConnectedBottomSheet.newInstance(isActive()) {
            TLog.info("DialogChainManager", "${this.javaClass.simpleName}showDialog setOnDismissListener")
            manager.process()
        }
        dialog.showSafely(context)
    }
}