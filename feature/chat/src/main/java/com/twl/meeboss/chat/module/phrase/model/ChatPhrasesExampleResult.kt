package com.twl.meeboss.chat.module.phrase.model

import com.twl.meeboss.base.model.BaseEntity

data class ChatPhrasesExampleResult(
    val scenes: List<ChatPhrasesScenesItem> = listOf()
):BaseEntity

data class ChatPhrasesScenesItem(
    val code: Long = 0,
    val name: String = "",
    val phrases: List<ChatPhrasesItem> = listOf()
):BaseEntity
data class ChatPhrasesItem(
    val templateId:Long = 0,
    val text:String = "",
):BaseEntity