package com.twl.meeboss.chat.module.phrase.viewmodel

import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.module.phrase.model.ChatPhrasesItemResult
import com.twl.meeboss.chat.repos.ChatRepository
import com.twl.meeboss.core.network.handleDefaultError
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class ChatPhrasesManageViewModel @Inject constructor(
    private val repos: ChatRepository
) : BaseMviViewModel<ChatPhrasesManageUiState, ChatPhrasesManageUiIntent>() {

    override fun initUiState(): ChatPhrasesManageUiState = ChatPhrasesManageUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is ChatPhrasesManageUiIntent.Init ->{
                getCommonPhrases()
            }
            is ChatPhrasesManageUiIntent.DeleteItem ->{
                deletePhraseItem(intent.index)
            }
            else->{

            }

        }
    }

    private fun getCommonPhrases() {
        sendLoadUiState(LoadState.Loading)
        requestData(
            enableLoadState = false,
            request = {
                repos.getCommonPhrases()
            },
            success = {
                sendLoadUiState(LoadState.Success)
                sendUiState {
                    copy(
                        list = it?.list?: listOf()
                    )
                }
            },
            fail = {
                sendLoadUiState(LoadState.Fail(content = it.message, buttonText = R.string.common_error_try_again.toResourceString()))
                it.handleDefaultError()
            }
        )
    }

    private fun deletePhraseItem(index: Int){
        val currentList = uiStateFlow.value.list.toMutableList()
        if (index !in 0 until currentList.size) {
            TLog.error(TAG,"deletePhraseItem error index:${index},size:${currentList.size}")
            return
        }
        showLoadingDialog()
        requestData(
            enableLoadState = false,
            request = {
                repos.deleteCommonPhrases(currentList[index].id)
            },
            success = {
                dismissLoadingDialog()
                currentList.removeAt(index)
                sendUiState {
                    copy(
                        list = currentList
                    )
                }
            },
            fail = {
                dismissLoadingDialog()
                it.handleDefaultError()
            }
        )
    }

}

data class ChatPhrasesManageUiState(
    val list: List<ChatPhrasesItemResult> = listOf(),
) : IUiState

sealed class ChatPhrasesManageUiIntent : IUiIntent {
    data object Init : ChatPhrasesManageUiIntent()
    data class DeleteItem(val index:Int) : ChatPhrasesManageUiIntent()
}