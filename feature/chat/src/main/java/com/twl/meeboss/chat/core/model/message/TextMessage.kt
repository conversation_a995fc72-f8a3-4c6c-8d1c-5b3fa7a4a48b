package com.twl.meeboss.chat.core.model.message

import androidx.compose.ui.text.AnnotatedString
import cn.techwolf.international.washington.chat.protocol.WashingtonProtocol
import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.chat.core.uitls.StickerHelper
import com.twl.meeboss.chat.export.constant.LocalMessageType
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.common.utils.T

/**
 * 文本消息
 */
class TextMessage : ChatMessage() {
    var textBody: TextBody? = null
    val stickerContent: AnnotatedString by lazy {
        StickerHelper.buildAnnotatedString(textBody?.content.notNull())
    }
    init {
        localMessageType = LocalMessageType.MSG_TEXT
    }

    companion object {
        /**
         * 富元素开始字符
         */
        const val RICH_ELEMENT_BEGIN_STR = "\u200b"

        /**
         * 富元素结束字符
         */
        const val RICH_ELEMENT_END_STR = "\u2060"
    }

    override fun parseMessage(pbMessage: WashingtonProtocol.GeneralMessage) {
        textBody = TextBody(content = pbMessage.body.text.content,
            type = pbMessage.body.text.type,
            url = pbMessage.body.text.url,
            richElementList = parseRichElementListFromPb(pbMessage.body.text.richElementsList)
        )
    }

    /**
     * 从pb中解析富元素列表
     */
    private fun parseRichElementListFromPb(richElementList: List<WashingtonProtocol.RichElement>?): List<RichElement>? {
        return richElementList?.map {
            RichElement(it.elementType, it.originText, it.enhancedText)
        }
    }

    override fun parseFromDatabase() {
        textBody = try {
            GsonUtils.fromJson(content, TextBody::class.java)?: TextBody(content = content)
        } catch (e: Exception) {
            T.ss( "文本解析消息失败")
            TextBody(content = content)
        }

    }

    override fun prepareForDatabase() {
        content = GsonUtils.toJson(textBody)
    }

    override fun getSummary(): String = textBody?.content.notNull()

    fun isSingleSticker(): Boolean {
        return textBody?.type == 1 && !textBody?.url.isNullOrBlank()
    }
}

data class TextBody(val content: String = "", val type: Int = 0, val url: String? = null, val richElementList: List<RichElement>? = null) : BaseEntity

data class RichElement(val elementType: Int = 0, val originText: String = "", val enhancedText: String = ""): BaseEntity