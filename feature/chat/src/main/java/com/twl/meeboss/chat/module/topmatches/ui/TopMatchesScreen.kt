package com.twl.meeboss.chat.module.topmatches.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.awaitTouchSlopOrCancellation
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.lerp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.twl.meeboss.chat.R
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.component.button.XCommonOutlineButton
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.theme.COLOR_DDDDDD
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.theme.COLOR_FFE000
import kotlin.math.absoluteValue
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.stringResource
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.theme.COLOR_AAAAAA
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.offset
import androidx.compose.material3.Icon
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.CompositingStrategy
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import com.twl.meeboss.chat.module.topmatches.viewmodel.TopMatchesGuideState
import androidx.compose.ui.platform.LocalDensity
import com.airbnb.lottie.compose.LottieConstants
import com.twl.meeboss.core.ui.theme.COLOR_000000
import com.twl.meeboss.core.ui.utils.noRippleClickable
import kotlinx.coroutines.delay
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.material3.LocalTextStyle


interface OnTopMatchesClickListener {
    fun onNotInterestedClick(currentPage: Int) {}

    fun onQuickMessageClick(currentPage: Int) {}

    fun onBack() {}

    fun onNextGuide() {}

    fun onShowTopMatchesTipDialog(){}
}

@Composable
fun TopMatchesScreen(
    pagerState: PagerState,
    isNotEmpty: Boolean = true,
    disableBtn: Boolean = false,
    indicatorLength: Dp = 88.dp,
    guideState: TopMatchesGuideState = TopMatchesGuideState.None,
    onClickListener: OnTopMatchesClickListener? = null,
    detailContent: @Composable BoxScope.(page: Int) -> Unit = { _ -> }
) {
    var quickMessageButtonBounds by remember { mutableStateOf<Rect?>(null) }

    Box(
        Modifier
            .fillMaxSize()
            .background(color = COLOR_F5F5F5)
    ) {
        Image(
            painter = painterResource(R.drawable.chat_bg_highly_matches_arch),
            contentDescription = "bg",
            contentScale = ContentScale.FillHeight,
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.35f)
        )
        Column(
            Modifier
                .matchParentSize()
                .systemBarsPadding()
        ) {
            Box(
                Modifier
                    .padding(horizontal = 16.dp, vertical = 10.dp)
                    .fillMaxWidth(),
            ) {
                Image(
                    painter = painterResource(R.drawable.ui_back_arrow),
                    contentDescription = "back",
                    modifier = Modifier
                        .size(24.dp)
                        .clickable {
                            onClickListener?.onBack()
                        },
                )

                Text(
                    text = stringResource(R.string.message_list_enterance_top_matches_title),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = COLOR_222222,
                    modifier = Modifier
                        .padding(start = 16.dp)
                        .align(Alignment.Center),
                )

                Icon(
                    painter = painterResource(R.drawable.base_icon_title_tips),
                    tint = COLOR_484848,
                    contentDescription = "tip",
                    modifier = Modifier
                        .size(24.dp)
                        .align(Alignment.CenterEnd)
                        .noRippleClickable {
                            onClickListener?.onShowTopMatchesTipDialog()
                        }
                )
            }

            val offsetPresHold = 20

            HorizontalPager(
                state = pagerState,
                modifier = Modifier
                    .padding(top = 16.dp)
                    .weight(1f),
                contentPadding = PaddingValues(horizontal = 25.dp),
                pageSpacing = 10.dp
            ) { page ->
                val pageOffset = ((pagerState.currentPage - page) + pagerState.currentPageOffsetFraction).absoluteValue
                val scaleFactor = lerp(
                    start = 0.85f,
                    stop = 1.0f,
                    fraction = 1f - pageOffset.absoluteValue.coerceIn(0f, 1f)
                )
                Box(
                    Modifier
                        .graphicsLayer {
                            scaleY = scaleFactor
                        }
                        .fillMaxSize()
                        .clip(shape = RoundedCornerShape(24.dp))
                        .background(color = Color.White)
                        .pointerInput(Unit) {
                            if (isNotEmpty) {
                                awaitPointerEventScope {
                                    while (true) {
                                        val change = awaitFirstDown()
                                        awaitTouchSlopOrCancellation(change.id) { c, offset ->
                                            val isLast = pagerState.currentPage == pagerState.pageCount - 1
                                            if (isLast && offset.x < -offsetPresHold) {
                                                T.ssd(R.string.top_matches_no_more_left_toast)
                                            }
                                        }
                                    }
                                }
                            }
                        },
                ) {

                    if (guideState == TopMatchesGuideState.SweepAnimal) {
                        LaunchedEffect(Unit) {
                            delay(3000)
                            onClickListener?.onNextGuide()
                        }
                        Box(Modifier.blur(5.dp)) {
                            detailContent(this, page)
                        }
                        Box(
                            modifier = Modifier
                                .matchParentSize()
                                .alpha(0.9f)
                                .background(Color.White)
                                .clickable {
                                    onClickListener?.onNextGuide()
                                }
                                .pointerInput(Unit) {
                                    detectDragGestures { change, dragAmount ->
                                        onClickListener?.onNextGuide()
                                    }
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                val composition by rememberLottieComposition(LottieCompositionSpec.RawRes(R.raw.chat_anim_top_matches_sweep))
                                LottieAnimation(
                                    modifier = Modifier
                                        .size(width = 155.dp, height = 62.dp),
                                    contentScale = ContentScale.Crop,
                                    composition = composition,
                                    iterations = LottieConstants.IterateForever,
                                )
                                Text(
                                    text = stringResource(R.string.top_matches_instruction_swipe_to_see_more),
                                    fontSize = 16.sp,
                                    color = COLOR_000000,
                                )
                            }
                        }
                    } else {
                        detailContent(this, page)
                    }
                }
            }

            if (isNotEmpty) {
                Row(
                    Modifier
                        .padding(vertical = 10.dp)
                        .size(width = indicatorLength, height = 4.dp)
                        .align(Alignment.CenterHorizontally)
                ) {
                    val divider = 8.dp
                    val singleItemLength = (indicatorLength - divider * (pagerState.pageCount - 1)) / pagerState.pageCount
                    (0..pagerState.pageCount).forEach { inx ->
                        val color = if (inx == pagerState.currentPage) {
                            COLOR_FFE000
                        } else {
                            COLOR_DDDDDD
                        }

                        Box(
                            Modifier
                                .padding(start = if (inx != 0) divider else 0.dp)
                                .fillMaxHeight()
                                .width(singleItemLength)
                                .background(color, shape = RoundedCornerShape(2.dp))
                        )
                    }
                }
            }

            Row(
                Modifier
                    .padding(16.dp)
                    .fillMaxWidth()
            ) {
                if (isNotEmpty) {
                    XCommonOutlineButton(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.top_matches_not_interested_button),
                        enabled = !disableBtn,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Transparent,
                            disabledContainerColor = Color.Transparent,
                            contentColor = COLOR_222222,
                            disabledContentColor = COLOR_AAAAAA
                        ),
                        contentPadding = PaddingValues(horizontal = 10.dp, vertical = 6.5.dp),
                        onClick = {
                            if (guideState == TopMatchesGuideState.None) {
                                onClickListener?.onNotInterestedClick(pagerState.currentPage)
                            } else {
                                onClickListener?.onNextGuide()
                            }
                        }
                    )
                    XCommonButton(
                        modifier = Modifier
                            .padding(start = 12.dp)
                            .height(48.dp)
                            .onGloballyPositioned { coordinates ->
                                quickMessageButtonBounds = coordinates.boundsInWindow()
                            },
                        enableCustomModifier = true,
                        text = stringResource(R.string.top_matches_quick_message_button),
                        enabled = !disableBtn,
                        imageSource = R.drawable.chat_icon_message,
                        imageSize = 20.dp,
                        onClick = {
                            if (guideState == TopMatchesGuideState.None) {
                                onClickListener?.onQuickMessageClick(pagerState.currentPage)
                            } else {
                                onClickListener?.onNextGuide()
                            }
                        }
                    )
                } else {
                    XCommonButton(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.common_ok),
                        onClick = {
                            onClickListener?.onBack()
                        }
                    )
                }
            }
        }

        // Focus Mask Overlay // 聚焦蒙层遮罩
        if (guideState == TopMatchesGuideState.FocusMask && quickMessageButtonBounds != null) {
            LaunchedEffect(Unit) {
                delay(3000)
                onClickListener?.onNextGuide()
            }
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .graphicsLayer(compositingStrategy = CompositingStrategy.Offscreen)
                    .clickable {
                        onClickListener?.onNextGuide()
                    }
            ) {
                val buttonBounds = quickMessageButtonBounds!! // 安全的非空断言，因为前面已经检查过
                val overlayColor = Color.Black.copy(alpha = 0.7f) // 半透明蒙层颜色

                // 绘制覆盖整个屏幕的半透明蒙层
                drawRect(
                    color = overlayColor,
                    size = size // 画布的尺寸，即全屏
                )

                // 定义挖孔区域的圆角半径 (假设按钮高度为48.dp，形状为药丸形)
                val cutoutCornerRadius = CornerRadius(8.dp.toPx(), 8.dp.toPx())
                val verticalOffset = 5.dp.toPx()
                val horizontalOffset = 8.dp.toPx()

                // 通过绘制一个透明的圆角矩形并使用 BlendMode.Clear 来"挖出"按钮区域
                drawRoundRect(
                    topLeft = Offset(buttonBounds.left - horizontalOffset, buttonBounds.top - verticalOffset), // 挖孔区域的左上角坐标
                    size = Size(buttonBounds.width + 2 * horizontalOffset, buttonBounds.height + 2 * verticalOffset),    // 挖孔区域的尺寸
                    cornerRadius = cutoutCornerRadius,                       // 挖孔区域的圆角
                    color = Color.Transparent, // 使用BlendMode.Clear时，颜色通常不重要
                    blendMode = BlendMode.Clear      // 清除模式，使得这块区域变透明
                )
            }

            // 新增：用于提示信息的 Box
            val buttonRectPx = quickMessageButtonBounds!! // quickMessageButtonBounds 已确保非空
            val density = LocalDensity.current

            // --- Dynamically measure text and set tooltip width ---
            val textMeasurer = rememberTextMeasurer()
            val textContentForTooltip = stringResource(R.string.top_matches_instruction_interested_say_hi)

            val tooltipTextStyle = LocalTextStyle.current.copy(fontSize = 14.sp, color = COLOR_222222, fontWeight = FontWeight.Medium)

            val measuredText = textMeasurer.measure(
                text = textContentForTooltip,
                style = tooltipTextStyle
            )
            val measuredTextWidthInDp = with(density) { measuredText.size.width.toDp() }

            // Bubble's internal horizontal padding (e.g., 8dp on each side) based on chat_bg_yellow_tip.xml design
            val bubbleInternalHorizontalPaddingDp = 16.dp
            val dynamicTooltipWidthDp = measuredTextWidthInDp + bubbleInternalHorizontalPaddingDp
            // --- End of dynamic width calculation ---

            // 从按钮到挖孔边缘的偏移量 (根据您之前的修改)
            val cutoutOffsetVerticalDp = 5.dp
            val cutoutOffsetHorizontalDp = 8.dp

            // 提示框的属性
            // val tooltipWidthDp = 234.dp // This is now dynamicTooltipWidthDp
            val tooltipHeightDp = 55.dp     // 示例高度，您可以后续调整
            val gapAboveCutoutDp = 4.dp     // 提示框与挖孔区域顶部的间隙

            // 将 Dp 值转换为 Px 以便与 buttonRectPx (像素单位) 进行计算
            val cutoutOffsetVerticalPx = with(density) { cutoutOffsetVerticalDp.toPx() }
            val cutoutOffsetHorizontalPx = with(density) { cutoutOffsetHorizontalDp.toPx() }
            val tooltipWidthPx = with(density) { dynamicTooltipWidthDp.toPx() } // Use dynamic width
            val tooltipHeightPx = with(density) { tooltipHeightDp.toPx() }
            val gapAboveCutoutPx = with(density) { gapAboveCutoutDp.toPx() }

            // 计算挖孔区域的实际边界 (以像素为单位，相对于屏幕/窗口)
            // 挖孔区域的顶部边缘 Y 坐标
            val cutoutAreaTopPx = buttonRectPx.top - cutoutOffsetVerticalPx
            // 挖孔区域的右侧边缘 X 坐标
            val cutoutAreaRightPx = buttonRectPx.right + cutoutOffsetHorizontalPx

            // 计算提示框的左上角 X 坐标，使其右边缘与挖孔区域的右边缘对齐
            val tooltipTargetLeftPx = cutoutAreaRightPx - tooltipWidthPx

            // 计算提示框的左上角 Y 坐标，使其底部边缘在挖孔区域顶部之上，并留有 gapAboveCutoutDp 的间隙
            // tooltipTargetTopPx = (挖孔顶部 Y - 间隙) - 提示框高度
            val tooltipTargetTopPx = cutoutAreaTopPx - gapAboveCutoutPx - tooltipHeightPx

            Box(
                modifier = Modifier
                    .offset( // offset 是相对于其在父布局中原始位置的偏移
                        x = with(density) { tooltipTargetLeftPx.toDp() },
                        y = with(density) { tooltipTargetTopPx.toDp() }
                    )
                    .size(width = dynamicTooltipWidthDp, height = tooltipHeightDp), // Use dynamic width
                contentAlignment = Alignment.Center // 使内部文本居中
            ) {
                Image(
                    painter = painterResource(R.drawable.chat_bg_yellow_tip),
                    contentDescription = "yellow tip",
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.matchParentSize()
                )
                Text(
                    text = textContentForTooltip,
                    style = tooltipTextStyle,
                    modifier = Modifier.offset(y = (-3).dp),
                )
            }
        }
    }
}

@Composable
fun TopMatchesGuideDialog(desc: String, onDismiss: () -> Unit = {}) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false, // 允许对话框扩展到全屏宽度
            decorFitsSystemWindows = false, // 关键设置：允许装饰内容适应系统窗口
            dismissOnClickOutside = false
        ),
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .noRippleClickable { onDismiss() }, // 点击背景区域关闭弹窗
            contentAlignment = Alignment.BottomCenter
        ) {
            Column(
                Modifier
                    .fillMaxWidth()
                    .noRippleClickable {/* 阻止事件向上传递 */ }
                    .background(Color.White, shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                    .padding(top = 20.dp, bottom = 16.dp, start = 16.dp, end = 16.dp)

            ) {
                Image(
                    painter = painterResource(R.drawable.ui_dailog_close),
                    contentDescription = "close",
                    modifier = Modifier
                        .clickable {
                            onDismiss()
                        }
                        .size(20.dp)

                )

                Image(
                    painter = painterResource(R.drawable.chat_icon_highly_matches_guide),
                    contentDescription = "guide icon",
                    modifier = Modifier
                        .padding(top = 28.dp)
                        .align(Alignment.CenterHorizontally)
                        .size(80.dp)
                )

                Text(
                    modifier = Modifier
                        .padding(top = 16.dp)
                        .align(Alignment.CenterHorizontally),
                    text = stringResource(R.string.top_matches_instruction_title),
                    fontSize = 24.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = COLOR_222222
                )

                Text(
                    modifier = Modifier
                        .padding(vertical = 24.dp)
                        .align(Alignment.CenterHorizontally),
                    text = desc,
                    fontSize = 14.sp,
                    color = COLOR_484848
                )

                XCommonButton(
                    modifier = Modifier.padding(top = 40.dp),
                    text = stringResource(R.string.top_matches_instruction_button),
                    enabled = true,
                    onClick = {
                        onDismiss()
                    }
                )
            }
        }
    }
}

@Composable
fun TopMatchesHighlightsGuideDialog(title: String, desc: String, onDismiss: () -> Unit) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            decorFitsSystemWindows = false,
            dismissOnClickOutside = false
        ),
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .noRippleClickable { onDismiss() }, // 点击背景区域关闭弹窗
            contentAlignment = Alignment.BottomCenter
        ) {
            Column(
                Modifier
                    .fillMaxWidth()
                    .noRippleClickable {/* 阻止事件向上传递 */ }
                    .background(Color.White, shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                    .padding(top = 20.dp, bottom = 16.dp, start = 16.dp, end = 16.dp)

            ) {
                Image(
                    painter = painterResource(R.drawable.ui_dailog_close),
                    contentDescription = "close",
                    modifier = Modifier
                        .clickable {
                            onDismiss()
                        }
                        .size(20.dp)

                )

                Text(
                    modifier = Modifier
                        .padding(top = 28.dp),
                    text = title,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = COLOR_222222
                )

                Text(
                    modifier = Modifier
                        .padding(vertical = 24.dp)
                        .align(Alignment.CenterHorizontally),
                    text = desc,
                    fontSize = 14.sp,
                    color = COLOR_484848
                )

                XCommonButton(
                    modifier = Modifier.padding(top = 16.dp),
                    text = stringResource(R.string.top_matches_instruction_button),
                    enabled = true,
                    onClick = {
                        onDismiss()
                    }
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewTopMatchesScreenLoading() {
    TopMatchesScreen(
        pagerState = rememberPagerState(pageCount = { 5 }, initialPage = 1),
        disableBtn = true,
        guideState = TopMatchesGuideState.None
    )
}

@Preview(showBackground = true)
@Composable
fun PreviewTopMatchesScreenGuide() {
    TopMatchesGuideDialog(stringResource(R.string.employer_top_matches_instruction_subtitle))
}

@Preview(showBackground = true)
@Composable
fun PreviewTopMatchesHighlightGuide() {
    TopMatchesHighlightsGuideDialog(title = stringResource(R.string.employer_top_matches_instruction_ai_title), desc = stringResource(R.string.employer_top_matches_instruction_ai_subtitle)) {  }
}
