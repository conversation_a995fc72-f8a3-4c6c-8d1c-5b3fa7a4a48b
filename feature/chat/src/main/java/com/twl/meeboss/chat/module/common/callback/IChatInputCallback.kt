package com.twl.meeboss.chat.module.common.callback

import android.content.Intent
import androidx.compose.ui.text.input.TextFieldValue
import com.twl.meeboss.chat.module.common.components.input.ChatInputIntent
import com.twl.meeboss.chat.module.common.components.input.ChatInputUiState

interface IChatInputCallback {

    fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?)

    fun onInputStateChanged(intent: ChatInputIntent)
}
