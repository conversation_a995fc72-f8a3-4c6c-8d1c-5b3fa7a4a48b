package com.twl.meeboss.chat.module.common.file

import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.FileProvider
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.media.imagepicker.util.FileUtil.getMimeType
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.message.FileBody
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.component.button.XCommonMidButton
import com.twl.meeboss.core.ui.theme.COLOR_888888
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.theme.Primary
import dagger.hilt.android.AndroidEntryPoint
import java.io.File

@AndroidEntryPoint
class FilePreviewActivity : BaseMviActivity<FilePreviewViewModel>() {

    override val viewModel: FilePreviewViewModel by viewModels()

    companion object {
        const val BUNDLE_FILE_NAME = "bundleFileName"
        const val BUNDLE_FILE_KEY = "bundleFileKey"
        const val BUNDLE_FILE_SIZE = "bundleFileSize"
        const val BUNDLE_FILE_MD5 = "bundleFileMd5"

        fun intent(context: Context, fileBody: FileBody) {
            context.startActivity(Intent(context, FilePreviewActivity::class.java).apply {
                putExtra(BUNDLE_FILE_NAME, fileBody.name)
                putExtra(BUNDLE_FILE_KEY, fileBody.key)
                putExtra(BUNDLE_FILE_SIZE, fileBody.size)
                putExtra(BUNDLE_FILE_MD5, fileBody.md5)
            })
        }
    }

    override fun preInit(intent: Intent) {
        val fileName = intent.getStringExtra(BUNDLE_FILE_NAME) ?: ""
        val fileKey = intent.getStringExtra(BUNDLE_FILE_KEY) ?: ""
        val fileMd5 = intent.getStringExtra(BUNDLE_FILE_MD5) ?: ""
        val fileSize = intent.getIntExtra(BUNDLE_FILE_SIZE, 0)

        viewModel.fileBody = FileBody(
            name = fileName,
            key = fileKey,
            size = fileSize,
            md5 = fileMd5
        )
    }

    override fun initData() {
        viewModel.sendUiIntent(FilePreviewUiIntent.LoadFile)
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        LaunchedEffect(uiState.navigation) {
            uiState.navigation?.let {
                if (it is FilePreviewNavigation.OpenInAnotherApp) {
                    openInAnotherApp(it.filePath)
                }
            }
            viewModel.sendUiIntent(FilePreviewUiIntent.NavigationHandled)
        }

        FilePreviewContent(
            uiState = uiState,
            onDownloadClick = {
                viewModel.sendUiIntent(FilePreviewUiIntent.DownloadFile)
            },
            onOpenInAnotherAppClick = {
                viewModel.sendUiIntent(FilePreviewUiIntent.OpenInAnotherApp)
            }
        )
    }

    private fun openInAnotherApp(filePath: String) {
        try {
            val file = File(filePath)
            if (file.exists()) {
                val uri = FileProvider.getUriForFile(
                    this@FilePreviewActivity,
                    "${packageName}.provider",
                    file
                )

                val intent = Intent(Intent.ACTION_VIEW).apply {
                    setDataAndType(uri, getMimeType(file.name))
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }

                val chooser = Intent.createChooser(intent, file.name)
                chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(chooser)
            }
        } catch (e: Exception) {
            T.ss(" ${e.message}")
        }
    }
}

@Preview
@Composable
private fun FilePreviewContent(
    uiState: FilePreviewUiState = FilePreviewUiState(
        fileName = "11231.pdf",
        fileSize = "2.4MB",
        status = FileStatus.DOWNLOADING,
        downloadProgress = 0.5f
    ),
    onDownloadClick: () -> Unit = {},
    onOpenInAnotherAppClick: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        XTitleBar(title = uiState.fileName)
        Spacer(modifier = Modifier.height(200.dp))
        Image(
            painter = painterResource(id = R.drawable.chat_file_unknown),
            contentDescription = null,
            modifier = Modifier.size(60.dp)
        )

        Spacer(modifier = Modifier.height(20.dp))
        Text(
            text = stringResource(R.string.messages_file_preview_failed_unsupported_type),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Black,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(8.dp))

        if (uiState.status == FileStatus.DOWNLOADING) {
            Text(
                text = stringResource(R.string.messages_file_loading),
                fontSize = 14.sp,
                color = COLOR_888888,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(28.dp))
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                CustomProgressBar(
                    progress = uiState.downloadProgress,
                    modifier = Modifier
                        .width(300.dp)
                        .height(6.dp),
                )
            }
        } else {
            Text(
                text = "File size: ${uiState.fileSize}",
                fontSize = 14.sp,
                color = COLOR_888888,
                textAlign = TextAlign.Center
            )
        }

        Spacer(modifier = Modifier.height(28.dp))

        // 根据下载状态显示不同的按钮
        when {
            uiState.status == FileStatus.DOWNLOADING -> {
                // 下载中，不显示按钮
            }

            uiState.status == FileStatus.DOWNLOADED || uiState.downloadProgress >= 1f -> {
                XCommonMidButton(
                    buttonText = stringResource(R.string.messages_file_open_in_another_app_button),
                    modifier = Modifier
                        .padding(horizontal = 16.dp),
                    onClick = onOpenInAnotherAppClick
                )
            }

            uiState.status == FileStatus.FAILED -> {
                XCommonMidButton(
                    buttonText = stringResource(R.string.messages_file_download_try_again),
                    modifier = Modifier
                        .padding(horizontal = 16.dp),
                    onClick = onDownloadClick
                )
            }

            else -> {
                XCommonMidButton(
                    buttonText = stringResource(R.string.messages_file_download_button),
                    modifier = Modifier
                        .padding(horizontal = 16.dp),
                    onClick = onDownloadClick
                )
            }
        }
    }
}

@Composable
fun CustomProgressBar(
    progress: Float, // 0.0f 到 1.0f
    modifier: Modifier = Modifier,
    height: Dp = 6.dp,
    backgroundColor: Color = COLOR_F5F5F5,
    progressColor: Color = Primary,
    cornerRadius: Dp = 4.dp
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(height)
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(cornerRadius)
            )
    ) {
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .fillMaxWidth(progress.coerceIn(0.0f, 1.0f))
                .background(
                    color = progressColor,
                    shape = RoundedCornerShape(cornerRadius)
                )
        )
    }
}