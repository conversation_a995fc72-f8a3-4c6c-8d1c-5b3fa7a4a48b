package com.twl.meeboss.chat.module.setting.home

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.sendStringLiveEvent
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.safetyGetSerializableExtra
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.export.ChatPageRouter
import com.twl.meeboss.chat.export.ChatRouterPath
import com.twl.meeboss.chat.export.constant.BlackListStatus
import com.twl.meeboss.chat.export.model.Conversation
import com.twl.meeboss.chat.module.common.dialog.showDeleteConversationDialog
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.core.ui.dialog.showConfirmDialog
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.AndroidEntryPoint

/**
 * @author: 冯智健
 * @date: 2024年08月18日 10:18
 * @description:
 */
@AndroidEntryPoint
@RouterPage(path = [ChatRouterPath.CHAT_SETTING_PAGE])
class ChatSettingActivity : BaseMviActivity<ChatSettingViewModel>() {

    override val viewModel: ChatSettingViewModel by viewModels()

    override fun preInit(intent: Intent) {

    }

    override fun initData() {
        viewModel.finishAct.observe(this) {
            onBackPressed()
        }
    }

    override fun onBackPressed() {
        if (viewModel.uiStateFlow.value.blacklistSwitch) { //如果开关是打开的，说明拉黑了
            intent.safetyGetSerializableExtra(ChatPageRouter.BUNDLE_CONVERSION, Conversation::class.java)?.run {
                sendStringLiveEvent(EventBusKey.CONTACT_BE_ADD_TO_BLACKLIST, this.friendId)
            }
        }
        super.onBackPressed()
    }

    @Composable
    override fun ComposeContent() {
        val conversation: MutableState<Conversation?> = rememberSaveable {
            mutableStateOf(intent.safetyGetSerializableExtra(ChatPageRouter.BUNDLE_CONVERSION, Conversation::class.java))
        }
        val vm: ChatSettingViewModel by viewModels()
        val uiState by vm.uiStateFlow.collectAsStateWithLifecycle()
        val blacklistStatus = rememberSaveable {
            mutableStateOf(conversation.value?.blacklist ?: false)
        }
        LaunchedEffect(uiState.blacklistSwitch) {
            blacklistStatus.value = uiState.blacklistSwitch
        }
        LaunchedEffect(uiState.deleteFinish) {
            if (uiState.deleteFinish) {
                finish()
            }
        }
        val isGeek = UserProvider.isGeek()
        ChatSettingPage(
            isGeek,
            blacklistStatus.value,
            conversation.value,
            onBlockStatusChange = {
                if (it) {
                    showConfirmDialog(
                        title =
                        if (isGeek) R.string.chat_block_recruiter_title.toResourceString()
                        else R.string.chat_block_candidate_title.toResourceString(),
                        content =
                        if (isGeek) R.string.chat_block_recruiter_desc.toResourceString()
                        else R.string.chat_block_candidate_desc.toResourceString(),
                        confirmText = R.string.common_block.toResourceString(),
                        cancelText = R.string.common_button_cancel.toResourceString(),
                        onConfirm = {
                            conversation.value?.let { value ->
                                vm.sendUiIntent(ChatSettingUiIntent.UpdateBlacklistStatus(value.friendId, BlackListStatus.TO_ADD))
                            }
                        }
                    )
                } else {
                    conversation.value?.let { value ->
                        vm.sendUiIntent(ChatSettingUiIntent.UpdateBlacklistStatus(value.friendId, BlackListStatus.TO_REMOVE))
                    }
                }
            },
            onDeleteClick = {
                showDeleteConversationDialog{
                    conversation.value?.let { value ->
                        vm.sendUiIntent(ChatSettingUiIntent.DeleteConversation(value.friendId, value.friendIdentity))
                    }
                }
            }
        )
    }
}