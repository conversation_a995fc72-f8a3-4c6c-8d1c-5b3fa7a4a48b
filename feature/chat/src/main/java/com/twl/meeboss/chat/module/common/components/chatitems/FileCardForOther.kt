package com.twl.meeboss.chat.module.common.components.chatitems

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.twl.meeboss.chat.core.model.MessageModel
import com.twl.meeboss.chat.core.model.message.FileBody
import com.twl.meeboss.chat.core.model.message.FileMessage

@Composable
fun FileCardForOther(
    modifier: Modifier = Modifier,
    model: MessageModel.FileForOther,
    onFileClick: (FileMessage) -> Unit = {}
) {
    ChatOtherBox(modifier = modifier, model = model) {
        FileCardBody(
            message = model.message,
            onFileClick = onFileClick
        )
    }
}


@Preview
@Composable
fun PreViewFileCardForOther() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = Color.White)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        FileCardForOther(
            model = MessageModel.FileForOther(FileMessage().apply {
                fileBody = FileBody(
                    fileType = 1,
                    name = "12312313123.pdf",
                    size = 1231231,
                )
            }),
        )
    }
}