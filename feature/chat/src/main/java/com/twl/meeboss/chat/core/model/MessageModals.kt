package com.twl.meeboss.chat.core.model

import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.FileMessage
import com.twl.meeboss.chat.core.model.message.HintMessage
import com.twl.meeboss.chat.core.model.message.PictureMessage
import com.twl.meeboss.chat.core.model.message.TextMessage
import com.twl.meeboss.chat.core.model.message.custom.ChatGreetingJobMessage
import com.twl.meeboss.chat.core.model.message.custom.ChatGreetingResumeMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangeEmailRequestMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangeEmailResultMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangePhoneRequestMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangePhoneResultMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangeResumeRequestMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangeResumeResultMessage
import com.twl.meeboss.chat.core.model.message.custom.FirstMsgBrowseCandidatesMessage
import com.twl.meeboss.chat.core.model.message.custom.FirstMsgBrowseJobsMessage
import com.twl.meeboss.chat.core.model.message.custom.FirstMsgPostJobMessage
import com.twl.meeboss.chat.core.model.message.custom.InterestGrayHintMessage
import com.twl.meeboss.chat.core.model.message.custom.SendResumeRequestMessage

abstract class BaseMessageModel<T>(val id: Long, val message: T) : BaseEntity {
    //用于优化列表
    abstract fun getItemType(): String
}

sealed class MessageModel<T : ChatMessage>(id: Long, msg: T) : BaseMessageModel<T>(id, msg) {
    var tinyAvatar:String = ""
    var avatar:String = ""

    override fun getItemType(): String {
        return "${message.localMessageType}${message.fromId}"
    }

    //个人文本消息
    data class TextForSelf(val data: TextMessage) : MessageModel<TextMessage>(data.id, data)

    //对方文本消息
    data class TextForOther(val data: TextMessage) : MessageModel<TextMessage>(data.id, data)

    //灰色提示消息
    data class GrayHint(val data: HintMessage) : MessageModel<HintMessage>(data.id, data)

    //图片卡片
    data class ImageForSelf(val data: PictureMessage) : MessageModel<PictureMessage>(data.id, data)

    //图片卡片
    data class ImageForOther(val data: PictureMessage) : MessageModel<PictureMessage>(data.id, data)

    //文件卡片
    data class FileForSelf(val data: FileMessage) : MessageModel<FileMessage>(data.id, data)

    //文件卡片
    data class FileForOther(val data: FileMessage) : MessageModel<FileMessage>(data.id, data)

    //版本不支持卡片
    data class NotSupportForSelf(val data: ChatMessage) : MessageModel<ChatMessage>(data.id, data)

    //版本不支持卡片
    data class NotSupportForOther(val data: ChatMessage) : MessageModel<ChatMessage>(data.id, data)

    //C欢迎职位卡片
    data class GreetingJobFromGeek(val data: ChatGreetingJobMessage) : MessageModel<ChatGreetingJobMessage>(data.id, data)

    //B欢迎职位卡片,B发C收
    data class GreetingJobFromBoss(val data: ChatGreetingJobMessage) : MessageModel<ChatGreetingJobMessage>(data.id, data)

    //C欢迎简历卡片,C发B收
    data class GreetingResumeFromGeek(val data: ChatGreetingResumeMessage) : MessageModel<ChatGreetingResumeMessage>(data.id, data)

    //B欢迎简历卡片
    data class GreetingResumeFromBoss(val data: ChatGreetingResumeMessage) : MessageModel<ChatGreetingResumeMessage>(data.id, data)

    //感兴趣灰条
    data class InterestGrayHint(val data: InterestGrayHintMessage) : MessageModel<InterestGrayHintMessage>(data.id, data)

    //请求交换邮箱(只有对方发过来)
    data class RequestEmailForOther(val data:ExchangeEmailRequestMessage) : MessageModel<ExchangeEmailRequestMessage>(data.id, data)

    //请求交换邮箱结果(只有对方发过来)
    data class ExchangeEmailResultForOther(val data:ExchangeEmailResultMessage) : MessageModel<ExchangeEmailResultMessage>(data.id, data)

    //请求交换电话(只有对方发过来)
    data class RequestPhoneForOther(val data: ExchangePhoneRequestMessage) : MessageModel<ExchangePhoneRequestMessage>(data.id, data)

    //请求交换电话结果(只有对方发过来)
    data class ExchangePhoneResultForOther(val data:ExchangePhoneResultMessage) : MessageModel<ExchangePhoneResultMessage>(data.id, data)
    //请求交换简历（索要简历，来自Boss）
    data class ExchangeResumeRequestFromBoss(val data: ExchangeResumeRequestMessage):MessageModel<ExchangeResumeRequestMessage>(data.id,data)
    //请求发送简历（发送简历，来自Geek）
    data class RequestSendResumeFromGeek(val data: SendResumeRequestMessage):MessageModel<SendResumeRequestMessage>(data.id,data)
    //简历结果,C看到自己的发送结果
    data class ExchangeResumeResultForGeek(val data: ExchangeResumeResultMessage):MessageModel<ExchangeResumeResultMessage>(data.id,data)
    //简历结果，B看到来自C发来的简历
    data class ExchangeResumeResultForBoss(val data: ExchangeResumeResultMessage):MessageModel<ExchangeResumeResultMessage>(data.id,data)
    //first message : post a job
    data class FirstMsgPostJobMessageFromMeeBoss(val data: FirstMsgPostJobMessage):MessageModel<FirstMsgPostJobMessage>(data.id,data)
    //first message : browse candidate
    data class FirstMsgBrowseCandidatesMessageFromMeeBoss(val data: FirstMsgBrowseCandidatesMessage):MessageModel<FirstMsgBrowseCandidatesMessage>(data.id,data)
    //first message : browse jobs
    data class FirstMsgBrowseJobsMessageFromMeeBoss(val data: FirstMsgBrowseJobsMessage):MessageModel<FirstMsgBrowseJobsMessage>(data.id,data)

}