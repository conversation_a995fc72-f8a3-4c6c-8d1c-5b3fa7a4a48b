package com.twl.meeboss.chat.module.notification.activity

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.components.list.refresh.XRefreshList
import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.export.ChatRouterPath
import com.twl.meeboss.chat.module.notification.component.NotificationItemComponent
import com.twl.meeboss.chat.module.notification.model.SystemNotificationItemResult
import com.twl.meeboss.chat.module.notification.viewmodel.NotificationUiIntent
import com.twl.meeboss.chat.module.notification.viewmodel.NotificationUiState
import com.twl.meeboss.chat.module.notification.viewmodel.NotificationViewModel
import com.twl.meeboss.core.ui.component.state.XEmptyContent
import com.twl.meeboss.core.ui.theme.XTheme
import dagger.hilt.android.AndroidEntryPoint
import java.util.UUID


@RouterPage(path = [ChatRouterPath.CHAT_SYSTEM_NOTIFICATION_PAGE])
@AndroidEntryPoint
class NotificationActivity() : BaseMviActivity<NotificationViewModel>() {

    override val viewModel: NotificationViewModel by viewModels()

    override fun preInit(intent: Intent) {
        viewModel.sendUiIntent(NotificationUiIntent.GetNotificationList(true))
    }

    override fun initData() {
    }

    @Composable
    override fun ComposeContent() {
        NotificationContent(
            uiState = viewModel.uiStateFlow.collectAsState().value,
            onButtonClick = {
                viewModel.sendUiIntent(NotificationUiIntent.UpdateReadStatus(it))
            },
            onItemClick = {
                if (it.buttons.isEmpty()) {
                    //有按钮不更新已读状态
                    viewModel.sendUiIntent(NotificationUiIntent.UpdateReadStatus(it))
                }
            },
            onRefresh = {
                viewModel.sendUiIntent(NotificationUiIntent.GetNotificationList(true))
            },
            onLoadMore = {
                viewModel.sendUiIntent(NotificationUiIntent.GetNotificationList(false))
            },
        )
    }

}

@Composable
fun NotificationContent(
    modifier: Modifier = Modifier,
    uiState: NotificationUiState,
    onButtonClick: (item: SystemNotificationItemResult) -> Unit = {},
    onItemClick: (item: SystemNotificationItemResult) -> Unit = {},
    onRefresh: () -> Unit = {},
    onLoadMore: () -> Unit = {},
) {
    XTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
        ) {
            XTitleBar(
                title = stringResource(id = R.string.common_notifications),
                showDivider = true
            )

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(top = 12.dp, start = 12.dp, end = 12.dp)
            ) {
                XRefreshList(
                    listState = uiState.listState,
                    getUniqueKey = {
                        it.notificationId.ifBlank { UUID.randomUUID().toString() }
                    },
                    onRefresh = onRefresh,
                    onLoadMore = onLoadMore,
                    emptyContent = {
                        XEmptyContent(text = stringResource(id = R.string.no_notification_tips))
                    }
                ) { item, index ->
                    NotificationItemComponent(
                        data = item,
                        localShowTime = if (index == 0) true else item.groupBy != uiState.listState.list[index - 1].groupBy,
                        onButtonClick = onButtonClick,
                        onItemClick = onItemClick,
                    )
                }
            }
        }
    }
}


@Preview
@Composable
private fun PreviewNotificationContent() {
    NotificationContent(uiState = NotificationUiState(listState = XRefreshListState.getPreviewDefault()))
}