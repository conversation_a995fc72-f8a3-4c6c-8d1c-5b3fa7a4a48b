package com.twl.meeboss.chat.module.phrase.viewmodel

import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.module.phrase.model.ChatPhrasesItemResult
import com.twl.meeboss.chat.repos.ChatRepository
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.handleDefaultError
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class ChatPhrasesEditViewModel @Inject constructor(
    private val repos: ChatRepository
) : BaseMviViewModel<ChatPhrasesEditUiState, ChatPhrasesEditUiIntent>() {
    val saveSuccess:MutableLiveData<Boolean> = MutableLiveData()

    override fun initUiState(): ChatPhrasesEditUiState = ChatPhrasesEditUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is ChatPhrasesEditUiIntent.Init -> {
                sendUiState {
                    copy(
                        input = TextFieldValue(intent.phrasesItemResult.text, TextRange(intent.phrasesItemResult.text.length)),
                        initialText = intent.phrasesItemResult.text
                    ).checkCanSave()
                }
            }
            is ChatPhrasesEditUiIntent.Save ->{
                savePhraseItem(intent.phrasesItemResult)
            }
            is ChatPhrasesEditUiIntent.OnValueChange ->{
                sendUiState {
                    copy(
                        input = intent.input
                    ).checkCanSave()
                }
            }
            else->{

            }

        }
    }


    private fun savePhraseItem(phrasesItemResult: ChatPhrasesItemResult? = null) {
        val legalText = uiStateFlow.value.input.text.trim()
        if (legalText.length > DefaultValueConstants.MAX_INPUT_CHAT_PHRASE) {
            T.ss(R.string.common_description_out_of_max_range.toResourceString(DefaultValueConstants.MAX_INPUT_CHAT_PHRASE.toString()))
            return
        }
        if (legalText.isEmpty()) {
            //输入纯空格的时候不允许提交到服务器
            return
        }

        showLoadingDialog()
        requestData(
            request = {
                repos.saveCommonPhrases(
                    id = phrasesItemResult?.id,
                    text = uiStateFlow.value.input.text.trim()
                )
            },
            success = {
                dismissLoadingDialog()
                saveSuccess.postValue(true)
            },
            fail = {
                dismissLoadingDialog()
                it.handleDefaultError()
            }
        )

    }


}

data class ChatPhrasesEditUiState(
    val input:TextFieldValue = TextFieldValue(),
    val saveEnable:Boolean = false,
    val initialText: String = ""
) : IUiState {
    fun checkCanSave(): ChatPhrasesEditUiState {
        val legalText = input.text.trim()
        return copy(
            saveEnable = legalText.isNotEmpty() && legalText.length <= DefaultValueConstants.MAX_INPUT_CHAT_PHRASE
        )
    }
    
    fun hasChange(): Boolean {
        return input.text.trim() != initialText.trim() && input.text.trim().isNotEmpty()
    }
}

sealed class ChatPhrasesEditUiIntent : IUiIntent {
    data class Init(val phrasesItemResult: ChatPhrasesItemResult) : ChatPhrasesEditUiIntent()
    data class Save(val phrasesItemResult: ChatPhrasesItemResult? = null) : ChatPhrasesEditUiIntent()
    data class OnValueChange(val input:TextFieldValue) : ChatPhrasesEditUiIntent()
}