package com.twl.meeboss.chat.module.common.components.chatitems

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.message.FileMessage
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.utils.noRippleClickable


@Composable
fun FileCardBody(
    modifier: Modifier = Modifier,
    message: FileMessage,
    onFileClick: (FileMessage) -> Unit = {}
) {
    Row(
        modifier = modifier
            .width(280.dp)
            .border(
                width = 1.5.dp,
                color = Color(0xFFEBEBEB),
                shape = RoundedCornerShape(size = 8.dp)
            )
            .background(color = Color.White, shape = RoundedCornerShape(8.dp))
            .padding(16.dp)
            .noRippleClickable { onFileClick(message) }
    ) {
        Image(
            modifier = Modifier
                .width(40.dp)
                .height(40.dp),
            painter = painterResource(
                if (message.fileBody.fileType == 10) {
                    R.drawable.chat_file_pdf
                } else {
                    R.drawable.chat_file_unknown
                }
            ),
            contentDescription = null
        )

        Spacer(modifier = Modifier.width(12.dp))
        Column {
            Text(
                text = message.fileBody.name,
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 20.sp,
                    fontWeight = FontWeight(510),
                    color = Black222222,
                ),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(3.dp))
            Text(
                text = formatFileSizeSimple(message.fileBody.size.toLong()),
                style = TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 18.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF888888),
                )
            )

        }
    }


}

@Composable
fun CircularProgressIndicator(
    progress: Float,
    modifier: Modifier = Modifier,
    strokeWidth: Dp = 8.dp,
    backgroundColor: Color = Color.White.copy(alpha = 0.3f),
    progressColor: Color = Color.White,
    size: Dp = 120.dp
) {
    Box(
        modifier = modifier.size(size),
        contentAlignment = Alignment.Center
    ) {
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            val canvasSize = size.toPx()
            val strokeWidthPx = strokeWidth.toPx()
            val center = Offset(canvasSize / 2, canvasSize / 2)
            val radius = (canvasSize - strokeWidthPx) / 2

            // 绘制背景圆环
            drawCircle(
                color = backgroundColor,
                radius = radius,
                center = center,
                style = Stroke(
                    width = strokeWidthPx,
                    cap = StrokeCap.Round
                )
            )

            // 绘制进度圆弧
            val sweepAngle = 360f * progress
            drawArc(
                color = progressColor,
                startAngle = -90f, // 从顶部开始
                sweepAngle = sweepAngle,
                useCenter = false,
                topLeft = Offset(
                    (canvasSize - 2 * radius) / 2,
                    (canvasSize - 2 * radius) / 2
                ),
                size = Size(2 * radius, 2 * radius),
                style = Stroke(
                    width = strokeWidthPx,
                    cap = StrokeCap.Round
                )
            )
        }
    }
}

/**
 * 简化版本，只支持KB、MB、GB
 */
fun formatFileSizeSimple(bytes: Long): String {
    return when {
        bytes < 1024 -> "$bytes B"
        bytes < 1024 * 1024 -> "%.1f KB".format(bytes / 1024.0)
        bytes < 1024 * 1024 * 1024 -> "%.1f M".format(bytes / (1024.0 * 1024))
        else -> "%.1f G".format(bytes / (1024.0 * 1024 * 1024))
    }
}