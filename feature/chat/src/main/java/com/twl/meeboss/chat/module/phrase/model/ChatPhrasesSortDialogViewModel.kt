package com.twl.meeboss.chat.module.phrase.model

import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.chat.repos.ChatRepository
import com.twl.meeboss.common.ktx.toJson
import com.twl.meeboss.core.network.handleDefaultError
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class ChatPhrasesSortDialogViewModel @Inject constructor(
    private val repos: ChatRepository
) : BaseMviViewModel<ChatPhrasesSortDialogUiState, ChatPhrasesSortDialogUiIntent>() {
    override fun initUiState(): ChatPhrasesSortDialogUiState = ChatPhrasesSortDialogUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is ChatPhrasesSortDialogUiIntent.Save ->{
                save()
            }
            is ChatPhrasesSortDialogUiIntent.Init ->{
                sendUiState {
                    copy(
                        phraseList = intent.phraseList,
                        saveSuccess = false,
                    )
                }
            }
            is ChatPhrasesSortDialogUiIntent.OnMoveChange -> {
                onMoveChange(intent.fromIndex, intent.targetIndex)
            }
            else->{

            }

        }
    }

    private fun save(){
        showLoadingDialog()
        requestData(
            request = {
                val ids = uiStateFlow.value.phraseList.map { it.id }.toJson()
                repos.sortCommonPhrases(ids)
            },
            success = {
                dismissLoadingDialog()
                sendUiState {
                    copy(
                        saveSuccess = true,
                    )
                }
            },
            fail = {
                dismissLoadingDialog()
                it.handleDefaultError()
            }
        )
    }

    private fun onMoveChange(fromIndex: Int, targetIndex: Int) {
        val tempList = uiStateFlow.value.phraseList.toMutableList()
        tempList.add(targetIndex, tempList.removeAt(fromIndex))
        sendUiState {
            copy(
                phraseList = tempList
            )
        }
    }

}

data class ChatPhrasesSortDialogUiState(
    val phraseList:List<ChatPhrasesItemResult> = listOf(),
    val saveSuccess:Boolean = false,
) : IUiState

sealed class ChatPhrasesSortDialogUiIntent : IUiIntent {
    data object Save : ChatPhrasesSortDialogUiIntent()
    data class Init(val phraseList:List<ChatPhrasesItemResult>): ChatPhrasesSortDialogUiIntent()
    data class OnMoveChange(val fromIndex:Int, val targetIndex:Int): ChatPhrasesSortDialogUiIntent()
}