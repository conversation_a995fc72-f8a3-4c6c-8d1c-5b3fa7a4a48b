package com.twl.meeboss.chat.module.geek.viewmodel

import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.chat.module.geek.bottomsheet.GreetingType
import com.twl.meeboss.chat.repos.ChatBossRepository
import com.twl.meeboss.chat.repos.ChatGeekRepository
import com.twl.meeboss.chat.utils.ChatPointReporter
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.common.utils.T
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

const val MAX_GREETING_LENGTH = 300
const val MIN_GREETING_LENGTH = 5

@HiltViewModel
class CommonEditGreetingViewModel @Inject constructor(
    private val geekRepository: ChatGeekRepository,
    private val bossRepository: ChatBossRepository
) : BaseMviViewModel<CommonEditGreetingUiState, CommonEditGreetingUiIntent>() {

    var jobId: String = ""
    var friendId: String = ""

    var source: GreetingType = GreetingType.StartGreeting

    val sendResult: MutableLiveData<Boolean> = MutableLiveData()

    fun isStartChatGreeting() = source == GreetingType.StartGreeting

    override fun initUiState(): CommonEditGreetingUiState = CommonEditGreetingUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is CommonEditGreetingUiIntent.Send -> {
                saveGreetingText()
                sendGreetingText()
            }

            is CommonEditGreetingUiIntent.OnTextChanged -> {
                sendUiState {
                    copy(text = intent.text)
                }
            }

            is CommonEditGreetingUiIntent.OnCheckChanged -> {
                sendUiState {
                    copy(isCheck = intent.isCheck)
                }
            }

            else -> {

            }

        }
    }

    private fun sendGreetingText() {
        requestData(
            enableLoadState = false,
            request = {
                val text = uiStateFlow.value.text.text
                if (UserProvider.isGeek()) {
                    if(isStartChatGreeting()){
                        geekRepository.sendGreetingText(0, text, friendId, jobId)
                    }else{
                        geekRepository.sendInterestText(0, text, friendId, jobId)
                    }
                } else {
                    bossRepository.sendGreetingText(0, text, friendId, jobId)
                }
            },
            success = {
                it?.let { _ ->
                    sendResult.postValue(true)
                }
            },
            fail = {
                T.ss(it.message)
                XLog.error(TAG, it.message)
            }
        )
        ChatPointReporter.greetingGuideSend(source.value,uiStateFlow.value.isCheck,0)
    }

    private fun saveGreetingText() {
        val isCheck = uiStateFlow.value.isCheck
        if (isCheck) {
            requestData(
                enableLoadState = false,
                request = {
                    val text = uiStateFlow.value.text.text
                    if (UserProvider.isGeek()) {
                        if(isStartChatGreeting()){
                            geekRepository.saveGreetingText(0, text, true)
                        }else{
                            geekRepository.saveInterestText(0, text, true)
                        }
                    } else {
                        bossRepository.saveGreetingText(0, text, true)
                    }
                },
                success = {

                },
                fail = {
                    XLog.error(TAG, it.message)
                }
            )
        }

    }

}

data class CommonEditGreetingUiState(
    val maxLength: Int = MAX_GREETING_LENGTH,
    val isCheck: Boolean = true,
    val text: TextFieldValue = TextFieldValue("")
) : IUiState

sealed class CommonEditGreetingUiIntent : IUiIntent {
    data object Send : CommonEditGreetingUiIntent()
    data class OnTextChanged(val text: TextFieldValue) : CommonEditGreetingUiIntent()
    data class OnCheckChanged(val isCheck: Boolean) : CommonEditGreetingUiIntent()
}