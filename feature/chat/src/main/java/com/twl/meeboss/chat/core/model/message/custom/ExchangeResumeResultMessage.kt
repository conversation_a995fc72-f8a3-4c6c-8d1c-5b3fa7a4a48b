package com.twl.meeboss.chat.core.model.message.custom

import cn.techwolf.international.washington.chat.protocol.WashingtonProtocol
import com.twl.meeboss.base.ktx.report
import com.twl.meeboss.chat.export.constant.LocalMessageType
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.bean.ExchangeResumeResultBean
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.utils.GsonUtils

/**
 * 交换邮箱结果
 */
class ExchangeResumeResultMessage : ChatMessage() {

    var resumeBean: ExchangeResumeResultBean? = null

    init {
        localMessageType = LocalMessageType.EXCHANGE_RESUME_RESULT
    }

    override fun parseMessage(pbMessage: WashingtonProtocol.GeneralMessage) {
        try {
            resumeBean = GsonUtils.fromJson(pbMessage.body?.custom?.content, ExchangeResumeResultBean::class.java)
        } catch (e: Exception) {
            e.report("parse_exchange_resume_result_message_pb_exception")
        }
    }

    override fun parseFromDatabase() {
        try {
            resumeBean = GsonUtils.fromJson(content, ExchangeResumeResultBean::class.java)
        } catch (e: Exception) {
            e.report("parse_exchange_resume_result_message_db_exception")
        }
    }

    override fun prepareForDatabase() {
        content = GsonUtils.toJson(resumeBean)
    }

    override fun getSummary(): String = resumeBean?.fileName.notNull()
}

