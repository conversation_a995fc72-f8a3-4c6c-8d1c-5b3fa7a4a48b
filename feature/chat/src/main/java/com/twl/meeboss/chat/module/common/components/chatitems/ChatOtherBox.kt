package com.twl.meeboss.chat.module.common.components.chatitems

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.glide.GlideImage
import com.skydoves.landscapist.placeholder.placeholder.PlaceholderPlugin
import com.twl.meeboss.base.media.pictureselector.previewImages
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.MessageModel
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.utils.ChatPointReporter
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.core.ui.utils.noRippleClickable

@Composable
fun ChatOtherBox(modifier: Modifier = Modifier, model: MessageModel<out ChatMessage>,content: @Composable () -> Unit) {
    val context = LocalContext.current
    Box(modifier = modifier.fillMaxWidth(), contentAlignment = Alignment.CenterStart) {
        Row {
            GlideImage(
                imageModel = { model.tinyAvatar },
                imageOptions = ImageOptions(contentDescription = "user avatar"),
                previewPlaceholder = painterResource(id = R.mipmap.base_avatar_placeholder),
                component = rememberImageComponent {
                    +PlaceholderPlugin.Loading(painterResource(id = R.drawable.base_company_default_logo))
                    +PlaceholderPlugin.Failure(painterResource(id = R.drawable.base_company_default_logo))
                },
                modifier = Modifier
                    .size(30.dp)
                    .clip(CircleShape)
                    .align(Alignment.Bottom)
            )
            Spacer(modifier = Modifier.width(8.dp))
            content()
        }
    }
}