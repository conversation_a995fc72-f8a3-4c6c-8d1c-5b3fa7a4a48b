package com.twl.meeboss.chat.module.boss.activity.callback

import android.content.Intent
import com.twl.meeboss.chat.api.resp.QuickReplyTemplateItem
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.FileMessage
import com.twl.meeboss.chat.core.model.message.custom.ChatGreetingJobMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangeEmailRequestMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangePhoneRequestMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangeResumeRequestMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangeResumeResultMessage
import com.twl.meeboss.chat.core.model.message.custom.FirstMsgBrowseCandidatesMessage
import com.twl.meeboss.chat.core.model.message.custom.FirstMsgBrowseJobsMessage
import com.twl.meeboss.chat.core.model.message.custom.FirstMsgPostJobMessage
import com.twl.meeboss.chat.core.model.message.custom.SendResumeRequestMessage
import com.twl.meeboss.chat.module.common.callback.IChatInputCallback
import com.twl.meeboss.chat.module.common.callback.IChatMessageClickCallback
import com.twl.meeboss.chat.module.common.callback.IHeaderAreaClick
import com.twl.meeboss.chat.module.common.components.input.ChatInputIntent
import com.twl.meeboss.chat.module.common.model.ChatTopAction

interface IBossUIClickCallback: IHeaderAreaClick, IChatMessageClickCallback, IChatInputCallback {
    fun onStarClick()
}

class BossUIClickCallbackDefault: IBossUIClickCallback {
    override fun onClickBack() {
    }

    override fun onClickMore() {
    }

    override fun onStarClick() {
    }

    override fun onClickTopButton(action: ChatTopAction) {
    }

    override fun sendRead() {

    }

    override fun onClickResend(message: ChatMessage) {
    }

    override fun onPreviewImage(message: ChatMessage) {
    }

    override fun onPreviewFile(message: FileMessage) {

    }

    override fun onClickJobCardInterest(message: ChatGreetingJobMessage) {
    }


    override fun onClickEmailRequest(message: ExchangeEmailRequestMessage, isAccept: Boolean) {
    }

    override fun onClickPhoneRequest(message: ExchangePhoneRequestMessage, isAccept: Boolean) {
    }

    override fun onClickResumeRequest(message: ExchangeResumeRequestMessage, isAccept: Boolean) {
    }

    override fun onClickResumeResult(message: ExchangeResumeResultMessage) {
    }

    override fun onClickSendResumeRequest(message: SendResumeRequestMessage, isAccept: Boolean) {
    }

    override fun onClickFirstMsgPostJob(message: FirstMsgPostJobMessage) {

    }

    override fun onClickFirstMsgBrowseCandidate(message: FirstMsgBrowseCandidatesMessage) {

    }

    override fun onClickFirstMsgBrowseJobs(message: FirstMsgBrowseJobsMessage) {

    }

    override fun sendQuickReply(item: QuickReplyTemplateItem, index: Int) {

    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {

    }

    override fun onInputStateChanged(intent: ChatInputIntent) {

    }
}