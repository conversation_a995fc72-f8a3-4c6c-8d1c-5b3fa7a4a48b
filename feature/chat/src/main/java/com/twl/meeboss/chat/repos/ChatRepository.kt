package com.twl.meeboss.chat.repos

import com.twl.meeboss.base.foundation.IRepository
import com.twl.meeboss.base.foundation.repo.BaseRepository
import com.twl.meeboss.base.model.PageList
import com.twl.meeboss.chat.api.ChatApi
import com.twl.meeboss.chat.api.resp.ReportReasonTypeResult
import com.twl.meeboss.chat.core.model.MessageFromNetResult
import com.twl.meeboss.chat.export.constant.ReportScene
import com.twl.meeboss.chat.module.common.file.model.FileDownloadInfo
import com.twl.meeboss.chat.module.phrase.model.ChatPhrasesExampleResult
import com.twl.meeboss.chat.module.phrase.model.ChatPhrasesResult
import com.twl.meeboss.chat.module.notification.model.SystemNotificationItemResult
import javax.inject.Inject

/**
 * @author: 冯智健
 * @date: 2024年08月18日 13:38
 * @description:
 */
class ChatRepository @Inject constructor(
    private val chatApi: ChatApi
): BaseRepository(), IChatRepository {
    override suspend fun chatMessageList(startMsgId: Long, endMsgId: Long) = safeCallApi {
        chatApi.chatMessageList(startMsgId, endMsgId)
    }

    override suspend fun getReportReasonTypeList() = safeCallApi {
        chatApi.getReportReasonTypeList()
    }

    override suspend fun reportFriend(reasonType: Int, additionalInfo: String?, securityId: String, scene:@ReportScene Int) = safeCallApi {
        chatApi.reportFriend(reasonType, additionalInfo, securityId, scene)
    }

    override suspend fun getNotificationList(pageSize: Int, lastId: String?) = safeCallApi {
        chatApi.getNotificationList(pageSize, lastId)
    }

    override suspend fun notificationUpdateRead(notificationIds: String?) = safeCallApi {
        chatApi.notificationUpdateRead(notificationIds)
    }

    override suspend fun getCommonPhrasesExample(): Result<ChatPhrasesExampleResult> = safeCallApi {
        chatApi.getCommonPhrasesExample()
    }

    override suspend fun getCommonPhrases(): Result<ChatPhrasesResult> = safeCallApi {
        chatApi.getCommonPhrases()
    }

    override suspend fun saveCommonPhrases(id: String?, text: String): Result<Any> = safeCallApi {
        chatApi.saveCommonPhrases(id, text)
    }

    override suspend fun deleteCommonPhrases(id: String): Result<Any> = safeCallApi {
        chatApi.deleteCommonPhrases(id)
    }

    override suspend fun sortCommonPhrases(ids: String): Result<Any> = safeCallApi {
        chatApi.sortCommonPhrases(ids)
    }
    override suspend fun getUrlWithExpire(key: String) = safeCallApi {
        chatApi.getUrlWithExpire(key)
    }
}

interface IChatRepository: IRepository {
    suspend fun chatMessageList(startMsgId: Long, endMsgId: Long): Result<MessageFromNetResult>

    suspend fun getReportReasonTypeList(): Result<ReportReasonTypeResult>

    suspend fun reportFriend(reasonType: Int, additionalInfo: String?, securityId: String, scene:@ReportScene Int): Result<Any>

    suspend fun getNotificationList(pageSize: Int, lastId: String?): Result<PageList<SystemNotificationItemResult>>

    suspend fun notificationUpdateRead(notificationIds: String?): Result<Any>

    suspend fun getCommonPhrasesExample(): Result<ChatPhrasesExampleResult>
    suspend fun getCommonPhrases(): Result<ChatPhrasesResult>
    suspend fun saveCommonPhrases(id: String? = null, text: String): Result<Any>
    suspend fun deleteCommonPhrases(id: String): Result<Any>
    suspend fun sortCommonPhrases(ids: String): Result<Any>
    suspend fun getUrlWithExpire(key: String):Result<FileDownloadInfo>
}