package com.twl.meeboss.chat.core.model.message.custom

import cn.techwolf.international.washington.chat.protocol.WashingtonProtocol
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.export.constant.LocalMessageType
import com.twl.meeboss.core.ui.utils.toResourceString
import org.json.JSONObject

/**
 * 交换邮箱结果
 */
class ExchangeEmailResultMessage : ChatMessage() {

    init {
        localMessageType = LocalMessageType.EXCHANGE_EMAIL_RESULT
    }

    override fun parseMessage(pbMessage: WashingtonProtocol.GeneralMessage) {
        try {
            val json = JSONObject(pbMessage.body.custom.content)
            content = json.optString("email")
        }catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun parseFromDatabase() {

    }

    override fun prepareForDatabase() {

    }

    override fun getSummary(): String = R.string.common_phone_number.toResourceString()
}