package com.twl.meeboss.chat.module.common.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.Gravity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.chat.R
import com.twl.meeboss.common.ktx.cap
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.component.button.XDialogOutlineButton
import com.twl.meeboss.core.ui.fragment.BaseComposeDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.toResourceString

class ConfirmSendContactDialog : BaseComposeDialogFragment() {

    val title: String by lazy {
        arguments?.getString("title") ?: ""
    }
    val content: String? by lazy {
        arguments?.getString("content")
    }
    val icon: Int by lazy {
        arguments?.getInt("icon") ?: R.drawable.chat_icon_exchange_email
    }
    val contentTitle: String by lazy {
        arguments?.getString("contentTitle") ?: ""
    }
    val contentDesc: String by lazy {
        arguments?.getString("contentDesc") ?: ""
    }
    val canEdit: Boolean by lazy {
        arguments?.getBoolean("canEdit") ?: true
    }
    val confirmText: String by lazy {
        arguments?.getString("confirmText") ?: com.twl.meeboss.core.ui.R.string.common_button_confirm.toResourceString()
    }
    val cancelText: String by lazy {
        arguments?.getString("cancelText") ?: com.twl.meeboss.core.ui.R.string.common_button_cancel.toResourceString()
    }

    private var onCancel: (() -> Unit)? = null
    private var onConfirm: (() -> Unit)? = null
    private var onEditClick: (() -> Unit)? = null

    companion object {
        fun newInstance(
            title: String,
            content: String?,
            icon: Int,
            contentTitle: String,
            contentDesc: String,
            canEdit: Boolean = true,
            onEditClick: () -> Unit,
            confirmText: String = com.twl.meeboss.core.ui.R.string.common_button_confirm.toResourceString(),
            onConfirm: () -> Unit = {},
            cancelText: String = com.twl.meeboss.core.ui.R.string.common_button_cancel.toResourceString(),
            onCancel: () -> Unit = {}
        ): ConfirmSendContactDialog {
            return ConfirmSendContactDialog().apply {
                this.onConfirm = onConfirm
                this.onEditClick = onEditClick
                this.onCancel = onCancel
                arguments = Bundle().apply {
                    putString("title", title)
                    putString("content", content)
                    putInt("icon", icon)
                    putString("contentTitle", contentTitle)
                    putString("contentDesc", contentDesc)
                    putBoolean("canEdit", canEdit)
                    putString("confirmText", confirmText)
                    putString("cancelText", cancelText)
                }
            }
        }
    }
    override fun setDialogAttributes(dialog: Dialog) {
        dialog.run {
            dialog.window?.attributes?.gravity = Gravity.CENTER
        }
        dialog.setCancelable(true)
        dialog.setCanceledOnTouchOutside(true)
    }

    @Composable
    override fun ComposeComponent() {
        if (contentTitle.isEmpty() || title.isEmpty()) {
            return dismissSafely()
        }
        ConfirmSendContactDialogContent(title = title, content = content,
            icon = icon,
            contentTitle = contentTitle,
            contentDesc = contentDesc,
            canEdit = canEdit,
            onEditClick = {
                dismissSafely()
                onEditClick?.invoke()
            },
            confirmText = confirmText,
            onConfirm = {
                dismissSafely()
                onConfirm?.invoke()
            },
            cancelText = cancelText,
            onCancel = {
                dismissSafely()
                onCancel?.invoke()

            })
    }
}

@Composable
private fun ConfirmSendContactDialogContent(
    title: String = "",
    content: String? = "",
    icon: Int = R.drawable.chat_icon_exchange_email,
    canEdit: Boolean = true,
    contentTitle: String = "Email",
    contentDesc: String = "",
    onEditClick: () -> Unit = {},
    confirmText: String = "",
    onConfirm: () -> Unit = {},
    cancelText: String = "",
    onCancel: () -> Unit = {}
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(Color.White)
    ) {
        Column(modifier = Modifier.padding(16.dp, 20.dp)) {
            if (title.isNotBlank()) {
                Text(
                    text = title,
                    fontWeight = FontWeight.SemiBold,
                    color = Black222222,
                    fontSize = 18.sp
                )
            }
            if (!content.isNullOrBlank()) {
                Text(
                    text = content,
                    modifier = Modifier.padding(top = 8.dp),
                    fontWeight = FontWeight.Normal,
                    fontSize = 14.sp
                )
            }
            Row(modifier = Modifier.padding(top = 20.dp)) {
                Icon(
                    painter = painterResource(id = icon),
                    tint = Color.Unspecified,
                    contentDescription = ""
                )
                Column(
                    modifier = Modifier
                        .weight(1F)
                        .padding(start = 12.dp)
                ) {
                    Text(
                        text = contentTitle,
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Black888888,
                        )
                    )
                    Text(
                        modifier = Modifier.padding(top = 5.dp),
                        text = contentDesc,
                        style = TextStyle(
                            fontSize = 15.sp,
                            fontWeight = FontWeight.Medium,
                            color = Black222222,
                        )
                    )
                }

                if (canEdit) {
                    Text(
                        modifier = Modifier.noRippleClickable { onEditClick() },
                        text = stringResource(id = R.string.common_button_edit).cap(),
                        style = TextStyle(
                            fontSize = 13.sp,
                            lineHeight = 20.sp,
                            fontWeight = FontWeight.Medium,
                            color = Black222222,
                            textDecoration = TextDecoration.Underline,
                        )
                    )
                }

            }
            Row(modifier = Modifier.padding(top = 20.dp)) {
                if (cancelText.isNotBlank()) {
                    XDialogOutlineButton(
                        text = cancelText,
                        modifier = Modifier.weight(1f),
                        onClick = {
                            onCancel()
                        }
                    )
                    Spacer(modifier = Modifier.width(11.dp))
                }
                XCommonButton(text = confirmText,
                    modifier = Modifier.weight(1f),
                    onClick = {
                        onConfirm()
                    })

            }
        }

    }
}

@Preview
@Composable
private fun PreviewDialog() {
    ConfirmSendContactDialogContent(
        title = "This is a title",
        contentTitle = "Phone",
        contentDesc = "123456789",
        confirmText = "Confirm",
        cancelText = "Cancel",
        canEdit = false,
        content = stringResource(id = com.twl.meeboss.core.ui.R.string.ui_common_long_place_holder)
    )
}



