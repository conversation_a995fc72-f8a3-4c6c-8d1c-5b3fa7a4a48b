package com.twl.meeboss.chat.module.common.components.chatitems

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.ClickableText
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.ktx.getHighlightString
import com.twl.meeboss.base.model.Highlight
import com.twl.meeboss.base.protocol.ProtocolHelper
import com.twl.meeboss.chat.core.model.MessageModel
import com.twl.meeboss.chat.core.model.convert.ChatHighlightContent
import com.twl.meeboss.chat.core.model.message.custom.InterestGrayHintMessage
import com.twl.meeboss.core.ui.theme.Black888888

@Composable
fun InterestGrayHintCard(modifier: Modifier = Modifier, model: MessageModel.InterestGrayHint) {
    Column(modifier = modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        ClickableText(text = model.message.interestGrayHintBean?.content.getHighlightString(model.message.interestGrayHintBean?.hrefs),
            style = TextStyle(
                fontSize = 12.sp,
                lineHeight = 20.sp,
                fontWeight = FontWeight.Normal,
                color = Black888888,
                textAlign = TextAlign.Center,
            )) { offset ->
            model.message.interestGrayHintBean?.hrefs?.forEach {
                if (offset in it.startIdx until it.endIdx) {
                    ProtocolHelper.parseProtocol(it.url)
                }
            }
        }
    }
}

@Preview
@Composable
fun PreViewInterestGrayHintCard() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = Color.White)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        InterestGrayHintCard(model = MessageModel.InterestGrayHint(InterestGrayHintMessage().apply {
            interestGrayHintBean = ChatHighlightContent(
                content = "To be, or not to be, that is the question",
                hrefs = listOf(
                    Highlight(startIdx = 1, endIdx = 7, url = ""),
                    Highlight(startIdx = 10, endIdx = 14, url = ""),
                    Highlight(startIdx = 15, endIdx = 21, url = "")
                ))
        }))

    }
}