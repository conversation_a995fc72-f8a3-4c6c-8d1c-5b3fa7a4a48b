package com.twl.meeboss.chat.module.geek.activity.module

import androidx.fragment.app.FragmentActivity
import com.twl.meeboss.base.components.dialog.chain.DialogChainManager
import com.twl.meeboss.chat.module.geek.activity.intent.GeekChatUiIntent
import com.twl.meeboss.chat.module.geek.bottomsheet.strategy.GeekConnectBottomSheetStrategy
import com.twl.meeboss.chat.module.geek.bottomsheet.strategy.GeekCoverGuidanceStrategy
import com.twl.meeboss.chat.module.geek.bottomsheet.strategy.GeekSendGreetingBottomSheetStrategy
import com.twl.meeboss.chat.module.geek.viewmodel.GeekChatViewModel

class GeekGreetingModule(val activity: FragmentActivity, val viewModel: GeekChatViewModel) {

    fun init() {
        //欢迎弹框，挨个显示
        DialogChainManager(activity).apply {
            this.add(GeekConnectBottomSheetStrategy(activity,viewModel))
            this.add(GeekSendGreetingBottomSheetStrategy(activity, viewModel.params, viewModel))
            this.add(GeekCoverGuidanceStrategy(activity = activity,viewModel = viewModel))
            process()
        }

        viewModel.showGreetingResult.observe(activity){
            if(it){
                GeekSendGreetingBottomSheetStrategy(activity, viewModel.params, viewModel).showDialog()
            }
        }
    }

    fun handleInteractInterest(mid: Long){
        viewModel.sendUiIntent(GeekChatUiIntent.Greeting.InteractInterest(mid))
    }


}