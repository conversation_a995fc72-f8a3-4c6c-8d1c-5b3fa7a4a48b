package com.twl.meeboss.chat.module.boss.viewmodel

import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.chat.export.model.ChatTemplateBean
import com.twl.meeboss.chat.export.model.ChatTemplateResult
import com.twl.meeboss.chat.repos.ChatBossRepository
import com.twl.meeboss.chat.utils.ChatPointReporter
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.T
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class BossSendGreetingViewModel @Inject constructor(
    private val repos: ChatBossRepository
) : BaseMviViewModel<BossSendGreetingUiState, BossSendGreetingUiIntent>() {

    private var template: ChatTemplateBean = ChatTemplateBean()

    var friendId: String = ""

    var source:Int = 1

    var jobId: String = ""

    val sendResult: MutableLiveData<Boolean> = MutableLiveData()

    override fun initUiState(): BossSendGreetingUiState = BossSendGreetingUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossSendGreetingUiIntent.Send -> {
                saveGreetingText()
                sendGreetingText()
            }
            is BossSendGreetingUiIntent.CheckChanged -> {
                sendUiState {
                    copy(isCheck = intent.isCheck)
                }
            }
            is BossSendGreetingUiIntent.Select -> {
                template = intent.template
            }
            else -> {

            }
        }
    }

    fun greetingTemplates() {
        requestData(
            enableLoadState = true,
            request = {
                repos.getGreetingTemplates(friendId, jobId)
            },
            success = {
                it?.let { result ->
                    if ((result.selectedTemplateId ?: 0) > 0) {
                        template = result.systemTemplates?.find {
                            item -> item.templateId == result.selectedTemplateId
                        } ?: ChatTemplateBean()
                    } else {
                        if (!result.systemTemplates.isNullOrEmpty()) {
                            template = result.systemTemplates?.first() ?: ChatTemplateBean()
                        }
                    }
                    sendUiState {
                        copy(list = result.systemTemplates ?: emptyList(), result = result)
                    }
                }
            },
            fail = {
                XLog.error(TAG, it.message)
            }
        )
    }

    private fun sendGreetingText() {
        requestData(
            request = {
                repos.sendGreetingText(template.templateId ?: 0, "", friendId, jobId)
            },
            success = {
                it?.let { _ ->
                    sendResult.postValue(true)
                }
            },
            fail = {
                T.ss(it.message)
                XLog.error(TAG, it.message)
            }
        )
        ChatPointReporter.greetingGuideSend(source,uiStateFlow.value.isCheck,uiStateFlow.value.list.indexOf(template))
    }

    private fun saveGreetingText() {
        val isCheck = uiStateFlow.value.isCheck
        if (isCheck) {
            requestData(
                request = {
                    repos.saveGreetingText(template.templateId ?: 0, "", true)
                },
                success = {

                },
                fail = {
                    XLog.error(TAG, it.message)
                }
            )
        }
    }
}

data class BossSendGreetingUiState(
    val isCheck: Boolean = true,
    val list: List<ChatTemplateBean> = emptyList(),
    val result: ChatTemplateResult = ChatTemplateResult(),
) : IUiState

sealed class BossSendGreetingUiIntent : IUiIntent {
    data object Send : BossSendGreetingUiIntent()
    data class CheckChanged(val isCheck: Boolean) : BossSendGreetingUiIntent()
    data class Select(val template: ChatTemplateBean) : BossSendGreetingUiIntent()
}