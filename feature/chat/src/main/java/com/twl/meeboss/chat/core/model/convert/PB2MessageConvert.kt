package com.twl.meeboss.chat.core.model.convert

import cn.techwolf.international.washington.chat.protocol.WashingtonProtocol
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.chat.core.constant.CustomBodyType
import com.twl.meeboss.chat.export.constant.GeneralMsgType
import com.twl.meeboss.chat.export.constant.LocalMessageType
import com.twl.meeboss.chat.core.model.message.ActionMessage
import com.twl.meeboss.chat.core.model.message.AudioMessage
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.EmptyMessage
import com.twl.meeboss.chat.core.model.message.FileMessage
import com.twl.meeboss.chat.core.model.message.HintMessage
import com.twl.meeboss.chat.core.model.message.NoSupportMessage
import com.twl.meeboss.chat.core.model.message.PictureMessage
import com.twl.meeboss.chat.core.model.message.TextMessage
import com.twl.meeboss.chat.core.model.message.custom.ChatGreetingJobMessage
import com.twl.meeboss.chat.core.model.message.custom.ChatGreetingResumeMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangeEmailRequestMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangeEmailResultMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangePhoneRequestMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangePhoneResultMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangeResumeRequestMessage
import com.twl.meeboss.chat.core.model.message.custom.ExchangeResumeResultMessage
import com.twl.meeboss.chat.core.model.message.custom.FirstMsgBrowseCandidatesMessage
import com.twl.meeboss.chat.core.model.message.custom.FirstMsgBrowseJobsMessage
import com.twl.meeboss.chat.core.model.message.custom.FirstMsgPostJobMessage
import com.twl.meeboss.chat.core.model.message.custom.InterestGrayHintMessage
import com.twl.meeboss.chat.core.model.message.custom.SendResumeRequestMessage

/**
 * 从PB解析出数据，搞成本地的可以显示的消息
 */
object PB2MessageConvert {
    private val tag = this::class.java.simpleName
    fun parsePbMessage(pbMessage: WashingtonProtocol.GeneralMessage): ChatMessage {
        val message = when (pbMessage.bodyType) {
            GeneralMsgType.MSG_EMPTY -> {
                EmptyMessage()
            }

            GeneralMsgType.MSG_TEXT -> {
                TextMessage()
            }

            GeneralMsgType.MSG_HINT -> {
                HintMessage()
            }

            GeneralMsgType.MSG_PIC -> {
                PictureMessage()
            }

            GeneralMsgType.MSG_FILE -> {
                FileMessage()
            }

            GeneralMsgType.MSG_AUDIO -> {
                AudioMessage()
            }

            GeneralMsgType.MSG_ACTION -> {
                ActionMessage()
            }

            GeneralMsgType.MSG_CUSTOM -> { //自定义消息，需要二次解析
                parseCustomPbMessage(pbMessage.body?.custom)
            }

            else -> {
                NoSupportMessage()
            }

        }
        message.parseFromProtobuf(pbMessage)
        TLog.debug(tag,"parsePbMessage ${message}")
        return message
    }


    /**
     * 解析自定义卡片
     */
    private fun parseCustomPbMessage(customMessage: WashingtonProtocol.Custom?): ChatMessage {
        return when (customMessage?.type) {
            CustomBodyType.GEEK_GREETING_JOB_IN_GEEK -> {
                ChatGreetingJobMessage().also {
                    it.localMessageType = LocalMessageType.GEEK_GREETING_JOB_IN_GEEK
                }
            }

            CustomBodyType.GEEK_GREETING_RESUME_IN_BOSS -> {
                ChatGreetingResumeMessage().also {
                    it.localMessageType = LocalMessageType.GEEK_GREETING_RESUME_IN_BOSS
                }
            }

            CustomBodyType.BOSS_GREETING_JOB_IN_GEEK -> {
                ChatGreetingJobMessage().also {
                    it.localMessageType = LocalMessageType.BOSS_GREETING_JOB_IN_GEEK
                }
            }

            CustomBodyType.BOSS_GREETING_RESUME_IN_BOSS -> {
                ChatGreetingResumeMessage().also {
                    it.localMessageType = LocalMessageType.BOSS_GREETING_RESUME_IN_BOSS
                }
            }
            CustomBodyType.INTEREST_GRAY_HINT->{//感兴趣灰色提示
                InterestGrayHintMessage()
            }
            CustomBodyType.EXCHANGE_REQUEST_EMAIL->{//请求交换邮箱
                ExchangeEmailRequestMessage()
            }
            CustomBodyType.EXCHANGE_REQUEST_PHONE->{//请求交换电话
                ExchangePhoneRequestMessage()
            }
            CustomBodyType.EXCHANGE_REQUEST_RESUME->{//请求交换简历
                ExchangeResumeRequestMessage()
            }
            CustomBodyType.EXCHANGE_SEND_RESUME_REQUEST->{//发送交换简历请求
                SendResumeRequestMessage()
            }
            CustomBodyType.EXCHANGE_EMAIL_RESULT->{//交换邮箱结果
                ExchangeEmailResultMessage()
            }
            CustomBodyType.EXCHANGE_PHONE_RESULT->{//交换电话结果
                ExchangePhoneResultMessage()
            }
            CustomBodyType.EXCHANGE_RESUME_RESULT->{//交换简历结果
                ExchangeResumeResultMessage()
            }
            CustomBodyType.FIRST_MESSAGE_POST_JOB->{
                FirstMsgPostJobMessage()
            }
            CustomBodyType.FIRST_MESSAGE_BROWSE_JOBS->{
                FirstMsgBrowseJobsMessage()
            }
            CustomBodyType.FIRST_MESSAGE_BROWSE_CANDIDATES->{
                FirstMsgBrowseCandidatesMessage()
            }
            else -> {
                NoSupportMessage()
            }

        }
    }
}