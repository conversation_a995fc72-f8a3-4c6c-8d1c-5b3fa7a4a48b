package com.twl.meeboss.chat.module.boss.activity.module

import androidx.fragment.app.FragmentActivity
import com.twl.meeboss.base.components.dialog.chain.DialogChainManager
import com.twl.meeboss.chat.module.boss.bottomsheet.strategy.BossConnectBottomSheetStrategy
import com.twl.meeboss.chat.module.boss.bottomsheet.strategy.BossCoverGuidanceStrategy
import com.twl.meeboss.chat.module.boss.bottomsheet.strategy.BossSendGreetingBottomSheetStrategy
import com.twl.meeboss.chat.module.boss.viewmodel.BossChatViewModel

class BossGreetingModule(val activity: FragmentActivity, val viewModel: BossChatViewModel) {

    fun init() {
        //欢迎弹框，挨个显示
        DialogChainManager(activity).apply {
            this.add(BossConnectBottomSheetStrategy(activity,viewModel))
            this.add(BossSendGreetingBottomSheetStrategy(activity, viewModel.params, viewModel))
            this.add(BossCoverGuidanceStrategy(activity,  viewModel))
            process()
        }
    }



}