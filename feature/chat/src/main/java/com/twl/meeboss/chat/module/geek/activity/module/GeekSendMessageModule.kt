package com.twl.meeboss.chat.module.geek.activity.module

import android.content.Intent
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.viewModelScope
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.FileMessage
import com.twl.meeboss.chat.core.model.message.PictureMessage
import com.twl.meeboss.chat.module.common.ImageMessagePreview
import com.twl.meeboss.chat.module.common.components.input.ChatInputIntent
import com.twl.meeboss.chat.module.geek.activity.intent.GeekChatUiIntent
import com.twl.meeboss.chat.module.geek.viewmodel.GeekChatViewModel
import com.twl.meeboss.chat.module.phrase.dialog.ChatPhrasesBottomSheet
import com.twl.meeboss.core.ui.utils.showSafely

/**
 * 发送消息模块，包括发送和预览图片
 */
class GeekSendMessageModule(val activity: FragmentActivity, val viewModel: GeekChatViewModel) {
    private val tag = "SendMessageModule"

    private val chatImageMaxSize = 10 * 1024 * 1024

    fun sendRead() {
        viewModel.sendUiIntent(GeekChatUiIntent.BaseMessage.SendRead)
    }

    fun sendTextMessage(text: CharSequence) {
        //发送文本消息
        viewModel.sendUiIntent(GeekChatUiIntent.BaseMessage.SendText(text))
    }

    fun sendPhraseMessage() {
        ChatPhrasesBottomSheet.newInstance(onClickSend = {
            viewModel.sendUiIntent(GeekChatUiIntent.BaseMessage.SendText(it.text))
        }, onClickItem = {
            viewModel.sendUiIntent(GeekChatUiIntent.GeekInputIntent(ChatInputIntent.AppendText(it.text)))
        }).showSafely(activity)
    }

    //处理图片选择结果
    fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    }

    fun previewImageMessage(message: ChatMessage) {
        if (message is PictureMessage) {
            ImageMessagePreview(viewModel.viewModelScope, activity).preview(message)
        }
    }

    fun resendMessage(message: ChatMessage) {
        viewModel.sendUiIntent(GeekChatUiIntent.BaseMessage.ResendMessage(message))
    }

}