package com.twl.meeboss.chat.repos

import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.chat.core.db.dao.MessageDao
import com.twl.meeboss.chat.core.model.convert.MessageUtils
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.MessageRecord
import com.twl.meeboss.chat.export.constant.LocalMessageType
import com.twl.meeboss.chat.export.constant.MessageStatus

class ChatMessageRepository(val mMessageDao: MessageDao) {
    private val tag = "MessageRepository"


    fun onReceive(chatMessage: ChatMessage): Boolean {
        var isExist = false
        var record: MessageRecord? = mMessageDao.query(chatMessage.mid)
        if (record != null) {
            isExist = true
            chatMessage.id = record.id
        }else if(chatMessage.cmid >0){
            record = mMessageDao.queryByCMid(chatMessage.cmid, chatId = chatMessage.chatId)
            if (record != null) {
                isExist = true
                chatMessage.id = record.id
            }
        }
        if (record != null) {//关键值相同，说明相同，否则也更新
            isExist = MessageUtils.messageSameToLocal(chatMessage, record)
        }
        insertMessage(chatMessage)
        TLog.debug(tag, "onReceive ${chatMessage}")
        return isExist
    }

    /**
     * 计算当前消息时间，是否大于时间间隔，要在顶部展示时间条
     */
//    private fun updateShowTime(message: ChatMessage) {
//        message.isShowTime = mMessageDao.queryRecentCount(
//            message.chatId,
//            message.mid,
//            message.cmid,
//            message.addTime - showTimeInterval
//        ) <= 0
//    }

    fun updateMessageStatus(id: Long, @MessageStatus status: Int) {
        mMessageDao.updateMessageStatus(id, status)
    }


    fun updateMessage(message: ChatMessage): Long {
        message.prepareForDatabase()
        return mMessageDao.insertMessage(message)
    }

    fun insertMessage(message: ChatMessage): Long {
        message.prepareForDatabase()
        return mMessageDao.insertMessage(message)
    }

    fun getMaxRecID(friendId: String): Long {
        return mMessageDao.getMaxRecID(friendId)
    }

    fun getMaxMessageId(): Long {
        return mMessageDao.getMaxMessageId()
    }

    suspend fun updateMessageToRead(friendId: String, chatId: String, maxMid: Long) {
        mMessageDao.updateMessageToRead(friendId, chatId, maxMid)
    }

    fun deleteMessage(chatMessage: ChatMessage) {
        mMessageDao.deleteMessage(chatMessage)
    }

    fun queryAllSendingMessage(): List<MessageRecord> {
        return mMessageDao.getAllMessagesByStatus(MessageStatus.SENDING)
    }

    fun deleteAllMessages(friendId: String){
        mMessageDao.deleteAllMessages(friendId)
    }

    fun queryMessageByType(chatId: String,@LocalMessageType type:Int):List<MessageRecord>{
        return mMessageDao.queryMessagesByType(chatId,type)
    }
}