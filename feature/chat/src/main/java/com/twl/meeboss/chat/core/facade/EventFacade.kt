package com.twl.meeboss.chat.core.facade

import android.os.Bundle
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.sendStringLiveEvent
import com.twl.meeboss.base.model.common.FORCE_LOGOUT_DATA
import com.twl.meeboss.base.model.common.ForceLogoutData
import com.twl.meeboss.base.mudule.ModuleManager
import com.twl.meeboss.boss.export.BossEventBusKey
import com.twl.meeboss.chat.core.constant.EventType
import com.twl.meeboss.chat.core.model.message.EventMessage
import com.twl.meeboss.chat.module.notification.manager.SystemNotificationManager
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.common.utils.T
import org.json.JSONObject

class EventFacade {

    fun onEvent(event: EventMessage) {
        when (event.type) {
            EventType.UPDATE_CONVERSATION -> {
                //更新联系人
                FacadeManager.getInstance().conversationFacade.updateConversations()
            }
            //同步消息，PC聊天消息同步
            EventType.SYNC_MESSAGES -> {
                FacadeManager.getInstance().messagePatchFacade.autoSyncPatch()
            }

            EventType.SYNC_NOTIFICATION_COUNT -> {
                SystemNotificationManager.updateNotificationCount(event.content)
            }

            EventType.SYNC_JOBS -> {
                sendStringLiveEvent(BossEventBusKey.UPDATE_JOB_LIST,"")
                ModuleManager.updateUserInfo()
            }

            EventType.SYNC_BOSS_USER_INFO -> {
                ModuleManager.updateUserInfo()
            }

            EventType.TOAST -> {
                if(event.content.isBlank()){
                    return
                }
                try {
                    JSONObject(event.content).run {
                        val message = optString("msg")
//                        val code = optInt("code")
                        if(!message.isNullOrBlank()){
                            T.ss(message)
                        }
                    }
                }catch (e:Exception){
                    e.printStackTrace()
                }
            }

            EventType.FORCE_LOGOUT -> {
                try {
                    val data = GsonUtils.fromJson(event.content, ForceLogoutData::class.java)
                    if (data != null) {
                        val bundle = Bundle()
                        bundle.putSerializable(FORCE_LOGOUT_DATA, data)
                        AccountManager.logout(bundle)
                    } else {
                        AccountManager.logout()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            EventType.EMAIL_VERIFIED->{
                sendStringLiveEvent(EventBusKey.EMAIL_VERIFIED_SUCCESS,"")
            }

            EventType.UPDATE_USER_INFO -> {
                ModuleManager.updateUserInfo()
            }

            EventType.TOP_MATCH_UNREAD -> {
                sendStringLiveEvent(EventBusKey.TOP_MATCHES_READ_DOT_SYNC,"")
            }

            EventType.SYNC_QUICK_REPLIES -> {
                sendStringLiveEvent(EventBusKey.SYNC_QUICK_REPLIES,"")
            }
        }

    }
}