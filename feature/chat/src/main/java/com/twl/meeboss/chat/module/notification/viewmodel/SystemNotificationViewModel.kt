package com.twl.meeboss.chat.module.notification.viewmodel

import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.ktx.getDefaultPageSize
import com.twl.meeboss.chat.module.notification.model.SystemNotificationItemResult
import com.twl.meeboss.chat.repos.ChatRepository
import com.twl.meeboss.common.ktx.toJson
import com.twl.meeboss.common.utils.T
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class NotificationViewModel @Inject constructor(
    private val repos: ChatRepository
) : BaseMviViewModel<NotificationUiState, NotificationUiIntent>() {

    override fun initUiState(): NotificationUiState = NotificationUiState(listState = XRefreshListState.getDefault())


    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is NotificationUiIntent.GetNotificationList -> {
                getNotificationList(intent.isRefresh)
            }

            is NotificationUiIntent.UpdateReadStatus -> {
                updateReadStatus(intent.item)
            }

            else -> {
            }
        }
    }

    private fun getNotificationList(isRefresh: Boolean) {
        requestData(
            enableLoadState = false,
            request = {

                repos.getNotificationList(getDefaultPageSize(), if(isRefresh) "" else uiStateFlow.value.lastId)
            },
            success = {
                it?.let { pageList ->
                    notificationUpdateRead((pageList.content?.map { notificationItem-> notificationItem.notificationId }
                        ?: listOf()).toJson())
                    sendUiState {
                        copy(
                            lastId = pageList.lastId ?: "",
                            listState = if (isRefresh) {
                                listState.refreshSuccess(
                                    list = pageList.content ?: listOf(),
                                    hasMore = pageList.hasMore ?: false
                                )
                            } else {
                                listState.loadMoreSuccess(
                                    list = pageList.content ?: listOf(),
                                    hasMore = pageList.hasMore ?: false
                                )
                            }
                        )
                    }
                }
            },
            fail = {
                T.ss(it.message)
                sendUiState {
                    copy(
                        listState = if (isRefresh) {
                            listState.refreshFail(it.message)
                        } else {
                            listState.loadMoreFail()
                        }
                    )
                }
            }
        )

    }

    private fun notificationUpdateRead(notificationIds: String?) {
        requestData(
            enableLoadState = false,
            request = {
                repos.notificationUpdateRead(notificationIds)
            }
        )
    }

    private fun updateReadStatus(readItem: SystemNotificationItemResult) {
        var hasMatchItem = false
        val originList = uiStateFlow.value.listState.list.map { item ->
            if (item.notificationId == readItem.notificationId) {
                hasMatchItem = true
                item.copy(
                    status = 1
                )
            } else {
                item
            }
        }

        if (hasMatchItem) {
            sendUiState {
                copy(
                    listState = listState.refreshData(originList)
                )
            }
        }
    }

}

data class NotificationUiState(
    val lastId: String = "",
    val listState: XRefreshListState<SystemNotificationItemResult>,
) : IUiState

sealed class NotificationUiIntent : IUiIntent {
    data class GetNotificationList(val isRefresh: Boolean) : NotificationUiIntent()

    data class UpdateReadStatus(val item: SystemNotificationItemResult) : NotificationUiIntent()
}