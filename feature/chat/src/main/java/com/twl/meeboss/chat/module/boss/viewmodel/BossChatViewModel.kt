package com.twl.meeboss.chat.module.boss.viewmodel

import android.content.Context
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.util.Log
import androidx.core.text.toSpannable
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.repo.toastErrorIfPresent
import com.twl.meeboss.base.ktx.buildFullNameOrUnknown
import com.twl.meeboss.base.model.ResumeUrlBean
import com.twl.meeboss.base.usecase.ImageUploadUseCase
import com.twl.meeboss.boss.export.BossServiceRouter
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.api.resp.QuickReplyTemplateItem
import com.twl.meeboss.chat.core.facade.FacadeManager
import com.twl.meeboss.chat.core.uitls.StickerHelper
import com.twl.meeboss.chat.export.constant.isFriendStatusCanExchange
import com.twl.meeboss.chat.export.model.BossChatParams
import com.twl.meeboss.chat.export.model.ChatCheckEmailResult
import com.twl.meeboss.chat.export.model.ChatCheckPhoneResult
import com.twl.meeboss.chat.export.model.Conversation
import com.twl.meeboss.chat.module.boss.intent.BossChatUiIntent
import com.twl.meeboss.chat.module.boss.intent.BossExchangeIntentHandler
import com.twl.meeboss.chat.module.boss.intent.BossMessageIntentHandler
import com.twl.meeboss.chat.module.boss.state.BossChatUIState
import com.twl.meeboss.chat.module.boss.state.BossChatUiEvent
import com.twl.meeboss.chat.module.common.components.input.ChatInputIntent
import com.twl.meeboss.chat.module.common.components.input.ChatInputUiState
import com.twl.meeboss.chat.module.common.components.input.InputUtils
import com.twl.meeboss.chat.module.common.conversation.getUserInfoInConversation
import com.twl.meeboss.chat.module.common.model.ChatTopAction
import com.twl.meeboss.chat.module.common.model.CheckContactResult
import com.twl.meeboss.chat.module.common.uistate.ChatUIState
import com.twl.meeboss.chat.module.common.viewmodel.BaseChatViewModel
import com.twl.meeboss.chat.repos.ChatBossRepository
import com.twl.meeboss.chat.repos.ChatRepository
import com.twl.meeboss.common.exp.ApiException
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.provider.ContextProvider
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class BossChatViewModel @Inject constructor(
    private val repository: ChatBossRepository,
    private val uploadFilesUseCase: ImageUploadUseCase,
    private val chatRepository: ChatRepository
) : BaseChatViewModel<ChatUIState<BossChatUIState>, BossChatUiIntent>() {

    var params: BossChatParams = BossChatParams()

    val validateBeforeExchangeResult:MutableLiveData<ChatTopAction> = MutableLiveData()

    val checkEmailResult: MutableLiveData<CheckContactResult<ChatCheckEmailResult>> = MutableLiveData()

    val checkPhoneResult: MutableLiveData<CheckContactResult<ChatCheckPhoneResult>> = MutableLiveData()

    val previewResumeResult:MutableLiveData<ResumeUrlBean> = MutableLiveData()

    private val messageIntentHandler: BossMessageIntentHandler by lazy {
        BossMessageIntentHandler(chatId = mFriendId, friendIdentity = mFriendIdentity, scope = viewModelScope, userCase = uploadFilesUseCase, facade = mMessageFacade, interact = { handleIntent(it) })
    }

    private val exchangeIntentHandler: BossExchangeIntentHandler by lazy {
        BossExchangeIntentHandler(this, repository, chatRepository)
    }

    override fun initComplete(contact: Conversation) {
        //加载草稿
        if(contact.draft.isNotBlank()){
            sendUiState {
                copy(chatInputUiState = chatInputUiState.copy(inputText = StickerHelper.obtainEmotionSpannable(ContextProvider.getAppContext(),contact.draft)))
            }
        }
    }

    override fun onConversationChanged(conversation: Conversation) {
        sendUiState {
            copy(
                title = buildFullNameOrUnknown(firstName = conversation.firstName, lastName = conversation.lastName),
                subTitle = conversation.getUserInfoInConversation(),
                topActions = getBossTopAction(conversation.friendStatus.isFriendStatusCanExchange()),
                userStatus = conversation.userStatus,
                userStatusPrompt = conversation.userStatusPrompt.notNull(),
                isSystemUser = conversation.isSystemUser(),
                mState = BossChatUIState(
                    friendId = conversation.friendId,
                    securityId = conversation.securityId ?: "",
                    starred = conversation.starred
                )
        ) }
    }

    override fun initUiState(): ChatUIState<BossChatUIState> = ChatUIState<BossChatUIState>(
        mState = BossChatUIState(),
        topActions = getBossTopAction(),
        chatInputUiState = ChatInputUiState(keyboardHeight = InputUtils.getKeyboardHeight())
    )

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossChatUiIntent.BossInputIntent -> {
                handleChatInputIntent(intent.inputIntent)
            }
            is BossChatUiIntent.BaseMessage -> {
                messageIntentHandler.handleMessageIntent(intent)
            }

            is BossChatUiIntent.Exchange -> {
                exchangeIntentHandler.handleMessageIntent(intent)
            }
            is BossChatUiIntent.OnBackPressed -> {
                launcherOnIO {
                    mConversationFacade.saveDraft(uiStateFlow.value.chatInputUiState.inputText.toString(),mFriendId)
                }
            }
            is BossChatUiIntent.OnStartUser -> {
                val (friendId, securityId, currentStarred) = uiStateFlow.value.mState
                val starred = !currentStarred
                requestData(
                    request = {
                        BossServiceRouter.starCandidate(securityId, starred)
                    },
                    success = {
                        FacadeManager.getInstance().conversationFacade.starContact(friendId, starred)
                        sendUiState { copy(mState = mState.copy(starred = starred)) }
                        T.ss(if (starred) R.string.star_action_confirmation_message else R.string.unstar_action_confirmation_message)
                    },
                    fail = {
                        if (it is ApiException) {
                            T.ss(it.message)
                        }
                    }
                )
            }
            is BossChatUiIntent.OnQuickReplayClick -> {
                handleQuickReplay(intent)
            }

            is BossChatUiIntent.OnVisaSponsorSyncConfirm -> {
                syncJobVisaSponsorStatus(intent.status)
            }

            is BossChatUiIntent.OnQuickReplayStartSending -> {
                quickReplayCMids[intent.cmid] = intent.quickReply
            }

            is BossChatUiIntent.OnSyncQuickReply -> {
                syncQuickReplies()
            }
        }
    }

    private fun handleQuickReplay(intent: BossChatUiIntent.OnQuickReplayClick) {
        launcherOnIO {
            messageIntentHandler.handleMessageIntent(BossChatUiIntent.BaseMessage.SendQuickReply(intent.item))
            val quickReplay = intent.item
            when (quickReplay.scene) {
                QuickReplyTemplateItem.SCENT_EMPLOYER_CHAT_VISA_SPONSOR_ANSWER_YES -> {
                    _eventFlow.tryEmit(BossChatUiEvent.ShowVisaSponsorSyncDialog(1))
                }
                QuickReplyTemplateItem.SCENT_EMPLOYER_CHAT_VISA_SPONSOR_ANSWER_NO -> {
                    _eventFlow.tryEmit(BossChatUiEvent.ShowVisaSponsorSyncDialog( 2))
                }
            }
        }
    }



    private fun handleChatInputIntent(intent: ChatInputIntent) {
        when (intent) {
            is ChatInputIntent.ChatInput -> {
                if (intent.text.length > uiStateFlow.value.chatInputUiState.maxLength) {
                    T.ss(R.string.common_text_limit_exceeded)
                    val originInput = SpannableString(uiStateFlow.value.chatInputUiState.inputText)
                    sendUiState {
                        copy(chatInputUiState = chatInputUiState.copy(inputText = originInput))
                    }
                }else{
                    sendUiState {
                        copy(chatInputUiState = chatInputUiState.copy(inputText = SpannableString(intent.text)))
                    }
                }
            }

            is ChatInputIntent.ChangeInputType -> {
                Log.i("ChatUserInput", "inputType INTENT: ${intent.inputType}")
                sendUiState {

                    copy(chatInputUiState = chatInputUiState.copy(chatInputType = intent.inputType))
                }
            }

            is ChatInputIntent.ChangePanelShow -> {
                Log.i("ChatUserInput", "showPanelArea INTENT: ${intent.showPanelArea}")
                sendUiState {
                    copy(chatInputUiState = chatInputUiState.copy(showPanelArea = intent.showPanelArea))
                }
            }

            is ChatInputIntent.KeyboardHeightChanged -> {
                sendUiState {
                    copy(chatInputUiState = chatInputUiState.copy(keyboardHeight = intent.heightDp))
                }
            }

            is ChatInputIntent.ImeVisibleChanged -> {
                sendUiState {
                    copy(chatInputUiState = chatInputUiState.copy(imeVisible = intent.visible))
                }
            }

            is ChatInputIntent.AppendText->{
                val text = uiStateFlow.value.chatInputUiState.inputText
                if(text.length + intent.appendText.length > uiStateFlow.value.chatInputUiState.maxLength){
                    T.ss(R.string.common_text_limit_exceeded)
                    return
                }
                sendUiState {
                    val sp = SpannableStringBuilder(text).append(StickerHelper.obtainEmotionSpannable(ContextProvider.getTopActivity(),intent.appendText.toString()))
                    copy(chatInputUiState = chatInputUiState.copy(inputText = sp.toSpannable(), showKeyboard = true))
                }
            }
            is ChatInputIntent.ResetShowKeyboard -> {
                sendUiState {
                    copy(chatInputUiState = chatInputUiState.copy(showKeyboard = false))
                }
            }

            else -> {

            }
        }
    }

    private fun syncJobVisaSponsorStatus(status: Int) {
        launcherOnIO {
            val result = repository.updateJobVisaSponsored(conversation?.jobId ?: "", status)
            result.toastErrorIfPresent()
        }
    }


}

fun getBossTopAction(enable:Boolean = true, context: Context = ContextProvider.getContext()): List<ChatTopAction> {
    return listOf(
        ChatTopAction.BossRequestResume(
            text = R.string.chat_top_request_resume.toResourceString(context),
            iconRes = com.twl.meeboss.chat.R.drawable.chat_icon_resume,
            clickable = enable
        ),
        ChatTopAction.BossRequestPhone(
            text = R.string.chat_top_exchange_phone.toResourceString(context),
            iconRes = com.twl.meeboss.chat.R.drawable.chat_icon_phone_number,
            clickable = enable
        ),
        ChatTopAction.BossRequestEmail(
            text = R.string.chat_top_exchange_email.toResourceString(context),
            iconRes = com.twl.meeboss.chat.R.drawable.chat_icon_email,
            clickable = enable
        )
    )
}


