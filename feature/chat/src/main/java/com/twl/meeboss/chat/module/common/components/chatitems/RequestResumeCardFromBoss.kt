package com.twl.meeboss.chat.module.common.components.chatitems

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.MessageModel
import com.twl.meeboss.chat.core.model.message.bean.ContactRequestBean
import com.twl.meeboss.chat.core.model.message.custom.ExchangeResumeRequestMessage

/**
 * 来自Boss的交换简历请求
 */
@Composable
fun RequestResumeCardFromBoss(modifier: Modifier = Modifier,
                             model: MessageModel.ExchangeResumeRequestFromBoss,
                             onAcceptRequest: (ExchangeResumeRequestMessage) -> Unit = {},
                             onRejectRequest: (ExchangeResumeRequestMessage) -> Unit = {}) {
    val requestBody = model.message.contactRequestBean ?: return
    ChatOtherBox(modifier = modifier, model) {
        RequestCardBody(
            modifier = Modifier.padding(end = 16.dp),
            resource = ExchangeCardResource.Resume,
            text = stringResource(id = R.string.chat_request_for_resume),
            status = requestBody.status,
            onAcceptRequest = {
                onAcceptRequest(model.message)
            },
            onRejectRequest = {
                onRejectRequest(model.message)
            }
        )
    }
}

@Preview
@Composable
fun PreViewRequestResumeCardFromBoss() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = Color.White)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {

        RequestResumeCardFromBoss(model = MessageModel.ExchangeResumeRequestFromBoss(ExchangeResumeRequestMessage().apply {
            contactRequestBean = ContactRequestBean()
        }))
    }
}
