package com.twl.meeboss.chat.module.boss.intent

import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.model.common.FileItem
import com.twl.meeboss.chat.api.resp.QuickReplyTemplateItem
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.FileBody
import com.twl.meeboss.chat.module.common.components.input.ChatInputIntent
import com.twl.meeboss.chat.module.common.model.ChatTopAction

sealed class BossChatUiIntent : IUiIntent {

    //输入交互事件
    data class BossInputIntent(val inputIntent: ChatInputIntent) : BossChatUiIntent()

    //基本消息处理
    sealed class BaseMessage : BossChatUiIntent() {
        data object SendRead:BaseMessage()
        //发送文本
        data class SendText(val text: CharSequence) : BaseMessage()
        //发送图片
        data class SendImage(val image: FileItem) : BaseMessage()
        //重发消息
        data class ResendMessage(val message: ChatMessage) : BaseMessage()
        //发送快捷回复
        data class SendQuickReply(val quickReply: QuickReplyTemplateItem) : BaseMessage()
    }

    //交换信息相关
    sealed class Exchange() : BossChatUiIntent() {
        data class ValidateBeforeExchange(val action: ChatTopAction):Exchange()
        //从顶部点击检查邮箱是否认证
        data object CheckEmailFromTop : Exchange()
        //卡片内点击检查邮箱是否认证
        data class CheckEmailFromCard(val exchangeId: String, val isAccept: Boolean) : Exchange()
        //从顶部检查手机号是否认证
        data object CheckPhoneFromTop : Exchange()
        //卡片内点击检查手机号是否认证
        data class CheckPhoneFromCard(val exchangeId: String, val isAccept: Boolean) : Exchange()
        //交互简历请求
        data object ExchangeResumeRequest : Exchange()
        data class HandleGeekSendResumeRequest(val exchangeId: String,val isAccept: Boolean):Exchange()
        //交换手机号请求
        data object ExchangePhoneRequest : Exchange()
        //交换邮箱请求
        data object ExchangeEmailRequest : Exchange()
        //处理交换邮箱请求
        data class HandleExchangeEmailRequest(val exchangeId: String, val isAccept: Boolean) : Exchange()
        //处理交换手机号请求
        data class HandleExchangePhoneRequest(val exchangeId: String, val isAccept: Boolean) : Exchange()
        //预览简历
        data class PreviewResume(val exchangeId: String): Exchange()
        //预览文件
        data class PreviewFile(val fileBody: FileBody): Exchange()

    }

    //输入框内容变化
    //关闭页面
    data object OnBackPressed : BossChatUiIntent()

    data object OnStartUser : BossChatUiIntent()

    data class OnQuickReplayClick(val item: QuickReplyTemplateItem, val index: Int) : BossChatUiIntent()
    data class OnVisaSponsorSyncConfirm(val status: Int) : BossChatUiIntent()
    data class OnQuickReplayStartSending(val cmid: Long, val quickReply: QuickReplyTemplateItem): BossChatUiIntent()
    data object OnSyncQuickReply : BossChatUiIntent()
}