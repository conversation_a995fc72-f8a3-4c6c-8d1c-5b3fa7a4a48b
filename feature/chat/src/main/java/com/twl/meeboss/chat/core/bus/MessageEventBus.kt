package com.twl.meeboss.chat.core.bus

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * A singleton event bus for message-related events, using a SharedFlow.
 */
object MessageEventBus {
    private val _events = MutableSharedFlow<MessageEvent>(extraBufferCapacity = 64)
    val events = _events.asSharedFlow()

    fun tryPost(event: MessageEvent) {
        _events.tryEmit(event)
    }
} 