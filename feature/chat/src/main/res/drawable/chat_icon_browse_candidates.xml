<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <group>
    <clip-path
        android:pathData="M8,0L32,0A8,8 0,0 1,40 8L40,32A8,8 0,0 1,32 40L8,40A8,8 0,0 1,0 32L0,8A8,8 0,0 1,8 0z"/>
    <path
        android:pathData="M8,0L32,0A8,8 0,0 1,40 8L40,32A8,8 0,0 1,32 40L8,40A8,8 0,0 1,0 32L0,8A8,8 0,0 1,8 0z"
        android:fillColor="#3E59E8"/>
    <path
        android:pathData="M30,15V28.556C30,29.9 28.875,31 27.5,31H12.5C11.125,31 10,29.9 10,28.556V11.444C10,10.1 11.125,9 12.5,9H23.813L30,15Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M15,22.5H19"
        android:strokeWidth="1.5"
        android:fillColor="#00000000"
        android:strokeColor="#3E59E8"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M15,26.5H25"
        android:strokeWidth="1.5"
        android:fillColor="#00000000"
        android:strokeColor="#3E59E8"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M24,13.788V9L30,15H25.212C24.545,15 24,14.455 24,13.788Z"
        android:fillColor="#97A9FF"/>
    <path
        android:pathData="M8,0L32,0A8,8 0,0 1,40 8L40,32A8,8 0,0 1,32 40L8,40A8,8 0,0 1,0 32L0,8A8,8 0,0 1,8 0z"
        android:fillColor="#298DFF"/>
    <path
        android:pathData="M30,15V28.556C30,29.9 28.875,31 27.5,31H12.5C11.125,31 10,29.9 10,28.556V11.444C10,10.1 11.125,9 12.5,9H23.813L30,15Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M15,22.5H19"
        android:strokeWidth="1.5"
        android:fillColor="#00000000"
        android:strokeColor="#298DFF"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M15,26.5H25"
        android:strokeWidth="1.5"
        android:fillColor="#00000000"
        android:strokeColor="#298DFF"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M24,13.788V9L30,15H25.212C24.545,15 24,14.455 24,13.788Z"
        android:fillColor="#BDDCFF"/>
    <group>
      <clip-path
          android:pathData="M-0.429,-0.429h40.857v40.857h-40.857z"/>
      <path
          android:pathData="M7.428,-0.429L32.571,-0.429A7.857,7.857 0,0 1,40.428 7.428L40.428,32.571A7.857,7.857 0,0 1,32.571 40.428L7.428,40.428A7.857,7.857 0,0 1,-0.429 32.571L-0.429,7.428A7.857,7.857 0,0 1,7.428 -0.429z"
          android:fillColor="#00BC5E"/>
      <path
          android:pathData="M38.84,7.223C39.853,8.099 40.428,9.324 40.428,10.607V15.972V33.48C40.428,37.318 37.063,40.428 32.911,40.428H6.029C2.462,40.428 -0.429,37.766 -0.429,34.47V34.46L-0.429,6.519C-0.429,2.682 2.937,-0.429 7.089,-0.429H28.008C29.279,-0.429 30.502,0.018 31.431,0.82C33.376,2.501 36.824,5.481 38.84,7.223Z"
          android:fillColor="#00BC5E"
          android:fillType="evenOdd"/>
    </group>
    <group>
      <clip-path
          android:pathData="M8.5,8h24v24h-24z"/>
      <path
          android:pathData="M17.449,11.597C17.788,11.535 18.168,11.407 18.536,11.354C19.387,11.231 20.241,11.124 21.104,11.183C23.03,11.316 25.192,11.768 26.733,12.942C28.03,13.93 28.972,15.154 29.469,16.68C29.899,18.004 30.072,19.356 30.065,20.736C30.065,20.736 30.065,22.123 29.77,23.448C29.475,24.772 29.041,26.008 28.117,27.054C27.196,28.097 26.109,28.923 24.768,29.441C23.86,29.791 23.661,29.966 21.603,30.093C19.545,30.221 16.452,29.426 14.5,27.799C11.95,25.674 10.901,22.999 10.936,20.791C10.936,18.261 10.937,19.517 10.937,19.17V10.047C10.937,9.275 11.811,9.241 12.368,9.562C12.368,9.562 15.997,11.423 16.244,11.51C16.492,11.597 17.11,11.659 17.449,11.597ZM17.449,11.597C17.11,11.659 17.788,11.535 17.449,11.597Z"
          android:strokeWidth="1.8"
          android:fillColor="#00000000"
          android:strokeColor="#ffffff"/>
      <path
          android:pathData="M24.909,18.123V19.899"
          android:strokeLineJoin="round"
          android:strokeWidth="1.8"
          android:fillColor="#00000000"
          android:strokeColor="#ffffff"
          android:strokeLineCap="round"/>
      <path
          android:pathData="M20.395,18.123V19.899"
          android:strokeLineJoin="round"
          android:strokeWidth="1.8"
          android:fillColor="#00000000"
          android:strokeColor="#ffffff"
          android:strokeLineCap="round"/>
      <path
          android:pathData="M21.1,23.173C21.1,23.173 21.69,23.673 22.6,23.673C23.51,23.673 24.1,23.173 24.1,23.173"
          android:strokeLineJoin="round"
          android:strokeWidth="1.8"
          android:fillColor="#00000000"
          android:strokeColor="#ffffff"
          android:strokeLineCap="round"/>
    </group>
  </group>
</vector>
