package com.twl.meeboss.chat

import com.twl.meeboss.chat.export.constant.isFriendStatusCanExchange
import com.twl.meeboss.chat.export.constant.isFriendStatusHasSendExchange
import org.junit.Assert.assertEquals
import org.junit.Test

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {


    @Test
    fun isFriendStatusCanExchange_isCorrect() {
        val a = 7
        val b = 3
        val c = 2
        assertEquals(true, a.isFriendStatusCanExchange())
        assertEquals(true, b.isFriendStatusCanExchange())
        assertEquals(false, c.isFriendStatusCanExchange())
    }

    @Test
    fun isFriendStatusHasSendExchange_isCorrect() {
        val a = 0
        val b = 1

        val c = 7 //111
        val d = 15 //1111
        val e = 31 //11111

        val f = 4 //100
        val g = 2 //100

        assertEquals(false, a.isFriendStatusHasSendExchange())
        assertEquals(false, b.isFriendStatusHasSendExchange())
        assertEquals(true, c.isFriendStatusHasSendExchange())
        assertEquals(true, d.isFriendStatusHasSendExchange())
        assertEquals(true, e.isFriendStatusHasSendExchange())
        assertEquals(true, f.isFriendStatusHasSendExchange())
        assertEquals(false, g.isFriendStatusHasSendExchange())
    }
}