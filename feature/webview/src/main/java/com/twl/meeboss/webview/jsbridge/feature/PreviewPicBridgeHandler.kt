package com.twl.meeboss.webview.jsbridge.feature

import androidx.fragment.app.FragmentActivity
import com.hpbr.directhires.module.localhtml.jsbridge.AbsBridgeHandler
import com.hpbr.directhires.module.localhtml.jsbridge.CallBackFunction
import com.twl.meeboss.base.media.pictureselector.previewImages
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.webview.jsbridge.callbackError
import com.twl.meeboss.webview.jsbridge.callbackSuccess
import com.twl.meeboss.webview.jsbridge.logError

/**
 * 预览图片
 */
class PreviewPicBridgeHandler(private val activity: FragmentActivity) : AbsBridgeHandler() {
    override fun getInvokeMethodName(): String {
        return "previewPic"
    }

    override fun handler(params: Map<String, String>, function: CallBackFunction) {
        try {
            val picUrlsJson = params["picUrls"] ?: throw IllegalArgumentException("参数picUrls不能为空")
            val picUrls = GsonUtils.fromJson<List<String>>(picUrlsJson) ?: listOf()
            val previewIndex = params["previewIndex"]?.toInt() ?: 0
            activity.previewImages(picUrls, previewIndex)
            function.callbackSuccess()
        } catch (e: IllegalArgumentException) {
            logError(e)
            function.callbackError(e)
        } catch (e: NumberFormatException) {
            logError(e)
            function.callbackError(e)
        }
    }
}