package com.twl.meeboss.webview

import android.webkit.JavascriptInterface
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.common.log.XLog
import org.json.JSONObject

class WebViewJavascriptInterface(val common: WebViewCommon) {

    @JavascriptInterface
    fun sendAppMessage(json: String?) {
        if (!json.isNullOrBlank()) {
            try {
                val params = JSONObject(json)
                val action = params.optString("action")
                val payload = params.optJSONObject("payload")
                if (!action.isNullOrBlank()) {
                    common.webActionFactory?.handleActionFromWeb(action, payload)
                }
            } catch (e: Exception) {
                TLog.error(common.TAG, e.message)
            }
        }
    }

    @JavascriptInterface
    fun BZPostMessage(json:String?) {
        //测试方法: wst.BZPostMessage("{\"name\":\"getCurrentLanguage\",\"callbackId\":\"123\",\"params\":{\"abc\":123}}")
        XLog.info(common.TAG, "BZPostMessage json:${json}")
        json?.takeIf { it.isNotBlank() }?.let {
            val jsonObject = JSONObject(json)
            val actionName = jsonObject.optString("name")
            val callbackName = jsonObject.optString("callbackName")
            val callbackId = jsonObject.optString("callbackId")
            val params = jsonObject.optJSONObject("params") ?: JSONObject()

            if (!actionName.isNullOrBlank()) {
                params.put("localActionName",actionName)
                params.put("localCallbackName",callbackName)
                params.put("callbackId", callbackId)
                common.webActionFactory?.handleActionFromWeb(actionName, params)
            }


        }?:let {
            XLog.error(common.TAG, "BZPostMessage json empty")
        }
    }

}