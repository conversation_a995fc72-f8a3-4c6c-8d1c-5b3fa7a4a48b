package com.twl.meeboss.webview.jsbridge.common

import com.hpbr.directhires.module.localhtml.jsbridge.AbsBridgeHandler
import com.hpbr.directhires.module.localhtml.jsbridge.CallBackFunction
import com.twl.meeboss.webview.jsbridge.callbackSuccess
import com.twl.meeboss.webview.jsbridge.logInfo
import java.io.Serializable

class GetBusinessParamsBridgeHandler(private val businessParamsGetter: () -> Serializable?) : AbsBridgeHandler() {
    override fun getInvokeMethodName(): String {
        return "getBusinessParams"
    }

    override fun handler(params: Map<String, String>, function: CallBackFunction) {
        val businessParams = businessParamsGetter()
        logInfo("businessParams is $businessParams")
        function.callbackSuccess(businessParams ?: hashMapOf<String, String>())
    }
}