package com.twl.meeboss.webview.action

import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.webview.WebViewCommon
import com.twl.meeboss.webview.model.SetNavigationBarTitleParam

class WebSetNavigationBarTitleAction(val common: WebViewCommon) : AbsWebAction<SetNavigationBarTitleParam>(common) {
    override fun handle(action: String, paramBean: SetNavigationBarTitleParam?) {
        XLog.info(TAG, "WebSetNavigationBarTitleAction title:${paramBean?.title}")
        common.iWebViewCallback?.onReceivedTitle(paramBean?.title ?: "")
        loadJavascript(code = 0, message = null, zpData = null)
    }
}