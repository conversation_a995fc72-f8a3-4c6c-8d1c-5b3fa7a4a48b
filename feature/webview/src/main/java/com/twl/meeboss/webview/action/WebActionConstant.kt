package com.twl.meeboss.webview.action

object WebActionConstant {
    const val GET_CURRENT_LANGUAGE_ACTION = "getCurrentLanguage"
    const val INTERCEPT_CLOSE_EVENT_ACTION = "androidIsInterceptCloseEvent"
    const val APP_DELETE_ACCOUNT = "appDeleteAccount"
    const val APP_PAY = "appPay"
    const val HIDDEN_NAVIGATION_BAR = "hiddenNavigationBar"
    const val CLOSE_VIEW = "closeView"
    const val ROBOT_CHECK_DIALOG = "setTurnstileToken"
    const val SHOW_FULL_SCREEN = "showFullScreen"
    const val CLOSE_TURNSTILE = "closeTurnstile"
    const val GET_REGION = "getRegion"
    const val CHOOSE_REGION = "chooseRegion"
    const val SET_NAVIGATION_BAR_TITLE = "setNavigationBarTitle"
    const val H5_EXECUTE_REQUEST = "h5ExecuteRequest"
    const val H5_GET_IDENTITY = "getIdentity"
}