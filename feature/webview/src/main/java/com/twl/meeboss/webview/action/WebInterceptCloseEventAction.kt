package com.twl.meeboss.webview.action

import com.twl.meeboss.webview.WebViewCommon
import com.twl.meeboss.webview.model.WebInterceptCloseParam


class WebInterceptCloseEventAction(val common: WebViewCommon) : AbsWebAction<WebInterceptCloseParam>(common) {
    override fun handle(action: String, paramBean: WebInterceptCloseParam?) {
        webViewCommon.isInterceptClose = paramBean?.isIntercept == 1
        loadJavascript(code = 0, message = null, zpData = null)
    }
}