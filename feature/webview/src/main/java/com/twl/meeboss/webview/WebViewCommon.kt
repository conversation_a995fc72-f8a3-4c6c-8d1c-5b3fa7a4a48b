package com.twl.meeboss.webview

import android.annotation.SuppressLint
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import android.webkit.CookieManager
import android.webkit.WebView
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.BarUtils
import com.hpbr.directhires.module.localhtml.LocalHtmlService
import com.hpbr.directhires.module.localhtml.server.LocalHttpServer
import com.twl.meeboss.base.BuildConfig
import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.base.constants.BUNDLE_STRING
import com.twl.meeboss.base.ktx.isMeeBossHost
import com.twl.meeboss.common.ktx.toDp
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.webview.action.AbsWebActionFactory
import com.twl.meeboss.webview.jsbridge.JsBridgeRegistry
import java.net.MalformedURLException
import java.net.URL
import java.util.regex.Pattern

@SuppressLint("SetJavaScriptEnabled")
class WebViewCommon(val activity: FragmentActivity, val webView: WebView) {

    val webViewBridge = LocalHtmlService.attach(webView)
    private val jsBridgeRegistry = JsBridgeRegistry(activity, webViewBridge)

    val TAG =  this::class.java.simpleName

    private val javascriptInterface by lazy {
        WebViewJavascriptInterface(this)
    }

    var webActionFactory: AbsWebActionFactory? = null

    var iWebViewCallback: IWebViewCallback? = null

    private var webClients: WebClients? = null

    var mCurrentUrl:String? = null

    var isInterceptClose:Boolean = false
    var hasAddJavascriptInterface:Boolean = false

    init {
        webView.getSettings().apply {
            this.useWideViewPort = true
            this.loadWithOverviewMode = true
            this.javaScriptEnabled = true
            this.defaultTextEncodingName = "UTF-8"
            this.builtInZoomControls = true
            this.domStorageEnabled = true
            this.databaseEnabled = true
            this.mediaPlaybackRequiresUserGesture = false
            this.allowFileAccess = true
            this.textZoom = 100
            val userAgent = userAgentString
            val bossUserAgent = "${AppUtils.getAppName()}/${AppUtils.getAppVersionName()}/${AppUtils.getAppVersionCode()}"
            val meeBossDataUserAgent = buildMeeBossDataUserAgent()
            val meeBossData = "meeBossData/$meeBossDataUserAgent"
            userAgentString = "$userAgent $bossUserAgent $meeBossData"
            XLog.info(TAG, "WebView init userAgentString:$userAgentString")
        }

        /**
         * 注册jsBridge
         */
        jsBridgeRegistry.registerJsBridge()
    }

    /**
     * 构造meeBossData相关UA信息，给H5使用，后续新加的参数都添加到这里
     */
    private fun buildMeeBossDataUserAgent(): String {
        val statusBarHeight = BarUtils.getStatusBarHeight().toDp
        val navigationBarHeight = BarUtils.getNavBarHeight().toDp
        val dzEnv = getDzEnv()
        val meeBossDataMap = hashMapOf(
            "tabbarHeight" to "$navigationBarHeight",
            "statusBarHeight" to "$statusBarHeight",
            "DzEnv" to dzEnv
        )
        return GsonUtils.mapToJson(meeBossDataMap)
    }

    /**
     * 映射本地环境变量到本地H5所需环境变量
     *
     * @return 1：线上 2：预发 3：QA
     */
    private fun getDzEnv(): Int {
        return LocalManager.getConfig().environmentType
    }

    fun parseIntent(intent: Intent) {
        this.mCurrentUrl = intent.getStringExtra(BUNDLE_STRING)
        XLog.info(TAG, "WebView init Url:$mCurrentUrl")
        jsBridgeRegistry.parseIntent(intent)
        addJavascriptInterface()
    }

    fun start(webActionFactory: AbsWebActionFactory, iWebViewCallback: IWebViewCallback?){
        this.webActionFactory = webActionFactory.setWebViewCommon(this)
        this.iWebViewCallback = iWebViewCallback
        webClients = WebClients(this, iWebViewCallback)
    }

    private fun addJavascriptInterface(){
        if (hasAddJavascriptInterface) {
            XLog.info(TAG, "addJavascriptInterface hasAddJavascriptInterface")
            return
        }
        if (
            !BuildConfig.DEBUG &&
            !mCurrentUrl.isMeeBossHost()
            ) {
            XLog.info(TAG, "addJavascriptInterface not MeeBossHost:${mCurrentUrl}")
            return
        }
        hasAddJavascriptInterface = true
        webView.addJavascriptInterface(javascriptInterface, "wst")
        webView.removeJavascriptInterface("searchBoxJavaBridge_")
    }


    fun loadUrl(url: String?) {
        XLog.info(TAG, "loadUrl:${url}")
        if (url.isNullOrEmpty()) return
        this.mCurrentUrl = url
        addJavascriptInterface()
        syncCookie(url)
//        webView.loadUrl(url)
        webViewBridge.loadUrl(url)
    }

    fun log() {
        XLog.info(TAG, "shy")
    }

    fun syncCookie(url: String?) {
        if (activity == null) return
        var cookUrl: URL? = null
        try {
            cookUrl = URL(url)
        } catch (e: MalformedURLException) {
            cookUrl = null
            Log.i(TAG, "转换URL失败", e)
        }
        if (cookUrl == null) return
        val host = cookUrl.host

        if (host != null &&
            /*!BuildConfig.DEBUG &&
            !host.endsWith(".offertoday.com") &&
            !host.endsWith(".bosszhipin.com") &&
            !host.endsWith(".kanzhun.com") &&
            !host.endsWith(".dianzhangzhipin.com") &&
            !host.endsWith(".zhipin.com") &&*/
            !host.endsWith(".meeboss.com") &&
            !host.startsWith("localhost") &&
            !host.endsWith(".weizhipin.com")
        ) {
            Log.e(TAG, "H5协议只支持meeboss.com和localhost域名")
            return
        }

        val cookieManager: CookieManager = CookieManager.getInstance()
        cookieManager.setAcceptCookie(true)
        val cookieUrl = cookUrl.protocol + "://" + cookUrl.host + "/"
        val domain: String = getDomain(host) ?: ""
        val domainValue = if (TextUtils.isEmpty(domain)) "" else ";Domain=$domain"
        val sdkVersion = "${LocalHttpServer.WEB_SDK_VERSION}=${LocalHtmlService.getVersion()}"
        cookieManager.setCookie(cookieUrl, sdkVersion + domainValue)
        cookieManager.flush()
    }

    private fun getDomain(host: String?): String? {
        if (host != null) {
            val regex = "\\b((?:[0-9]{1,3}\\.){3}[0-9]{1,3})\\b"
            val pattern = Pattern.compile(regex) //IP
            val matcher = pattern.matcher(host)
            if (!matcher.matches()) { //非IP
                val startIndex = host.lastIndexOf(".") - 1
                val endIndex = host.lastIndexOf(".", startIndex)
                if (endIndex > 0 && endIndex < host.length) {
                    return host.substring(endIndex)
                }
            }
        }
        return host
    }

    fun activityResult(requestCode: Int, resultCode: Int, data: Intent?) {

    }

    fun goBack(): Boolean {
        val list = webView.copyBackForwardList() ?: return false
        val start = list.currentIndex
        var stepBack = -1
        while (start + stepBack >= 0) {
            var hist = list.getItemAtIndex(start + stepBack).url
            var isAnchorUrl = false
            if (!hist.isNullOrEmpty() && hist.endsWith("#")) {
                hist = hist.substring(0, hist.length - 1)
                isAnchorUrl = true
            }
            if (!mCurrentUrl.isNullOrEmpty() && mCurrentUrl?.endsWith("#") == true) {
                isAnchorUrl = true
                mCurrentUrl = mCurrentUrl?.substring(0, mCurrentUrl?.length ?: 0) ?: ""
            }
            if ((isAnchorUrl && mCurrentUrl == hist)) {
                stepBack -= 1
            } else {
                mCurrentUrl = hist
                break
            }
        }
        if (webView.canGoBackOrForward(stepBack)) {
            webView.goBackOrForward(stepBack)
            return true
        }
        return false
    }

    fun isInterceptCloseEvent(): Boolean {
        return isInterceptClose
    }


}
