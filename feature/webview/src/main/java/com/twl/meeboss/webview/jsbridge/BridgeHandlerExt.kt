package com.twl.meeboss.webview.jsbridge

import com.hpbr.directhires.module.localhtml.jsbridge.AbsBridgeHandler
import com.hpbr.directhires.module.localhtml.jsbridge.BridgeHandler
import com.hpbr.directhires.module.localhtml.jsbridge.BridgeResult
import com.hpbr.directhires.module.localhtml.jsbridge.CallBackFunction
import com.techwolf.lib.tlog.TLog

fun BridgeHandler.logInfo(message: String) {
    TLog.info(AbsBridgeHandler.TAG, "methodName: ${getInvokeMethodName()}, message: $message}")
}

fun BridgeHandler.logError(message: String) {
    TLog.error(AbsBridgeHandler.TAG, "methodName: ${getInvokeMethodName()}, message: $message}")
}

fun BridgeHandler.logError(throwable: Throwable) {
    TLog.error(AbsBridgeHandler.TAG, throwable, "methodName: ${getInvokeMethodName()}, throwable: ${throwable.message}")
}

fun CallBackFunction.callbackError(throwable: Throwable) {
    onCallBack(BridgeResult<Any>().setError(errorMessage = throwable.message ?: "").toJson())
}

fun CallBackFunction.callbackSuccess() {
    onCallBack(BridgeResult<Any>().toJson())
}

fun <T> CallBackFunction.callbackSuccess(data: T) {
    onCallBack(BridgeResult<T>().setData(data).toJson())
}