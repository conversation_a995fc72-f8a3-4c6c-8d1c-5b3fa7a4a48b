package com.twl.meeboss.webview.jsbridge.common

import com.hpbr.directhires.module.localhtml.jsbridge.AbsBridgeHandler
import com.hpbr.directhires.module.localhtml.jsbridge.CallBackFunction
import com.twl.meeboss.base.point.PointBean
import com.twl.meeboss.base.point.PointHelper
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.webview.jsbridge.callbackError
import com.twl.meeboss.webview.jsbridge.callbackSuccess
import com.twl.meeboss.webview.jsbridge.logError
import org.json.JSONException
import org.json.JSONObject

/**
 * 打点
 */
class PointBridgeHandler : AbsBridgeHandler() {
    private val pFunMap: MutableMap<String, (PointBean, String) -> Unit> = mutableMapOf()
    private val encryptPFunMap: MutableMap<String, (PointBean, String) -> Unit> = mutableMapOf()

    init {
        // 初始化普通参数处理函数
        pFunMap["p1"] = { bean, value -> bean.addP(value) }
        pFunMap["p2"] = { bean, value -> bean.addP2(value) }
        pFunMap["p3"] = { bean, value -> bean.addP3(value) }
        pFunMap["p4"] = { bean, value -> bean.addP4(value) }
        pFunMap["p5"] = { bean, value -> bean.addP5(value) }
        pFunMap["p6"] = { bean, value -> bean.addP6(value) }
        pFunMap["p7"] = { bean, value -> bean.addP7(value) }
        pFunMap["p8"] = { bean, value -> bean.addP8(value) }

        // 初始化加密参数处理函数
        encryptPFunMap["p1"] = { bean, value -> bean.addEncryptP(value) }
        encryptPFunMap["p2"] = { bean, value -> bean.addEncryptP2(value) }
        encryptPFunMap["p3"] = { bean, value -> bean.addEncryptP3(value) }
        encryptPFunMap["p4"] = { bean, value -> bean.addEncryptP4(value) }
        encryptPFunMap["p5"] = { bean, value -> bean.addEncryptP5(value) }
        encryptPFunMap["p6"] = { bean, value -> bean.addEncryptP6(value) }
        encryptPFunMap["p7"] = { bean, value -> bean.addEncryptP7(value) }
        encryptPFunMap["p8"] = { bean, value -> bean.addEncryptP8(value) }
    }

    override fun getInvokeMethodName(): String {
        return "point"
    }

    override fun handler(params: Map<String, String>, function: CallBackFunction) {
        try {
            val action = params["action"]?.takeIf { it.isNotEmpty() }
                ?: throw IllegalArgumentException("参数action不能为空")
            val encryptPListJson = params["encryptPList"] ?: ""
            val encryptPList =
                GsonUtils.fromJson<List<String>>(encryptPListJson)?.takeIf { it.isNotEmpty() }
                    ?: listOf()

            PointHelper.reportPoint(action) {
                // 处理 p1-p8 参数
                for (i in 1..8) {
                    val pKey = "p$i"
                    val pValue = params[pKey]
                    if (!pValue.isNullOrEmpty()) {
                        if (encryptPList.contains(pKey)) {
                            encryptPFunMap[pKey]?.invoke(this, pValue)
                        } else {
                            pFunMap[pKey]?.invoke(this, pValue)
                        }
                    }
                }

                // 处理 ext 参数
                params["ext"]?.let { extJson ->
                    if (extJson.isNotEmpty()) {
                        val jsonObject = JSONObject(extJson)
                        val keys = jsonObject.keys()
                        while (keys.hasNext()) {
                            val key = keys.next()
                            val value = jsonObject.getString(key)
                            if (value.isNotEmpty()) {
                                this[key] = value
                            }
                        }
                    }
                }
            }
            function.callbackSuccess()
        } catch (e: IllegalArgumentException) {
            logError(e)
            function.callbackError(e)
        } catch (e: JSONException) {
            logError(e)
            function.callbackError(e)
        }
    }
}