package com.twl.meeboss.webview.action

import com.twl.meeboss.base.model.common.CommonZpWebParam
import com.twl.meeboss.webview.WebViewCommon

class WebCloseViewAction(val common: WebViewCommon) : AbsWebAction<CommonZpWebParam>(common) {
    override fun handle(action: String, paramBean: CommonZpWebParam?) {
        common.activity.finish()
        loadJavascript(code = 0, message = null, zpData = null)
    }
}