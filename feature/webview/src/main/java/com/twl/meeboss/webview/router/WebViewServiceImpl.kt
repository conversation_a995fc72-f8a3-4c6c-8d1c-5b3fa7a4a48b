package com.twl.meeboss.webview.router

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.sankuai.waimai.router.annotation.RouterService
import com.twl.meeboss.webview.component.XWebViewComponent
import com.twl.meeboss.webview.export.IWebViewInterface
import com.twl.meeboss.webview.export.WebViewRouterPath

@RouterService(interfaces = [IWebViewInterface::class], key = [WebViewRouterPath.WEBVIEW_SERVICE], singleton = true)
class WebViewServiceImpl : IWebViewInterface {
    @Composable
    override fun DefaultWebViewComponent(modifier: Modifier?, url: String) {
        XWebViewComponent(modifier = modifier, url = url)
    }
}