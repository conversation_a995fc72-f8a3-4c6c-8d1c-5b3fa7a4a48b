package com.twl.meeboss.webview.viewmodel

import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.URLUtils
import com.twl.meeboss.webview.WebViewConstant
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class WebViewViewModel @Inject constructor(
    //private val repos: BossJobRepository
) : BaseMviViewModel<WebViewUiState, WebViewUiIntent>() {

    override fun initUiState(): WebViewUiState = WebViewUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is WebViewUiIntent.UpdateTitle -> {
                sendUiState {
                    copy(title = intent.title?:"")
                }
            }
            is WebViewUiIntent.UpdateLoadProgress -> {
                sendUiState {
                    copy(
                        progress = intent.progress
                    )
                }
            }
            is WebViewUiIntent.HideNavigationBar -> {
                sendUiState {
                    copy(hideNavigationBar = intent.isHidden)
                }
            }
            is WebViewUiIntent.ParseUrlParam -> {
                parseUrlParam(intent.url)
            }

            else -> {

            }

        }
    }

    private fun parseUrlParam(url:String) {
        val paramMap = URLUtils.parseUrlParam(url)
        if (paramMap.containsKey(WebViewConstant.PARAM_NO_HEAD) && !paramMap[WebViewConstant.PARAM_NO_HEAD].isNullOrEmpty()) {
            try {
                val noHead = Integer.parseInt(paramMap[WebViewConstant.PARAM_NO_HEAD] ?: "")
                if (noHead == WebViewConstant.CONFIG_NO_HEAD) {
                    sendUiState {
                        copy(hideNavigationBar = true)
                    }
                }
            } catch (e: Throwable) {
                XLog.error(TAG, "parseUrlParam noHead error:${url}")
            }
        }
    }

}

data class WebViewUiState(
    val title: String = "",
    val progress: Int = 0,
    val hideNavigationBar:Boolean = false,
) : IUiState

sealed class WebViewUiIntent : IUiIntent {
    data class UpdateTitle(val title: String? = "") : WebViewUiIntent()
    data class UpdateLoadProgress(val progress: Int = 0) : WebViewUiIntent()
    data class HideNavigationBar(val isHidden:Boolean = false) : WebViewUiIntent()
    data class ParseUrlParam(val url:String = "") : WebViewUiIntent()
}