package com.twl.meeboss.webview.action

import com.twl.meeboss.webview.WebViewCommon
import com.twl.meeboss.webview.model.HiddenNavigationBarParam

class WebHiddenNavigationBarAction(val common: WebViewCommon) : AbsWebAction<HiddenNavigationBarParam>(common) {
    override fun handle(action: String, paramBean: HiddenNavigationBarParam?) {
        common.iWebViewCallback?.hideNavigationBar(paramBean?.isHidden == 1)
        loadJavascript(code = 0, message = null, zpData = null)
    }
}