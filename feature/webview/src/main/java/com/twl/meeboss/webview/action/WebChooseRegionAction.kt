package com.twl.meeboss.webview.action

import androidx.fragment.app.FragmentActivity
import com.twl.meeboss.base.main.router.BasePageRouter
import com.twl.meeboss.base.model.common.CommonZpWebParam
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.webview.WebViewCommon
import org.json.JSONObject

class WebChooseRegionAction(val common: WebViewCommon) : AbsWebAction<CommonZpWebParam>(common) {
    override fun handle(action: String, paramBean: CommonZpWebParam?) {
        if (common.activity is FragmentActivity) {
            BasePageRouter.showSelectAreaCodeBottomSheet(
                activity = common.activity,
                onCancel = {
                    loadJavascript(
                        0,
                        "success",
                        paramBean?.callbackId,
                        null
                    )
                }
            ) {
                loadJavascript(
                    0,
                    "success",
                    paramBean?.callbackId,
                    JSONObject(GsonUtils.toJson(it.formatAreaCodeBean()))
                )
            }
        } else {
            XLog.error(TAG, "handle error activity not FragmentActivity")
        }
    }
}