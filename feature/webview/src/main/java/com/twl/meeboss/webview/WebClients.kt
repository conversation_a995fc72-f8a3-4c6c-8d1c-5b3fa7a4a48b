package com.twl.meeboss.webview

import android.text.TextUtils
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebView
import com.hpbr.directhires.module.localhtml.LocalHtmlService
import com.twl.meeboss.base.protocol.ProtocolHelper
import com.twl.meeboss.common.log.XLog
import androidx.core.net.toUri

class WebClients(val common: WebViewCommon, val callback: IWebViewCallback?) {
    companion object {
        const val TAG = "WebClients"
    }

    init {
        common.webView.run {
            // 设置WebChromeClient
            webChromeClient = object : WebChromeClient() {
                override fun onProgressChanged(view: WebView?, newProgress: Int) {
                    super.onProgressChanged(view, newProgress)
                    callback?.onProgressChanged(newProgress)
                }

                override fun onReceivedTitle(view: WebView?, title: String?) {
                    super.onReceivedTitle(view, title)
                    XLog.info("WebClients", "onReceivedTitle:${title}")
                    callback?.onReceivedTitle(title ?: "")
                }
            }

            // 设置WebViewClient
            webViewClient = object : android.webkit.WebViewClient() {

                private var lastReplaceUrl: String? = null
                private var lastReplaceTime = 0L

                override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                    val url = request?.url?.toString() ?: return true
                    if (url.startsWith("http") || url.startsWith("https")) {
                        XLog.info(TAG, "shouldOverrideUrlLoading[%s]", url)
                        val currentTimeMillis = System.currentTimeMillis()

                        /*
                            replace触发条件修改
                            1.非meeboss://链接
                            2.存在replace链接
                            3.当前加载链接与replace链接相同
                            4.当期时间与replace时间相差50ms以内（经验值，实际测试中时差在毫秒级）
                         */
                        if (!TextUtils.isEmpty(lastReplaceUrl)
                            && isSameUrl(lastReplaceUrl!!, url)
                            && currentTimeMillis - lastReplaceTime < 50
                        ) {
                            lastReplaceUrl = null
                            return false
                        }
                        lastReplaceUrl = null
                        if (url.contains("isFromReplace=true")) {
                            lastReplaceUrl = deleteReplaceParams(url)
                            lastReplaceTime = System.currentTimeMillis()

                            val script =
                                "javascript:location.replace('" + deleteReplaceParams(url) + "')"
                            XLog.info(TAG, "script : %s", script)
                            view?.evaluateJavascript(script, null)
                            return true
                        }
                    }
                    ProtocolHelper.parseProtocol(url)
                    return true
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    common.webViewBridge.loadJsBridge()
                }

                /**
                 * 因为实际加载的url H5可能会修改参数，所以只能从host和path来判断是否是统一链接
                 */
                private fun isSameUrl(replaceUrl: String, url: String): Boolean {
                    try {
                        val parseReplace = replaceUrl.toUri()
                        val parseUrl = url.toUri()
                        return TextUtils.equals(
                            parseReplace.host,
                            parseUrl.host
                        ) && TextUtils.equals(parseReplace.path, parseUrl.path)
                    } catch (e: Exception) {
                        XLog.error(TAG, e, "isSameUrl error")
                        return false
                    }
                }

                private fun deleteReplaceParams(url: String): String? {
                    var resultUrl = url
                    if ((resultUrl.startsWith("http://") || resultUrl.startsWith("https://")) && resultUrl.contains("isFromReplace=true")) {
                        val originalUri = resultUrl.toUri()
                        val builder = originalUri.buildUpon()
                        val queryParamNames = originalUri.queryParameterNames
                        if (queryParamNames != null && queryParamNames.isNotEmpty()) {
                            // 清除现有的查询参数
                            builder.clearQuery()
                            // 重新添加除了要移除的参数外的所有参数
                            for (key in queryParamNames) {
                                if (key != "isFromReplace") {
                                    val values = originalUri.getQueryParameters(key)
                                    if (values != null) {
                                        for (value in values) {
                                            builder.appendQueryParameter(key, value)
                                        }
                                    }
                                }
                            }
                        }
                        resultUrl = builder.build().toString()
                    }
                    common.syncCookie(resultUrl)
                    return if (resultUrl.contains(LocalHtmlService.DISABLE_LOCAL_H5 + "=true")) {
                        resultUrl
                    } else {
                        LocalHtmlService.replaceUrl(resultUrl)
                    }
                }

            }
        }

    }
}