package com.twl.meeboss.webview.jsbridge.common

import android.os.Bundle
import com.hpbr.directhires.module.localhtml.jsbridge.AbsBridgeHandler
import com.hpbr.directhires.module.localhtml.jsbridge.CallBackFunction
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.model.common.FORCE_LOGOUT_DATA
import com.twl.meeboss.base.model.common.FORCE_LOGOUT_OTHER_DEVICE
import com.twl.meeboss.base.model.common.ForceLogoutData
import com.twl.meeboss.webview.jsbridge.callbackSuccess

/**
 * 强制退出登录
 */
class CheckLogoutBridgeHandler : AbsBridgeHandler() {
    override fun getInvokeMethodName(): String {
        return "checkLogout"
    }

    override fun handler(params: Map<String, String>, function: CallBackFunction) {
        val bundle = Bundle()
        val data = ForceLogoutData(
            code = FORCE_LOGOUT_OTHER_DEVICE,
        )
        bundle.putSerializable(FORCE_LOGOUT_DATA, data)
        AccountManager.logout()
        function.callbackSuccess()
    }
}