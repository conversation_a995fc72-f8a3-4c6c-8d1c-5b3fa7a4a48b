package com.twl.meeboss.webview.jsbridge.feature

import androidx.annotation.Keep
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.ActivityUtils
import com.hpbr.directhires.module.localhtml.jsbridge.CallBackFunction
import com.hpbr.directhires.module.localhtml.jsbridge.GenericBridgeHandler
import com.twl.meeboss.base.model.common.CommonTypeBean
import com.twl.meeboss.boss.export.BossServiceRouter

@Keep
class ShowCopiedJobAlertBridgeHandler : GenericBridgeHandler<ShowCopiedJobAlertData>() {
    override fun getInvokeMethodName(): String {
        return "showCopiedJobAlert"
    }

    override fun handler(t: ShowCopiedJobAlertData, function: CallBackFunction) {
        ActivityUtils.getTopActivity()?.let {
            if (it is FragmentActivity) {
                BossServiceRouter.showCopiedJobAlert(
                    activity = it,
                    jobId = t.jobId ?: "",
                    jobTitle = t.jobTitle ?: "",
                    employmentType = t.jobType?.map { type -> type.name }?.joinToString(",") ?: "",
                    workplaceType = t.locationType?.name ?: ""
                )
            }
        }
    }
}

@Keep
data class ShowCopiedJobAlertData(
    val jobId: String? = null,
    val jobTitle: String? = null,
    val jobType: List<CommonTypeBean>? = null,
    val locationType: CommonTypeBean? = null,
)