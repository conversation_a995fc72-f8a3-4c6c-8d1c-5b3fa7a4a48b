package com.twl.meeboss.webview.action

import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.webview.WebViewCommon
import com.twl.meeboss.webview.model.GetCurrentLanguageParam
import org.json.JSONObject

class WebGetCurrentLanguageAction(val common: WebViewCommon):AbsWebAction<GetCurrentLanguageParam>(common) {
    override fun handle(action: String, paramBean: GetCurrentLanguageParam?) {
        loadJavascript(
            0,
            "success",
            paramBean?.callbackId,
            JSONObject()
                .put("currentLanguage", LocalManager.getConfig().getAppLanguageCountryStr())
        )
    }
}