package com.twl.meeboss.webview.action

import com.twl.meeboss.base.model.common.CommonZpWebParam
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.webview.WebViewCommon
import org.json.JSONObject

class WebGetIdentityAction(val common: WebViewCommon) : AbsWebAction<CommonZpWebParam>(common) {
    override fun handle(action: String, paramBean: CommonZpWebParam?) {
        loadJavascript(0, "", paramBean?.callbackId, JSONObject().put("identity", UserProvider.getIdentify()))
    }
}