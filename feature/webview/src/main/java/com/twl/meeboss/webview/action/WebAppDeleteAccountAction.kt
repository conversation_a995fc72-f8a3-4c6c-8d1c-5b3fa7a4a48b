package com.twl.meeboss.webview.action

import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.main.router.BasePageRouter
import com.twl.meeboss.base.model.common.CommonZpWebParam
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.common.utils.getNewScope
import com.twl.meeboss.core.network.HttpResult
import com.twl.meeboss.core.ui.activity.FoundationActivity
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.webview.R
import com.twl.meeboss.webview.WebViewCommon
import kotlinx.coroutines.launch

class WebAppDeleteAccountAction(val common: WebViewCommon) : AbsWebAction<CommonZpWebParam>(common) {
    override fun handle(action: String, paramBean: CommonZpWebParam?) {
        getNewScope().launch {
            if (common.activity is FoundationActivity) {
                common.activity.showLoadingDialog(false)
            }
            val result = BasePageRouter.deleteAccount()
            if (common.activity is FoundationActivity) {
                common.activity.dismissLoadingDialog()
            }
            if (result.isSuccess) {
                AccountManager.logout()
                T.ss(R.string.deleteaccount_finalconfirmation_popup_title.toResourceString())
            } else {
                when (result) {
                    is HttpResult.ApiError -> {
                        T.ss(result.message)
                    }

                    is HttpResult.NetworkError -> {
                        T.ss(result.error.message)
                    }

                    else -> {

                    }
                }
            }
            loadJavascript(code = 0, message = null, zpData = null)
        }

    }
}