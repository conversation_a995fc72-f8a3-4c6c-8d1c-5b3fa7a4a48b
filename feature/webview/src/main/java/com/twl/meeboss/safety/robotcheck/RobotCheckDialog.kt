package com.twl.meeboss.safety.robotcheck

import android.app.Dialog
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import androidx.core.graphics.drawable.toDrawable
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.ScreenUtils
import com.twl.meeboss.base.databinding.BaseMachineCheckDialogBinding
import com.twl.meeboss.core.ui.fragment.BaseCenterDialog
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.webview.WebViewCommon
import com.twl.meeboss.webview.action.AbsWebActionFactory
import com.twl.meeboss.webview.action.IWebAction
import com.twl.meeboss.webview.action.MachineCheckAction
import com.twl.meeboss.webview.action.WebActionConstant
import com.twl.meeboss.webview.action.WebGetCurrentLanguageAction
import com.twl.meeboss.webview.export.WebUrls
import com.twl.meeboss.webview.ktx.appendParamToWebUrl
import org.json.JSONObject

/**
 * turnstile 人机校验弹框
 */
class MachineCheckDialog : BaseCenterDialog() {

    private var common:WebViewCommon? = null
    private var callback: ((String) -> Unit)? = null

    companion object {
        const val TAG = "MachineCheckDialog"
        fun newInstance(callback: (String) -> Unit): MachineCheckDialog {
            return MachineCheckDialog().apply {
                this.callback = callback
            }
        }
    }

    private val actionFactory: AbsWebActionFactory = object : AbsWebActionFactory() {

        override fun createWebAction(action: String): IWebAction? {
            return when (action) {
                WebActionConstant.ROBOT_CHECK_DIALOG -> {
                    MachineCheckAction {
                        dismissSafely()
                        callback?.invoke(it)
                    }
                }

                WebActionConstant.SHOW_FULL_SCREEN -> {
                    object : IWebAction {
                        override fun handleActionFromWeb(action:String, params: JSONObject?) {
                            activity?.runOnUiThread {
                                mBinding?.loadingMask?.visibility = View.GONE
                                mBinding?.webView?.apply {
                                    scaleX = 0f
                                    scaleY = 0f
                                    animate()
                                        .alpha(1f)
                                        .scaleX(1f)
                                        .scaleY(1f)
                                        .setDuration(300)
                                        .setStartDelay(120)
                                        .setInterpolator(DecelerateInterpolator())
                                        .start()
                                }
                            }
                        }
                    }
                }

                WebActionConstant.CLOSE_TURNSTILE -> {
                    object : IWebAction {
                        override fun handleActionFromWeb(action:String, params: JSONObject?) {
                            dismissSafely()
                        }
                    }
                }

                WebActionConstant.GET_CURRENT_LANGUAGE_ACTION -> {
                    WebGetCurrentLanguageAction(common!!)
                }

                else->{
                    null
                }
            }
        }
    }

    private var mBinding: BaseMachineCheckDialogBinding? = null

    override fun setDialogAttributes(dialog: Dialog) {
        dialog.run {
            this.setCancelable(true)
            this.setCanceledOnTouchOutside(true)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val binding = BaseMachineCheckDialogBinding.inflate(inflater, container, false)
        mBinding = binding
        binding.webView.layoutParams?.let {
            it.width = ScreenUtils.getScreenWidth()
            it.height = ScreenUtils.getScreenHeight()
        }
        binding.loadingMask.let {
            it.layoutParams?.width = ScreenUtils.getScreenWidth()
            it.layoutParams?.height = ScreenUtils.getScreenHeight()
            it.setOnClickListener { dismissSafely() }
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mBinding?.run {
            common = WebViewCommon(requireActivity(), this.webView)
            common?.start(actionFactory, null)
            this.webView.setBackgroundColor(Color.TRANSPARENT)
            this.webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
            this.webView.settings.run {
                loadWithOverviewMode = false
                this.useWideViewPort = true
            }
            this.webView.isHorizontalScrollBarEnabled = false
            common?.loadUrl(WebUrls.getRobotCheckUrl().appendParamToWebUrl())
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
    }

}


/**
 * 人机校验弹框
 */
fun FragmentActivity.showRobotCheckDialog(oldToken:String? = "", callback: ((String) -> Unit)) {
    if (oldToken.isNullOrBlank()) {
        MachineCheckDialog.newInstance(callback).showSafely(this)
    } else {
        callback(oldToken)
    }
}

