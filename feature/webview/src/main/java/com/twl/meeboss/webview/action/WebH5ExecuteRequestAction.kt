package com.twl.meeboss.webview.action

import com.twl.meeboss.common.utils.getNewScope
import com.twl.meeboss.core.network.HttpCore
import com.twl.meeboss.core.network.config.HttpConfigManager
import com.twl.meeboss.webview.WebViewCommon
import com.twl.meeboss.webview.model.H5HttpRequestParam
import kotlinx.coroutines.launch
import okhttp3.FormBody
import okhttp3.Request
import org.json.JSONObject

/**
 * 给H5提供客户端请求功能
 */
class WebH5ExecuteRequestAction(val common: WebViewCommon) : AbsWebAction<H5HttpRequestParam>(common) {
    override fun handle(action: String, paramBean: H5HttpRequestParam?) {
        paramBean ?: return
        paramBean.url.takeIf { it.isNotEmpty() } ?:apply {
            sendDefaultErrorCallback("url empty", paramBean)
            return
        }
        paramBean.method.takeIf {
            it.isNotEmpty() &&
                    (it.equals("get", true) || it.equals("post", true))
        } ?:apply {
            sendDefaultErrorCallback("method is not get or post", paramBean)
            return
        }
        getNewScope().launch {
            runCatching {
                val url = paramBean.url.takeIf { !it.startsWith("/") } ?: paramBean.url.substring(1)
                val method = paramBean.method

                val requestBuilder = Request.Builder()
                if (method.equals("get", true)) {
                    val paramToString = paramBean.params.map {
                        "${it.key}=${it.value}"
                    }.joinToString("&")
                    val getUrl = HttpConfigManager.getNetEnvironment().baseHttpUrl + url + "?${paramToString}"
                    requestBuilder.url(getUrl).get()
                } else if (method.equals("post", true)) {
                    val postUrl = HttpConfigManager.getNetEnvironment().baseHttpUrl + url
                    val formBuilder = FormBody.Builder()
                    paramBean.params.forEach { (key, value) ->
                        formBuilder.add(key, value)
                    }
                    requestBuilder.url(postUrl).post(formBuilder.build())
                }
                val request = requestBuilder.build()
                val response = HttpCore.call(request)
                if (response.isSuccessful && response.body != null) {
                    loadJavascript(
                        0,
                        "success",
                        paramBean.callbackId,
                        JSONObject(response.body?.string() ?: "")
                    )
                } else {
                    sendDefaultErrorCallback(response.message, paramBean)
                }
            }.onFailure {
                sendDefaultErrorCallback(it.message, paramBean)
            }
        }
    }

    private fun sendDefaultErrorCallback(message: String?, paramBean: H5HttpRequestParam) {
        loadJavascript(
            -1,
            message,
            paramBean.callbackId,
            null
        )
    }
}