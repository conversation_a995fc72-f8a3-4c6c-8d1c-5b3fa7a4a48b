package com.twl.meeboss.webview.component

import android.view.LayoutInflater
import android.view.View
import android.webkit.WebView
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.fragment.app.FragmentActivity
import com.hpbr.directhires.module.localhtml.LocalHtmlService
import com.twl.meeboss.base.R
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.webview.WebViewCommon
import com.twl.meeboss.webview.action.createWebActionFactory

@Composable
fun XWebViewComponent(
    modifier: Modifier? = null,
    url: String,
    webViewCommon: WebViewCommon? = null,
    contentView: View? = null,
    iWebViewCallback: com.twl.meeboss.webview.IWebViewCallback? = null,
) {
    val context = LocalContext.current
    var common:WebViewCommon? = webViewCommon
    Box(modifier = modifier ?: Modifier.fillMaxSize()) {
        AndroidView(
            modifier = Modifier
                .fillMaxSize(),
            factory = {
                var webViewContentView: View? = contentView
                if (webViewContentView == null) {
                    webViewContentView =
                        LayoutInflater.from(it).inflate(R.layout.base_webview_container, null)
                    val webView = webViewContentView.findViewById<WebView>(R.id.webView)
                    common = WebViewCommon(context as FragmentActivity, webView)
                    common?.start(createWebActionFactory(common), iWebViewCallback)
                }

                url.takeIf { url ->
                    url.isNotBlank()
                }?.let { realUrl ->
                    val finalUrl = LocalHtmlService.replaceUrl(realUrl) ?: realUrl
                    common?.syncCookie(finalUrl)
                } ?: apply {
                    XLog.error("XWebViewComponent", "AndroidView factory method called, load Url blank")
                }
                webViewContentView!!
            },
            update = {
                url.takeIf { it.isNotBlank() }?.let { realUrl ->
                    val finalUrl = LocalHtmlService.replaceUrl(realUrl) ?: realUrl
                    XLog.error("XWebViewComponent", "load Url $finalUrl")
                    it.findViewById<WebView>(R.id.webView).loadUrl(finalUrl)
                }?:apply {
                    XLog.error("XWebViewComponent", "AndroidView update method called, load Url blank")
                }
            })
    }
}