package com.twl.meeboss.webview.ktx

import com.blankj.utilcode.util.BarUtils
import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.common.ktx.toDp
import com.twl.meeboss.common.utils.URLUtils

fun String.appendParamToWebUrl():String {
    return URLUtils.appendParamsToUrl(this, mapOf(
        "lang" to LocalManager.getConfig().getAppLanguageCountryStr(),
        "barHeight" to BarUtils.getStatusBarHeight().toDp.toString(),
    ))
}