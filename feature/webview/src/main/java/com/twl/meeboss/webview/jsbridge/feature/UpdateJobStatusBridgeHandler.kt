package com.twl.meeboss.webview.jsbridge.feature

import com.hpbr.directhires.module.localhtml.jsbridge.AbsBridgeHandler
import com.hpbr.directhires.module.localhtml.jsbridge.CallBackFunction
import com.twl.meeboss.base.eventbus.sendIntLiveEvent
import com.twl.meeboss.boss.export.BossEventBusKey
import com.twl.meeboss.webview.jsbridge.callbackError
import com.twl.meeboss.webview.jsbridge.logError

/**
 * 职位状态更新后，通知客户端
 */
class UpdateJobStatusBridgeHandler : AbsBridgeHandler() {
    override fun getInvokeMethodName(): String {
        return "updateJobStatus"
    }

    override fun handler(params: Map<String, String>, function: CallBackFunction) {
        try {
            val status = params["status"]?.toInt() ?: throw IllegalArgumentException("参数status不能为空")
            sendIntLiveEvent(BossEventBusKey.UPDATE_JOB_STATUS, status)
        } catch (e: IllegalArgumentException) {
            logError(e)
            function.callbackError(e)
        } catch (e: NumberFormatException) {
            logError(e)
            function.callbackError(e)
        }
    }
}