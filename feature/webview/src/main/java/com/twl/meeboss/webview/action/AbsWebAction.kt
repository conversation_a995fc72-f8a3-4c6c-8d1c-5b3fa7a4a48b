package com.twl.meeboss.webview.action

import android.app.Activity
import com.twl.meeboss.base.model.common.CommonZpWebParam
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.common.utils.getNewScope
import com.twl.meeboss.webview.WebViewCommon
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.lang.reflect.ParameterizedType

/**
 * webview action基类
 */
abstract class AbsWebAction<T : CommonZpWebParam>(val webViewCommon: WebViewCommon) : IWebAction {
    val TAG = this::class.java.simpleName

    fun getActivity(): Activity {
        return webViewCommon.activity
    }

    protected fun loadJavascript(code:Int, message:String?, callbackId:String? = null, zpData:JSONObject?) {
        getNewScope().launch(Dispatchers.Main) {
            try {
                val realCallback = "KZWebViewJSBridge.handleMessageFromNative"
                val resultBean = JSONObject()
                resultBean.put("code", code)
                resultBean.put("message", message)
                resultBean.put("responseId", callbackId)
                resultBean.put("zpData", zpData)
                val loadUrl = "javascript:${realCallback}(${resultBean})"
                XLog.info(webViewCommon.TAG, "loadJavascript:$loadUrl")
                webViewCommon.webView.loadUrl(loadUrl)

            } catch (e: Throwable) {
                XLog.error(webViewCommon.TAG, "loadJavascript error:${e.message}")
            }
        }
    }


    @Suppress("UNCHECKED_CAST")
    protected fun getMessageType(): Class<T> {
        val parameterizedType = javaClass.genericSuperclass as ParameterizedType
        val type = parameterizedType.actualTypeArguments
        return type[0] as Class<T>
    }

    override fun handleActionFromWeb(action: String, params: JSONObject?) {
        val json = params?.toString()
        var paramBean:T? = null
        try {
            paramBean = GsonUtils.fromJson(json, getMessageType())
        } catch (e: Throwable) {
            XLog.error(webViewCommon.TAG, "handleActionFromWeb format error:${e.message}")
        }

        handle(action, paramBean)
    }

    abstract fun handle(action:String, paramBean:T?)
}