package com.twl.meeboss.webview.action

import com.twl.meeboss.base.config.AreaCodeHelper
import com.twl.meeboss.base.model.common.CommonZpWebParam
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.webview.WebViewCommon
import org.json.JSONObject

class WebGetRegionAction(val common: WebViewCommon) : AbsWebAction<CommonZpWebParam>(common) {
    override fun handle(action: String, paramBean: CommonZpWebParam?) {
        loadJavascript(
            0,
            "success",
            paramBean?.callbackId,
            JSONObject(GsonUtils.toJson(AreaCodeHelper.getAreaCode().formatAreaCodeBean()))
        )
    }
}