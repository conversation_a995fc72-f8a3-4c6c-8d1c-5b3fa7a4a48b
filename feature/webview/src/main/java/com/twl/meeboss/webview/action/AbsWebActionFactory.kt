package com.twl.meeboss.webview.action

import com.twl.meeboss.webview.WebViewCommon
import org.json.JSONObject

abstract class AbsWebActionFactory {
    private val mWebActionMaps = mutableMapOf<String, IWebAction>()
    private var mWebViewCommon:WebViewCommon? = null

    fun setWebViewCommon(webViewCommon: WebViewCommon):AbsWebActionFactory {
        this.mWebViewCommon = webViewCommon
        return this
    }

    protected fun getWebViewCommon():WebViewCommon? {
        return mWebViewCommon
    }


    abstract fun createWebAction(action: String): IWebAction?

    fun handleActionFromWeb(action: String, params: JSONObject?) {
        mWebActionMaps.getOrPut(action) {
            createWebAction(action) ?: return
        }.handleActionFromWeb(action, params)
    }

}

fun createWebActionFactory(webViewCommon: WebViewCommon?):AbsWebActionFactory {
    return object : AbsWebActionFactory() {
        override fun createWebAction(action: String): IWebAction? {
            webViewCommon?.let {webViewCommon->
                when(action) {
                    WebActionConstant.GET_CURRENT_LANGUAGE_ACTION -> {
                        return WebGetCurrentLanguageAction(webViewCommon)
                    }
                    WebActionConstant.INTERCEPT_CLOSE_EVENT_ACTION -> {
                        return WebInterceptCloseEventAction(webViewCommon)
                    }
                    WebActionConstant.APP_DELETE_ACCOUNT -> {
                        return WebAppDeleteAccountAction(webViewCommon)
                    }
                    WebActionConstant.APP_PAY -> {
                        return WebAppPayAction(webViewCommon)
                    }
                    WebActionConstant.HIDDEN_NAVIGATION_BAR -> {
                        return WebHiddenNavigationBarAction(webViewCommon)
                    }
                    WebActionConstant.CLOSE_VIEW -> {
                        return WebCloseViewAction(webViewCommon)
                    }
                    WebActionConstant.GET_REGION -> {
                        return WebGetRegionAction(webViewCommon)
                    }
                    WebActionConstant.CHOOSE_REGION -> {
                        return WebChooseRegionAction(webViewCommon)
                    }
                    WebActionConstant.SET_NAVIGATION_BAR_TITLE -> {
                        return WebSetNavigationBarTitleAction(webViewCommon)
                    }
                    WebActionConstant.H5_EXECUTE_REQUEST -> {
                        return WebH5ExecuteRequestAction(webViewCommon)
                    }
                    WebActionConstant.H5_GET_IDENTITY -> {
                        return WebGetIdentityAction(webViewCommon)
                    }
                    else ->{
                    }
                }
            }
            return null
        }
    }
}