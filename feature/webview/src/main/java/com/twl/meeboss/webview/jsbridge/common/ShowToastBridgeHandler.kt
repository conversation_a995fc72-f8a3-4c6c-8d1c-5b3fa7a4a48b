package com.twl.meeboss.webview.jsbridge.common

import com.hpbr.directhires.module.localhtml.jsbridge.AbsBridgeHandler
import com.hpbr.directhires.module.localhtml.jsbridge.CallBackFunction
import com.twl.meeboss.common.utils.T

class ShowToastBridgeHandler : AbsBridgeHandler() {

    override fun getInvokeMethodName(): String {
        return "showToast"
    }

    override fun handler(params: Map<String, String>, function: CallBackFunction) {
        val text = params["text"]
        val type = params["type"]?.toInt() ?: 0
        T.ss(text, type)
    }
}