package com.twl.meeboss.webview.router

import androidx.fragment.app.FragmentActivity
import com.sankuai.waimai.router.annotation.RouterService
import com.twl.meeboss.base.main.router.BaseRouterPath
import com.twl.meeboss.base.main.router.IBaseWebViewService
import com.twl.meeboss.safety.robotcheck.showRobotCheckDialog

@RouterService(
    interfaces = [IBaseWebViewService::class],
    key = [BaseRouterPath.BASE_WEBVIEW_SERVICE],
    singleton = true
)
class BaseWebViewServiceImpl : IBaseWebViewService {
    override fun showRobotCheckDialog(
        context: FragmentActivity,
        oldToken: String?,
        callback: (String) -> Unit
    ) {
        context.showRobotCheckDialog(oldToken, callback)
    }
}